# ArchScope - 架构鹰眼

ArchScope是一个面向开发者的架构观测和守护系统，旨在通过自动化分析设计文档和代码仓库，为开发者提供项目的全局视角，帮助开发者快速理解和治理项目。

## 项目特点

- 基于LLM+提示词的代码仓库解析
- 自动生成项目架构文档
- 版本变更感知和对比
- 项目健康度评估和星级评定
- 文档网站自动生成和托管

## 技术栈

- 前端：TypeScript/Vue 3.x/Tailwind CSS
- 后端：Java/Spring Boot/DDD架构
- 数据库：MySQL 8.0+
- 缓存：Redis
- 消息队列：RocketMQ

## 项目结构

### 整体结构

本项目采用领域驱动设计(DDD)的六边形架构（[后端DDD约定详见 README-DDD.md](/README-DDD.md)），分为以下几个模块：

```
/
├── docs/
│   └── prototype/              # 界面原型
├── arch-scope-app/             # 应用服务层
├── arch-scope-domain/          # 领域模型层
├── arch-scope-facade/          # 接口层
├── arch-scope-infrastructure/  # 基础设施层
├── arch-scope-main/            # 主应用模块
├── arch-scope-frontend/        # 前端应用
└── pom.xml               # 父级POM
```

### 前端项目结构

```
/arch-scope-frontend/
├── .github/workflows/
│   └── e2e-tests.yml                  # CI/CD工作流
├── public/             # 静态资源
├── cypress/
│   ├── e2e/
│   │   ├── task-management.cy.ts      # 任务列表页面测试
│   │   ├── task-detail.cy.ts          # 任务详情页面测试
│   │   └── task-workflow.cy.ts        # 完整工作流程测试
│   ├── support/
│   │   ├── e2e.ts                     # 全局配置
│   │   └── commands.ts                # 自定义命令
│   ├── fixtures/
│   │   ├── tasks.json                 # 测试数据
│   │   ├── task-processing.json       # 处理中任务数据
│   │   ├── task-completed.json        # 已完成任务数据
│   │   └── task-failed.json           # 失败任务数据
│   └── README.md                      # 测试文档
├── src/
│   ├── assets/         # 静态文件 (图片、字体等)
│   ├── components/     # 可复用组件
│   ├── layouts/        # 页面布局组件
│   ├── router/         # Vue Router 配置
│   ├── stores/         # Pinia 状态管理
│   ├── views/          # 页面组件
│   │   ├── projects/   # 项目相关页面
│   │   ├── tasks/      # 任务相关页面
│   │   └── ...
│   ├── App.vue         # 根组件
│   ├── main.ts         # 入口文件
│   └── styles/         # 全局样式
├── cypress.config.ts                  # Cypress配置
├── run-e2e-tests.sh                   # 测试运行脚本
├── E2E_TESTING_SUMMARY.md             # 实现总结
├── .gitignore
├── package.json
├── tailwind.config.js  # Tailwind CSS 配置文件
├── tsconfig.json       # TypeScript 配置文件
└── vite.config.ts      # Vite 配置文件
```

## 快速开始

1. 克隆仓库
   ```
   git clone https://github.com/your-username/arch-scope.git
   cd arch-scope
   ```

2. 设置OpenAI API密钥
   ```
   export OPENAI_API_KEY=your-api-key
   ```

3. 构建项目
   ```
   mvn clean package
   ```

4. 运行项目
   ```
   java -jar target/arch-scope-0.0.1-SNAPSHOT.jar
   ```

5. 访问API
   ```
   curl http://localhost:8080/api/prompts
   ```

## 测试验收

## 交互式运行（推荐）

```shell
npm run test:e2e:dev:open
```

## 无头模式运行

```shell
npm run test:e2e:dev
```

## 使用脚本运行

```shell
./run-e2e-tests.sh
./run-e2e-tests.sh --open
```

## 贡献指南

欢迎贡献代码和提示词！请遵循以下步骤：

1. Fork仓库
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。
