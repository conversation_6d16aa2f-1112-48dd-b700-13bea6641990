package com.archscope.app.controller;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.domain.service.MarkdownService;
import com.archscope.domain.valueobject.DocumentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import java.util.List;
import java.util.Optional;

/**
 * 文档版本控制器
 * 提供文档版本的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/document-versions")
@RequiredArgsConstructor
public class DocumentVersionController {

    private final DocumentVersionService documentVersionService;
    private final MarkdownService markdownService;

    /**
     * 获取项目的所有文档版本
     *
     * @param projectId 项目ID
     * @return 文档版本列表
     */
    @GetMapping("/project/{projectId}")
    public ResponseEntity<List<DocumentVersion>> getDocumentVersionsByProjectId(@PathVariable Long projectId) {
        log.info("获取项目的所有文档版本，项目ID: {}", projectId);
        List<DocumentVersion> versions = documentVersionService.getDocumentVersionsByProjectId(projectId);
        return ResponseEntity.ok(versions);
    }

    /**
     * 获取项目指定类型的所有文档版本
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 文档版本列表
     */
    @GetMapping("/project/{projectId}/type/{docType}")
    public ResponseEntity<List<DocumentVersion>> getDocumentVersionsByProjectIdAndType(
            @PathVariable Long projectId,
            @PathVariable DocumentType docType) {
        log.info("获取项目指定类型的所有文档版本，项目ID: {}, 文档类型: {}", projectId, docType);
        List<DocumentVersion> versions = documentVersionService.getDocumentVersionsByProjectIdAndType(projectId, docType);
        return ResponseEntity.ok(versions);
    }

    /**
     * 获取项目指定提交的所有文档版本
     *
     * @param projectId 项目ID
     * @param commitId 提交ID
     * @return 文档版本列表
     */
    @GetMapping("/project/{projectId}/commit/{commitId}")
    public ResponseEntity<List<DocumentVersion>> getDocumentVersionsByProjectIdAndCommitId(
            @PathVariable Long projectId,
            @PathVariable String commitId) {
        log.info("获取项目指定提交的所有文档版本，项目ID: {}, 提交ID: {}", projectId, commitId);
        List<DocumentVersion> versions = documentVersionService.getDocumentVersionsByProjectIdAndCommitId(projectId, commitId);
        return ResponseEntity.ok(versions);
    }

    /**
     * 获取项目指定版本标签的文档版本
     *
     * @param projectId 项目ID
     * @param versionTag 版本标签
     * @return 文档版本
     */
    @GetMapping("/project/{projectId}/tag/{versionTag}")
    public ResponseEntity<DocumentVersion> getDocumentVersionByProjectIdAndVersionTag(
            @PathVariable Long projectId,
            @PathVariable String versionTag) {
        log.info("获取项目指定版本标签的文档版本，项目ID: {}, 版本标签: {}", projectId, versionTag);
        Optional<DocumentVersion> versionOpt = documentVersionService.getDocumentVersionByProjectIdAndVersionTag(projectId, versionTag);
        return versionOpt.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 获取项目指定类型的最新文档版本
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 最新文档版本
     */
    @GetMapping("/project/{projectId}/type/{docType}/latest")
    public ResponseEntity<DocumentVersion> getLatestDocumentVersionByProjectIdAndType(
            @PathVariable Long projectId,
            @PathVariable DocumentType docType) {
        log.info("获取项目指定类型的最新文档版本，项目ID: {}, 文档类型: {}", projectId, docType);
        Optional<DocumentVersion> versionOpt = documentVersionService.getLatestDocumentVersionByProjectIdAndType(projectId, docType);
        return versionOpt.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 获取文档版本详情
     *
     * @param id 文档版本ID
     * @return 文档版本
     */
    @GetMapping("/{id}")
    public ResponseEntity<DocumentVersion> getDocumentVersionById(@PathVariable Long id) {
        log.info("获取文档版本详情，ID: {}", id);
        Optional<DocumentVersion> versionOpt = documentVersionService.getDocumentVersionById(id);
        return versionOpt.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 创建文档版本
     *
     * @param documentVersion 文档版本
     * @return 创建后的文档版本
     */
    @PostMapping
    public ResponseEntity<DocumentVersion> createDocumentVersion(@RequestBody DocumentVersion documentVersion) {
        log.info("创建文档版本，项目ID: {}, 文档类型: {}", documentVersion.getProjectId(), documentVersion.getDocType());
        DocumentVersion createdVersion = documentVersionService.createDocumentVersion(documentVersion);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdVersion);
    }

    /**
     * 更新文档版本
     *
     * @param id 文档版本ID
     * @param documentVersion 文档版本
     * @return 更新后的文档版本
     */
    @PutMapping("/{id}")
    public ResponseEntity<DocumentVersion> updateDocumentVersion(
            @PathVariable Long id,
            @RequestBody DocumentVersion documentVersion) {
        log.info("更新文档版本，ID: {}", id);

        // 确保ID一致
        documentVersion.setId(id);

        DocumentVersion updatedVersion = documentVersionService.updateDocumentVersion(documentVersion);
        return ResponseEntity.ok(updatedVersion);
    }

    /**
     * 删除文档版本
     *
     * @param id 文档版本ID
     * @return 无内容响应
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDocumentVersion(@PathVariable Long id) {
        log.info("删除文档版本，ID: {}", id);
        documentVersionService.deleteDocumentVersion(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * 发布文档版本
     *
     * @param id 文档版本ID
     * @return 发布后的文档版本
     */
    @PostMapping("/{id}/publish")
    public ResponseEntity<DocumentVersion> publishDocumentVersion(@PathVariable Long id) {
        log.info("发布文档版本，ID: {}", id);
        DocumentVersion publishedVersion = documentVersionService.publishDocumentVersion(id);
        return ResponseEntity.ok(publishedVersion);
    }

    /**
     * 取消发布文档版本
     *
     * @param id 文档版本ID
     * @return 取消发布后的文档版本
     */
    @PostMapping("/{id}/unpublish")
    public ResponseEntity<DocumentVersion> unpublishDocumentVersion(@PathVariable Long id) {
        log.info("取消发布文档版本，ID: {}", id);
        DocumentVersion unpublishedVersion = documentVersionService.unpublishDocumentVersion(id);
        return ResponseEntity.ok(unpublishedVersion);
    }

    /**
     * 比较两个文档版本
     *
     * @param versionId1 文档版本1的ID
     * @param versionId2 文档版本2的ID
     * @return 比较结果
     */
    @GetMapping("/compare")
    public ResponseEntity<String> compareDocumentVersions(
            @RequestParam Long versionId1,
            @RequestParam Long versionId2) {
        log.info("比较两个文档版本，版本ID1: {}, 版本ID2: {}", versionId1, versionId2);
        String compareResult = documentVersionService.compareDocumentVersions(versionId1, versionId2);
        return ResponseEntity.ok(compareResult);
    }

    /**
     * 获取文档内容
     *
     * @param id 文档版本ID
     * @param docType 文档类型（可选）
     * @return 文档内容
     */
    @GetMapping("/{id}/content")
    public ResponseEntity<Map<String, Object>> getDocumentContent(
            @PathVariable Long id,
            @RequestParam(required = false) DocumentType docType) {
        log.info("获取文档内容，文档版本ID: {}, 文档类型: {}", id, docType);

        // 获取文档版本
        Optional<DocumentVersion> versionOpt = documentVersionService.getDocumentVersionById(id);
        if (versionOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        DocumentVersion version = versionOpt.get();

        // 如果指定了文档类型，检查是否匹配
        if (docType != null && !version.getDocType().equals(docType)) {
            log.warn("文档类型不匹配，请求类型: {}, 实际类型: {}", docType, version.getDocType());
            return ResponseEntity.badRequest().build();
        }

        try {
            // 读取文档内容
            String contentPath = version.getContentPath();
            if (contentPath == null || contentPath.isEmpty()) {
                log.warn("文档内容路径为空: {}", id);
                return ResponseEntity.noContent().build();
            }

            Path contentFilePath = Paths.get(contentPath);
            if (!Files.exists(contentFilePath)) {
                log.warn("文档内容文件不存在: {}", contentPath);
                return ResponseEntity.noContent().build();
            }

            // 读取文件内容
            String markdown = Files.readString(contentFilePath);

            // 转换为HTML
            Map<String, String> additionalHeadElements = new HashMap<>();
            String html = markdownService.convertToHtml(markdown, additionalHeadElements);

            // 提取标题
            String title = markdownService.extractTitle(markdown);

            // 创建响应对象
            Map<String, Object> response = new HashMap<>();
            response.put("id", version.getId());
            response.put("projectId", version.getProjectId());
            response.put("versionTag", version.getVersionTag());
            response.put("docType", version.getDocType());
            response.put("title", title);
            response.put("html", html);
            response.put("headElements", additionalHeadElements);
            response.put("timestamp", version.getTimestamp());
            response.put("lastModified", version.getLastModified());

            return ResponseEntity.ok(response);
        } catch (IOException e) {
            log.error("读取文档内容失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
