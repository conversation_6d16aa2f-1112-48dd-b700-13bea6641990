package com.archscope.app.controller;

import com.archscope.app.service.ProjectAppService;
import com.archscope.facade.dto.PageResponseDTO;
import com.archscope.facade.dto.ProjectDTO;
import com.archscope.facade.dto.ProjectRegistrationDTO;
import com.archscope.facade.dto.ProjectSummaryDTO;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 项目控制器，处理项目注册、查询等请求
 */
@RestController
@RequestMapping("/api/projects")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectAppService projectAppService;

    /**
     * 注册新项目
     * @param registrationDTO 项目注册信息
     * @return 注册结果
     */
    @Operation(summary = "注册新项目", description = "注册新项目并提供Git仓库地址")
    @PostMapping
    public ResponseEntity<ProjectDTO> registerProject(@Valid @RequestBody ProjectRegistrationDTO registrationDTO) {
        ProjectDTO projectDTO = projectAppService.registerProject(registrationDTO);
        return ResponseEntity.ok(projectDTO);
    }

    /**
     * 获取项目详情
     * @param id 项目ID
     * @return 项目详情
     */
    @Operation(summary = "获取项目详情", description = "根据项目ID获取项目详情")
    @GetMapping("/{id}")
    public ResponseEntity<ProjectDTO> getProject(@PathVariable Long id) {
        ProjectDTO projectDTO = projectAppService.getProjectById(id);
        return ResponseEntity.ok(projectDTO);
    }

    /**
     * 更新项目信息
     * @param id 项目ID
     * @param projectDTO 项目信息
     * @return 更新后的项目信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<ProjectDTO> updateProject(@PathVariable Long id, @Valid @RequestBody ProjectDTO projectDTO) {
        ProjectDTO updatedProject = projectAppService.updateProject(id, projectDTO);
        return ResponseEntity.ok(updatedProject);
    }

    /**
     * 删除项目
     * @param id 项目ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProject(@PathVariable Long id) {
        projectAppService.deleteProject(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 分页查询项目
     * @param page 页码
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向
     * @return 分页项目列表
     */
    @Operation(summary = "获取项目列表", description = "获取当前用户的所有项目")
    @GetMapping
    public ResponseEntity<PageResponseDTO<ProjectSummaryDTO>> getProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction) {

        PageResponseDTO<ProjectSummaryDTO> projects = projectAppService.getProjects(page, size, sortBy, direction);
        return ResponseEntity.ok(projects);
    }

    /**
     * 搜索项目
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<PageResponseDTO<ProjectSummaryDTO>> searchProjects(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        PageResponseDTO<ProjectSummaryDTO> projects = projectAppService.searchProjects(keyword, page, size);
        return ResponseEntity.ok(projects);
    }

    /**
     * 获取当前用户的项目
     * @return 项目列表
     */
    @GetMapping("/my")
    public ResponseEntity<List<ProjectSummaryDTO>> getCurrentUserProjects() {
        List<ProjectSummaryDTO> projects = projectAppService.getCurrentUserProjects();
        return ResponseEntity.ok(projects);
    }

    /**
     * 获取最近更新的项目
     * @param limit 限制数量
     * @return 项目列表
     */
    @GetMapping("/recent")
    public ResponseEntity<List<ProjectSummaryDTO>> getRecentProjects(
            @RequestParam(defaultValue = "5") int limit) {

        List<ProjectSummaryDTO> projects = projectAppService.getRecentProjects(limit);
        return ResponseEntity.ok(projects);
    }

    /**
     * 获取星级最高的项目
     * @param limit 限制数量
     * @return 项目列表
     */
    @GetMapping("/top-rated")
    public ResponseEntity<List<ProjectSummaryDTO>> getTopRatedProjects(
            @RequestParam(defaultValue = "5") int limit) {

        List<ProjectSummaryDTO> projects = projectAppService.getTopRatedProjects(limit);
        return ResponseEntity.ok(projects);
    }

    /**
     * 检查仓库URL是否已存在
     * @param url 仓库URL
     * @return 是否存在
     */
    @GetMapping("/check-repository")
    public ResponseEntity<Boolean> checkRepositoryExists(@RequestParam String url) {
        boolean exists = projectAppService.isRepositoryUrlExists(url);
        return ResponseEntity.ok(exists);
    }

    /**
     * 将用户添加到项目
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/{projectId}/members/{userId}")
    public ResponseEntity<Boolean> addUserToProject(
            @PathVariable Long projectId,
            @PathVariable Long userId) {

        boolean result = projectAppService.addUserToProject(projectId, userId);
        return ResponseEntity.ok(result);
    }

    /**
     * 从项目移除用户
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{projectId}/members/{userId}")
    public ResponseEntity<Boolean> removeUserFromProject(
            @PathVariable Long projectId,
            @PathVariable Long userId) {

        boolean result = projectAppService.removeUserFromProject(projectId, userId);
        return ResponseEntity.ok(result);
    }
}