package com.archscope.app.controller;

import com.archscope.domain.service.StaticSiteService;
import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.GenerateSiteRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.nio.file.Path;
import java.util.List;

/**
 * 静态站点控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/static-site")
@RequiredArgsConstructor
public class StaticSiteController {

    private final StaticSiteService staticSiteService;
    private final DocumentVersionService documentVersionService;

    /**
     * 生成项目的静态站点
     *
     * @param projectId 项目ID
     * @return 生成结果
     */
    @PostMapping("/projects/{projectId}")
    public ResponseEntity<ApiResponse<String>> generateProjectSite(@PathVariable Long projectId) {
        log.info("接收到生成项目静态站点请求，项目ID: {}", projectId);
        
        try {
            // 获取项目的所有已发布文档版本
            List<DocumentVersion> documentVersions = documentVersionService.findPublishedByProjectId(projectId);
            
            if (documentVersions.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("项目没有已发布的文档版本"));
            }
            
            // 生成静态站点
            Path sitePath = staticSiteService.generateProjectSite(projectId, documentVersions);
            
            return ResponseEntity.ok(ApiResponse.success("静态站点生成成功", sitePath.toString()));
        } catch (Exception e) {
            log.error("生成项目静态站点失败", e);
            return ResponseEntity.ok(ApiResponse.error("生成项目静态站点失败: " + e.getMessage()));
        }
    }

    /**
     * 生成自定义静态站点
     *
     * @param request 请求参数
     * @return 生成结果
     */
    @PostMapping("/custom")
    public ResponseEntity<ApiResponse<String>> generateCustomSite(@Valid @RequestBody GenerateSiteRequest request) {
        log.info("接收到生成自定义静态站点请求，文档版本IDs: {}", request.getDocumentVersionIds());
        
        try {
            // 获取指定的文档版本
            List<DocumentVersion> documentVersions = documentVersionService.findByIds(request.getDocumentVersionIds());
            
            if (documentVersions.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("未找到指定的文档版本"));
            }
            
            // 生成静态站点
            Path sitePath = staticSiteService.generateCustomSite(documentVersions, request.getOutputPath());
            
            return ResponseEntity.ok(ApiResponse.success("静态站点生成成功", sitePath.toString()));
        } catch (Exception e) {
            log.error("生成自定义静态站点失败", e);
            return ResponseEntity.ok(ApiResponse.error("生成自定义静态站点失败: " + e.getMessage()));
        }
    }
}
