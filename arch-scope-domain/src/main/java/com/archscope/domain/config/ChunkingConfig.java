package com.archscope.domain.config;

import com.archscope.domain.service.CodeChunkingService;
import com.archscope.domain.service.impl.CodeChunkingServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 代码分块服务配置类
 */
@Configuration
public class ChunkingConfig {
    
    /**
     * 注册代码分块服务
     * 
     * @return 代码分块服务实例
     */
    @Bean
    public CodeChunkingService codeChunkingService() {
        return new CodeChunkingServiceImpl();
    }
}
