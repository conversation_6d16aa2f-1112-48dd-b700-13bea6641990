package com.archscope.domain.entity;

import com.archscope.domain.valueobject.ResourceUsage;
import com.archscope.domain.valueobject.TaskStatus;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Task {
    private Long id;
    private Long projectId;              // 关联的项目ID
    private String type;                 // 任务类型
    private TaskStatus status;           // 使用枚举替代字符串
    private Integer priority;             // 优先级
    private LocalDateTime createdAt;     // 创建时间
    private LocalDateTime updatedAt;     // 更新时间

    // 根据原型扩展的字段
    private String detailedStatus;       // 详细状态（用于自定义状态描述）
    private Integer progress;  // 进度百分比（0-100）
    private String errorLog;             // 错误日志
    private Integer retryCount;          // 重试次数
    private String taskType;             // 任务类型（代码解析任务、文档生成任务等）
    private Long executionTime;          // 执行时间（毫秒）
    private ResourceUsage resourceUsage; // 资源使用情况，使用值对象替代Map
    private String name;                 // 任务名称
    private String description;          // 任务描述
    private Long userId;                 // 创建用户ID
    private Long assigneeId;             // 分配用户ID
    private String result;               // 任务结果（成功时的输出）
    private Map<String, Object> parameters;  // 任务参数
}