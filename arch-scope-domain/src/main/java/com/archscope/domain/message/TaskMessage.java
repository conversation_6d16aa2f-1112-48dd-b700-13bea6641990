package com.archscope.domain.message;

import com.archscope.domain.valueobject.TaskType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务消息
 * 用于在消息队列中传递的任务消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskMessage implements Serializable {
    
    @Serial
    private static final long serialVersionUID = -1L;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 任务类型
     */
    private TaskType taskType;
    
    /**
     * 任务优先级
     */
    private Integer priority;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
} 