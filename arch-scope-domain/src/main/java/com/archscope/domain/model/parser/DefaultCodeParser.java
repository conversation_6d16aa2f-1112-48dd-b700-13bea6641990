package com.archscope.domain.model.parser;

import com.archscope.domain.external.llm.LlmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;

/**
 * 代码解析器默认实现
 */
@Slf4j
@Component
@RequiredArgsConstructor // Constructor will change
public class DefaultCodeParser implements CodeParser {

    private final LlmService llmService; // Keep LLM Service
    // Remove TraditionalParserRegistry and ParseResultMerger fields
    // private final TraditionalParserRegistry traditionalParserRegistry;
    // private final ParseResultMerger parseResultMerger;
    
    // Remove mergeStrategyStr field
    // @Value("${archscope.parser.merge-strategy:SMART_MERGE}")
    // private String mergeStrategyStr;

    // 包声明的正则表达式
    private static final Pattern PACKAGE_PATTERN = Pattern.compile("package\\s+([\\w.]+)\\s*;");

    // 导入声明的正则表达式
    private static final Pattern IMPORT_PATTERN = Pattern.compile("import\\s+([\\w.]+)\\s*;");

    // Java类定义的正则表达式
    private static final Pattern CLASS_PATTERN = Pattern.compile("(public|private|protected)?\\s*(abstract|final)?\\s*class\\s+(\\w+)\\s*(extends\\s+([\\w.]+))?\\s*(implements\\s+([\\w.,\\s]+))?\\s*\\{");

    // 字段定义的正则表达式
    private static final Pattern FIELD_PATTERN = Pattern.compile("(private|public|protected)?\\s*(static|final)?\\s*(\\w+)\\s+(\\w+)\\s*;");

    @Override
    public FileParseResult parseFile(String filename, String content) {
        try {
            LanguageType languageType = identifyLanguage(filename);
            
            // Simplify logic: Use LLM if supported, otherwise Regex
            boolean supportsLlmParsing = (languageType == LanguageType.JAVA ||
                                         languageType == LanguageType.JAVASCRIPT ||
                                         languageType == LanguageType.TYPESCRIPT);

            if (supportsLlmParsing) {
                log.info("使用LLM解析，文件: {}", filename);
                FileParseResult llmResult = llmService.parseCodeWithLlm(filename, content, languageType.name());
                
                // Fallback to Regex if LLM fails
                if (llmResult != null && llmResult.isSuccessful()) {
                    return llmResult;
                } else {
                    log.warn("LLM解析失败或不支持，回退到正则表达式解析，文件: {}", filename);
                    return parseFileWithRegex(filename, content, languageType);
                }
            } else {
                // Use Regex for unsupported languages
                log.info("语言不支持LLM，使用正则表达式解析，文件: {}", filename);
                return parseFileWithRegex(filename, content, languageType);
            }
        } catch (Exception e) {
            log.error("解析文件时出错: {}", filename, e);
            return FileParseResult.builder()
                    .filename(filename)
                    .filePath(filename) // Assuming filename is also the path for now
                    .languageType(identifyLanguage(filename))
                    .successful(false)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 使用正则表达式解析文件
     */
    private FileParseResult parseFileWithRegex(String filename, String content, LanguageType languageType) {
        FileParseResult.FileParseResultBuilder resultBuilder = FileParseResult.builder()
                .filename(filename)
                .filePath(filename) // Assuming filename is also the path for now
                .languageType(languageType)
                .successful(true);

        // 根据语言类型选择不同的解析策略
        switch (languageType) {
            case JAVA:
                return parseJavaFile(resultBuilder, content);
            case JAVASCRIPT:
            case TYPESCRIPT:
                return parseJsOrTsFile(resultBuilder, content, languageType);
            default:
                // 对于其他或未知类型，返回基本信息
                return resultBuilder.build();
        }
    }


    @Override
    public FileParseResult parseFile(File file) throws IOException {
        String content = Files.readString(file.toPath());
        return parseFile(file.getName(), content);
    }

    @Override
    public List<FileParseResult> parseDirectory(File directory) throws IOException {
        if (!directory.isDirectory()) {
            throw new IllegalArgumentException(directory.getPath() + " 不是一个有效的目录");
        }

        List<FileParseResult> results = new ArrayList<>();

        Files.walk(directory.toPath())
                .filter(Files::isRegularFile)
                .forEach(path -> {
                    try {
                        results.add(parseFile(path.toFile()));
                    } catch (IOException e) {
                        log.error("解析文件失败: {}", path, e);
                    }
                });

        return results;
    }

    @Override
    public List<FileParseResult> parseDirectory(String directoryPath) throws IOException {
        return parseDirectory(new File(directoryPath));
    }
    
    /**
     * 解析指定的文件列表
     * 
     * @param files 要解析的文件列表
     * @return 解析结果列表
     * @throws IOException 如果文件读取失败
     */
    public List<FileParseResult> parseFiles(List<File> files) throws IOException {
        log.info("解析 {} 个文件", files.size());
        
        List<FileParseResult> results = new ArrayList<>();
        
        for (File file : files) {
            if (file.isFile()) {
                try {
                    FileParseResult result = parseFile(file);
                    results.add(result);
                    log.debug("成功解析文件: {}", file.getPath());
                } catch (IOException e) {
                    log.error("解析文件失败: {}", file.getPath(), e);
                    // 创建一个失败的解析结果
                    FileParseResult failedResult = FileParseResult.builder()
                            .filename(file.getName())
                            .filePath(file.getPath())
                            .languageType(identifyLanguage(file.getName()))
                            .successful(false)
                            .errorMessage("解析失败: " + e.getMessage())
                            .build();
                    results.add(failedResult);
                }
            } else {
                log.warn("跳过非文件: {}", file.getPath());
            }
        }
        
        return results;
    }
    
    /**
     * 合并解析结果
     * 
     * @param existingResults 现有解析结果
     * @param newResults 新的解析结果
     * @return 合并后的解析结果
     */
    public List<FileParseResult> mergeParseResults(List<FileParseResult> existingResults, List<FileParseResult> newResults) {
        log.debug("合并解析结果: 现有结果 {} 个, 新结果 {} 个", existingResults.size(), newResults.size());
        
        // 创建一个新的结果列表，初始包含所有现有结果
        List<FileParseResult> mergedResults = new ArrayList<>(existingResults);
        
        // 创建一个映射，用于快速查找现有结果中的文件
        Map<String, Integer> filePathToIndex = new HashMap<>();
        for (int i = 0; i < existingResults.size(); i++) {
            filePathToIndex.put(existingResults.get(i).getFilePath(), i);
        }
        
        // 处理新的解析结果
        for (FileParseResult newResult : newResults) {
            String filePath = newResult.getFilePath();
            
            // 检查文件是否已存在于现有结果中
            if (filePathToIndex.containsKey(filePath)) {
                // 替换现有结果
                int index = filePathToIndex.get(filePath);
                mergedResults.set(index, newResult);
                log.debug("替换文件 {} 的解析结果", filePath);
            } else {
                // 添加新结果
                mergedResults.add(newResult);
                log.debug("添加文件 {} 的新解析结果", filePath);
            }
        }
        
        log.debug("Starting dependency analysis in merge...");
        long analyzeDepsStartTime = System.nanoTime();
        List<DependencyRelation> mergedDependencies = analyzeDependencies(mergedResults);
        long analyzeDepsEndTime = System.nanoTime();
        log.info("Time taken for analyzeDependencies in merge: {} ms", TimeUnit.NANOSECONDS.toMillis(analyzeDepsEndTime - analyzeDepsStartTime));

        log.debug("Starting dependency distribution in merge...");
        long distributeDepsStartTime = System.nanoTime();
        // 更新每个解析结果中的依赖关系
        for (FileParseResult result : mergedResults) {
            // 过滤出与当前文件相关的依赖关系
            List<DependencyRelation> fileDependencies = mergedDependencies.stream()
                    .filter(dep -> {
                        if (dep == null || dep.getSourceClass() == null || result.getFilename() == null) return false; // Defensive null checks
                        // 尝试更精确匹配：源类的全限定名是否以文件名（不含扩展名）结尾，并且包名匹配
                        // 或者，源类是否是该文件定义的类之一
                        String resultFileNameWithoutExtension = result.getFilename().contains(".") ? result.getFilename().substring(0, result.getFilename().lastIndexOf('.')) : result.getFilename();
                        boolean fqnEndsWithFileName = dep.getSourceClass().endsWith("." + resultFileNameWithoutExtension) || dep.getSourceClass().equals(resultFileNameWithoutExtension);
                        boolean packageMatchesOrSourceHasNoPackage = (result.getPackageName() == null && !dep.getSourceClass().contains(".")) || 
                                                                   (result.getPackageName() != null && dep.getSourceClass().startsWith(result.getPackageName() + "."));
                        
                        boolean matchesSourceInFile = fqnEndsWithFileName && packageMatchesOrSourceHasNoPackage;
                                  
                        boolean isDefinedInFile = result.getClassDefinitions() != null && result.getClassDefinitions().stream()
                                          .anyMatch(cls -> cls != null && cls.getFullyQualifiedName() != null && dep.getSourceClass().equals(cls.getFullyQualifiedName()));
                        return matchesSourceInFile || isDefinedInFile;
                    })
                    .collect(Collectors.toList());
            
            // 更新依赖关系
            result.setDependencies(fileDependencies);
        }
        long distributeDepsEndTime = System.nanoTime();
        log.info("Time taken for dependency distribution in merge: {} ms", TimeUnit.NANOSECONDS.toMillis(distributeDepsEndTime - distributeDepsStartTime));
        
        return mergedResults;
    }

    @Override
    public LanguageType identifyLanguage(String filename) {
        return LanguageType.fromFilename(filename);
    }

    @Override
    public List<DependencyRelation> analyzeDependencies(List<FileParseResult> parseResults) {
        List<DependencyRelation> allDependencies = new ArrayList<>();

        // 首先收集所有类定义，以便后续查找依赖关系
        for (FileParseResult result : parseResults) {
            if (result == null) continue; // Skip null results
            
            // 将每个文件中的依赖关系添加到总列表中 (Add null check for getDependencies)
            if (result.getDependencies() != null) {
                allDependencies.addAll(result.getDependencies());
            }

            // 创建基于导入声明的依赖关系 (Add null check for getClassDefinitions)
            if (result.getClassDefinitions() == null) continue; 
            
            for (ClassDefinition classDef : result.getClassDefinitions()) {
                if (classDef == null) continue; // Skip null class definitions
                
                String sourceClass = classDef.getFullyQualifiedName();
                if (sourceClass == null) continue; // Skip if fully qualified name is null

                // 分析导入 (Add null check for getImports)
                if (result.getImports() != null) {
                    for (String importClass : result.getImports()) {
                        if (importClass == null) continue; // Skip null imports
                        DependencyRelation dependency = DependencyRelation.builder()
                                .sourceClass(sourceClass)
                                .targetClass(importClass)
                                .type(DependencyType.IMPORT)
                                .strength(1) // 导入的依赖强度较低
                                .location("import")
                                .build();
                        allDependencies.add(dependency);
                    }
                }

                // 分析继承关系
                if (classDef.getSuperClass() != null && !classDef.getSuperClass().isEmpty()) {
                    DependencyRelation dependency = DependencyRelation.builder()
                            .sourceClass(sourceClass)
                            .targetClass(classDef.getSuperClass())
                            .type(DependencyType.INHERITANCE)
                            .strength(10) // 继承是强依赖
                            .location("class definition")
                            .build();
                    allDependencies.add(dependency);
                }

                // 分析接口实现 (Add null check for getInterfaces)
                if (classDef.getInterfaces() != null) {
                    for (String interfaceName : classDef.getInterfaces()) {
                        if (interfaceName == null) continue; // Skip null interfaces
                        DependencyRelation dependency = DependencyRelation.builder()
                                .sourceClass(sourceClass)
                                .targetClass(interfaceName)
                                .type(DependencyType.IMPLEMENTATION)
                                .strength(8) // 接口实现是较强依赖
                                .location("class definition")
                                .build();
                        allDependencies.add(dependency);
                    }
                }
            }
        }

        return allDependencies;
    }

    /**
     * 解析Java文件
     */
    private FileParseResult parseJavaFile(FileParseResult.FileParseResultBuilder resultBuilder, String content) {
        try {
            // 解析包名
            Matcher packageMatcher = PACKAGE_PATTERN.matcher(content);
            if (packageMatcher.find()) {
                String packageName = packageMatcher.group(1);
                resultBuilder.packageName(packageName);
            }

            // 解析导入
            List<String> imports = new ArrayList<>();
            Matcher importMatcher = IMPORT_PATTERN.matcher(content);
            while (importMatcher.find()) {
                imports.add(importMatcher.group(1));
            }
            resultBuilder.imports(imports);

            // 解析类定义
            List<ClassDefinition> classDefinitions = new ArrayList<>();
            Matcher classMatcher = CLASS_PATTERN.matcher(content);
            while (classMatcher.find()) {
                String accessModifierStr = classMatcher.group(1);
                AccessModifier accessModifier = parseAccessModifier(accessModifierStr);

                String modifierStr = classMatcher.group(2);
                boolean isAbstract = modifierStr != null && modifierStr.equals("abstract");
                boolean isFinal = modifierStr != null && modifierStr.equals("final");

                String className = classMatcher.group(3);
                if (className == null) {
                    log.warn("Regex未能捕获Java类名，文件: {}", resultBuilder.build().getFilename());
                    continue;
                }

                String superClassPart = classMatcher.group(4);
                String superClass = null;
                if (superClassPart != null) {
                    String superClassGroup = classMatcher.group(5);
                    superClass = superClassGroup != null ? superClassGroup.trim() : null;
                }

                List<String> interfaces = new ArrayList<>();
                String interfacesPart = classMatcher.group(6);
                if (interfacesPart != null) {
                    String interfacesGroup = classMatcher.group(7);
                    if (interfacesGroup != null) {
                        for (String interfaceName : interfacesGroup.split(",")) {
                            interfaces.add(interfaceName.trim());
                        }
                    }
                }

                // 创建类定义
                ClassDefinition classDef = ClassDefinition.builder()
                        .name(className)
                        .type(ClassType.CLASS)
                        .packageName(resultBuilder.build().getPackageName())
                        .fullyQualifiedName(resultBuilder.build().getPackageName() + "." + className)
                        .accessModifier(accessModifier)
                        .superClass(superClass)
                        .interfaces(interfaces)
                        .isAbstract(isAbstract)
                        .isFinal(isFinal)
                        .build();

                classDefinitions.add(classDef);
            }
            resultBuilder.classDefinitions(classDefinitions);

            // 解析依赖关系
            List<DependencyRelation> dependencies = new ArrayList<>();

            // 这里使用更具体的方法来识别依赖关系
            // 例如，在测试中我们需要检测 Service 类型的字段依赖
            for (String importName : imports) {
                // 从导入的类名中提取简单类名（不含包名部分）
                String simpleClassName = importName.substring(importName.lastIndexOf('.') + 1);

                // 使用正则表达式查找该类型的字段定义
                Pattern fieldPattern = Pattern.compile("private\\s+" + simpleClassName + "\\s+(\\w+)");
                Matcher fieldMatcher = fieldPattern.matcher(content);

                while (fieldMatcher.find()) {
                    String fieldName = fieldMatcher.group(1);

                    // 为每个找到的字段创建依赖关系
                    for (ClassDefinition classDef : classDefinitions) {
                        DependencyRelation dependency = DependencyRelation.builder()
                                .sourceClass(classDef.getPackageName() + "." + classDef.getName())
                                .targetClass(importName)
                                .type(DependencyType.FIELD)
                                .strength(7) // 字段依赖的强度
                                .location("field: " + fieldName)
                                .build();
                        dependencies.add(dependency);
                    }
                }

                // 同样检查是否作为方法参数使用
                Pattern paramPattern = Pattern.compile("\\(.*?" + simpleClassName + "\\s+(\\w+).*?\\)");
                Matcher paramMatcher = paramPattern.matcher(content);

                while (paramMatcher.find()) {
                    String paramName = paramMatcher.group(1);

                    for (ClassDefinition classDef : classDefinitions) {
                        DependencyRelation dependency = DependencyRelation.builder()
                                .sourceClass(classDef.getPackageName() + "." + classDef.getName())
                                .targetClass(importName)
                                .type(DependencyType.PARAMETER)
                                .strength(5) // 参数依赖的强度
                                .location("parameter: " + paramName)
                                .build();
                        dependencies.add(dependency);
                    }
                }

                // 检查方法调用
                Pattern methodCallPattern = Pattern.compile("(\\w+)\\.(\\w+)\\s*\\(");
                Matcher methodCallMatcher = methodCallPattern.matcher(content);

                while (methodCallMatcher.find()) {
                    String objectName = methodCallMatcher.group(1);
                    String methodName = methodCallMatcher.group(2);

                    // 检查是否是我们关注的字段（在之前的private字段定义中找到的）
                    fieldMatcher = fieldPattern.matcher(content);
                    while (fieldMatcher.find()) {
                        String fieldName = fieldMatcher.group(1);
                        if (fieldName.equals(objectName)) {
                            for (ClassDefinition classDef : classDefinitions) {
                                DependencyRelation dependency = DependencyRelation.builder()
                                        .sourceClass(classDef.getPackageName() + "." + classDef.getName())
                                        .targetClass(importName)
                                        .type(DependencyType.METHOD_CALL)
                                        .strength(8) // 方法调用依赖的强度
                                        .location("method call: " + objectName + "." + methodName)
                                        .build();
                                dependencies.add(dependency);
                            }
                        }
                    }
                }
            }

            resultBuilder.dependencies(dependencies);

            return resultBuilder.build();
        } catch (Exception e) {
            log.error("Regex解析Java文件时出错: {}, 文件名: {}", e.getMessage(), resultBuilder.build().getFilename(), e);
            return resultBuilder
                    .successful(false)
                    .errorMessage("Regex parsing error: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 解析JavaScript或TypeScript文件
     */
    private FileParseResult parseJsOrTsFile(FileParseResult.FileParseResultBuilder resultBuilder, String content, LanguageType languageType) {
        try {
            // 这是一个简化的示例实现，实际应用中需要更复杂的解析逻辑

            // 对于JS/TS，解析导入语句
            List<String> imports = new ArrayList<>();
            // 为简单起见，这里仅匹配ES6导入语法
            Pattern importPattern = Pattern.compile("import\\s+.*?from\\s+['\"]([^'\"]+)['\"]");
            Matcher importMatcher = importPattern.matcher(content);
            while (importMatcher.find()) {
                imports.add(importMatcher.group(1));
            }
            resultBuilder.imports(imports);

            // 解析类定义或组件定义
            List<ClassDefinition> classDefinitions = new ArrayList<>();
            // 简单匹配ES6类定义 - Updated regex to capture complex superclass names
            Pattern classPattern = Pattern.compile("class\\s+(\\w+)\\s*(extends\\s+([\\w.]+))?");
            Matcher classMatcher = classPattern.matcher(content);
            while (classMatcher.find()) {
                String className = classMatcher.group(1);
                if (className == null) {
                    log.warn("Regex未能捕获JS/TS类名，文件: {}", resultBuilder.build().getFilename());
                    continue;
                }
                String superClass = classMatcher.group(3);

                ClassDefinition classDef = ClassDefinition.builder()
                        .name(className)
                        .type(ClassType.CLASS)
                        .superClass(superClass)
                        .build();

                classDefinitions.add(classDef);
            }
            resultBuilder.classDefinitions(classDefinitions);

            return resultBuilder.build();
        } catch (Exception e) {
            log.error("Regex解析JS/TS文件时出错: {}, 文件名: {}", e.getMessage(), resultBuilder.build().getFilename(), e);
            return resultBuilder
                    .successful(false)
                    .errorMessage("Regex parsing error: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 解析访问修饰符
     */
    private AccessModifier parseAccessModifier(String modifier) {
        if (modifier == null) {
            return AccessModifier.PACKAGE_PRIVATE;
        }

        return switch (modifier.trim()) {
            case "public" -> AccessModifier.PUBLIC;
            case "private" -> AccessModifier.PRIVATE;
            case "protected" -> AccessModifier.PROTECTED;
            default -> AccessModifier.PACKAGE_PRIVATE;
        };
    }
}
