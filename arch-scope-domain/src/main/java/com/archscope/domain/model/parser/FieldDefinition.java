package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 字段定义模型
 */
@Data
@Builder
public class FieldDefinition {
    /**
     * 字段名
     */
    private String name;
    
    /**
     * 字段类型
     */
    private String type;
    
    /**
     * 访问修饰符
     */
    private AccessModifier accessModifier;
    
    /**
     * 注解列表
     */
    @Builder.Default
    private List<String> annotations = new ArrayList<>();
    
    /**
     * 是否为静态字段
     */
    private boolean isStatic;
    
    /**
     * 是否为最终字段
     */
    private boolean isFinal;
    
    /**
     * 是否为常量（静态最终字段）
     */
    private boolean isConstant;
    
    /**
     * 字段注释
     */
    private String comment;
    
    /**
     * 初始值（如果有）
     */
    private String initialValue;

    public void setModifiers(List<String> modifiersFromJson) {
        if (modifiersFromJson == null) {
            return;
        }
        for (String modifier : modifiersFromJson) {
            if (modifier == null) continue;
            switch (modifier.toLowerCase()) {
                case "public":
                    this.accessModifier = AccessModifier.PUBLIC;
                    break;
                case "private":
                    this.accessModifier = AccessModifier.PRIVATE;
                    break;
                case "protected":
                    this.accessModifier = AccessModifier.PROTECTED;
                    break;
                case "static":
                    this.isStatic = true;
                    break;
                case "final":
                    this.isFinal = true;
                    break;
                // transient, volatile are other field modifiers but might not come from LLM in this format
                default:
                    break;
            }
        }
        if (this.isStatic && this.isFinal) {
            this.isConstant = true;
        }
        // Infer PACKAGE_PRIVATE if no explicit access modifier is found
        if (this.accessModifier == null && 
            !modifiersFromJson.stream().anyMatch(m -> m != null && List.of("public", "private", "protected").contains(m.toLowerCase()))) {
            this.accessModifier = AccessModifier.PACKAGE_PRIVATE;
        }
    }
} 