package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 方法定义模型
 */
@Data
@Builder
public class MethodDefinition {
    /**
     * 方法名
     */
    private String name;
    
    /**
     * 返回类型
     */
    private String returnType;
    
    /**
     * 访问修饰符
     */
    private AccessModifier accessModifier;
    
    /**
     * 参数列表
     */
    @Builder.Default
    private List<ParameterDefinition> parameters = new ArrayList<>();
    
    /**
     * 异常列表
     */
    @Builder.Default
    private List<String> exceptions = new ArrayList<>();
    
    /**
     * 注解列表
     */
    @Builder.Default
    private List<String> annotations = new ArrayList<>();
    
    /**
     * 是否为静态方法
     */
    private boolean isStatic;
    
    /**
     * 是否为抽象方法
     */
    private boolean isAbstract;
    
    /**
     * 是否为最终方法
     */
    private boolean isFinal;
    
    /**
     * 是否为同步方法
     */
    private boolean isSynchronized;
    
    /**
     * 是否为默认方法（接口）
     */
    private boolean isDefault;
    
    /**
     * 是否为构造方法
     */
    private boolean isConstructor;
    
    /**
     * 方法注释
     */
    private String comment;
    
    /**
     * 方法体
     */
    private String body;

    public void setModifiers(List<String> modifiersFromJson) {
        if (modifiersFromJson == null) {
            return;
        }
        for (String modifier : modifiersFromJson) {
            if (modifier == null) continue;
            switch (modifier.toLowerCase()) {
                case "public":
                    this.accessModifier = AccessModifier.PUBLIC;
                    break;
                case "private":
                    this.accessModifier = AccessModifier.PRIVATE;
                    break;
                case "protected":
                    this.accessModifier = AccessModifier.PROTECTED;
                    break;
                case "static":
                    this.isStatic = true;
                    break;
                case "final":
                    this.isFinal = true;
                    break;
                case "abstract":
                    this.isAbstract = true;
                    break;
                case "synchronized":
                    this.isSynchronized = true;
                    break;
                case "default": // for interface default methods
                    this.isDefault = true;
                    break;
                default:
                    break;
            }
        }
        // Infer PACKAGE_PRIVATE if no explicit access modifier is found
        if (this.accessModifier == null && 
            !modifiersFromJson.stream().anyMatch(m -> m != null && List.of("public", "private", "protected").contains(m.toLowerCase()))) {
            this.accessModifier = AccessModifier.PACKAGE_PRIVATE;
        }
    }
} 