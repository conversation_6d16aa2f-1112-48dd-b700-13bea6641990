package com.archscope.domain.repository;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务仓库接口
 */
public interface TaskRepository {
    
    /**
     * 保存任务
     *
     * @param task 任务
     * @return 保存后的任务
     */
    Task save(Task task);
    
    /**
     * 根据ID查找任务
     *
     * @param id 任务ID
     * @return 任务
     */
    Optional<Task> findById(Long id);
    
    /**
     * 查找下一个待执行的任务
     *
     * @return 任务
     */
    Optional<Task> findNextPendingTask();
    
    /**
     * 查找指定类型的下一个待执行任务
     *
     * @param taskType 任务类型
     * @return 任务
     */
    Optional<Task> findNextPendingTaskByType(String taskType);
    
    /**
     * 查找所有指定状态的任务
     *
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findAllByStatus(TaskStatus status);
    
    /**
     * 查找所有指定状态和类型的任务
     *
     * @param status 任务状态
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<Task> findAllByStatusAndTaskType(TaskStatus status, String taskType);
    
    /**
     * 查找所有指定状态和项目ID的任务
     *
     * @param status 任务状态
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<Task> findAllByStatusAndProjectId(TaskStatus status, Long projectId);
    
    /**
     * 删除指定日期之前的已完成任务
     *
     * @param date 日期
     * @return 删除的任务数量
     */
    int deleteCompletedTasksOlderThan(LocalDateTime date);
    
    /**
     * 查找所有任务
     *
     * @return 任务列表
     */
    List<Task> findAll();

    Task update(Task task);

    /**
     * 删除任务
     *
     * @param id 任务ID
     */
    void deleteById(Long id);

    List<Task> findByProjectId(Long projectId);
}
