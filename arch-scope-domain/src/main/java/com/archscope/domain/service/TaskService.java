package com.archscope.domain.service;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskType;

import java.util.List;

/**
 * 任务服务接口
 */
public interface TaskService {
    
    /**
     * 创建任务
     *
     * @param projectId 项目ID
     * @param taskType 任务类型
     * @param parameters 任务参数
     * @return 创建的任务
     */
    Task createTask(Long projectId, TaskType taskType, String parameters);
    
    /**
     * 提交任务到队列
     *
     * @param taskId 任务ID
     * @return 是否提交成功
     */
    boolean submitTaskToQueue(Long taskId);
    
    /**
     * 获取项目的任务列表
     *
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<Task> getTasksByProject(Long projectId);
    
    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelTask(Long taskId);
    
    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    Task getTaskById(Long taskId);
    
    /**
     * 重试失败的任务
     *
     * @param taskId 任务ID
     * @return 是否重试成功
     */
    boolean retryTask(Long taskId);
} 