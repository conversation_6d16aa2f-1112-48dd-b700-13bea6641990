package com.archscope.domain.service.git;

import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.nio.file.Path;
import java.util.Map;

@Service
public class GitServiceImpl implements GitService {

    private static final Logger logger = LoggerFactory.getLogger(GitServiceImpl.class);

    @Override
    public GitOperationResult cloneRepository(String repositoryUrl, Path localPath, Map<String, String> credentials) {
        logger.info("Attempting to clone repository {} to {}", repositoryUrl, localPath);
        try {
            org.eclipse.jgit.api.CloneCommand cloneCommand = Git.cloneRepository()
                    .setURI(repositoryUrl)
                    .setDirectory(localPath.toFile());

            if (credentials != null && !credentials.isEmpty()) {
                String username = credentials.get("username");
                String password = credentials.get("password");
                String privateKeyPath = credentials.get("privateKeyPath");
                String passphrase = credentials.get("passphrase");

                if (username != null && password != null) {
                    // HTTPS authentication
                    cloneCommand.setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password));
                } else if (privateKeyPath != null) {
                    // SSH authentication with private key
                    // Note: JGit SSH support requires org.eclipse.jgit.ssh.apache dependency
                    // and potentially additional configuration for known_hosts, etc.
                    // For simplicity, this example assumes basic private key usage.
                    // More robust SSH handling might require custom JSch or other configurations.
                    // cloneCommand.setTransportConfigCallback(new SshTransportConfigCallback() { ... });
                    logger.warn("SSH private key authentication is not fully implemented in this example.");
                    return GitOperationResult.failure("SSH private key authentication not fully supported yet.");
                }
            }

            cloneCommand.call();

            logger.info("Successfully cloned repository {} to {}", repositoryUrl, localPath);
            return GitOperationResult.success("Repository cloned successfully", localPath.toString());

        } catch (GitAPIException e) {
            logger.error("Failed to clone repository {}: {}", repositoryUrl, e.getMessage(), e);
            return GitOperationResult.failure("Failed to clone repository: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An unexpected error occurred during cloning {}: {}", repositoryUrl, e.getMessage(), e);
            return GitOperationResult.failure("An unexpected error occurred during cloning: " + e.getMessage());
        }
    }

    @Override
    public GitOperationResult fetchChanges(Path localPath, String remoteName, Map<String, String> credentials) {
        logger.info("Attempting to fetch changes for repository at {}", localPath);
        try (Git git = Git.open(localPath.toFile())) {
            org.eclipse.jgit.api.FetchCommand fetchCommand = git.fetch();

            if (remoteName != null && !remoteName.isEmpty()) {
                fetchCommand.setRemote(remoteName);
            }

            if (credentials != null && !credentials.isEmpty()) {
                String username = credentials.get("username");
                String password = credentials.get("password");
                String privateKeyPath = credentials.get("privateKeyPath");
                String passphrase = credentials.get("passphrase");

                if (username != null && password != null) {
                    // HTTPS authentication
                    fetchCommand.setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password));
                } else if (privateKeyPath != null) {
                    // SSH authentication with private key
                    logger.warn("SSH private key authentication is not fully implemented in this example.");
                    return GitOperationResult.failure("SSH private key authentication not fully supported yet.");
                }
            }

            fetchCommand.call();

            logger.info("Successfully fetched changes for repository at {}", localPath);
            return GitOperationResult.success("Changes fetched successfully", localPath.toString());

        } catch (GitAPIException e) {
            logger.error("Failed to fetch changes for repository at {}: {}", localPath, e.getMessage(), e);
            return GitOperationResult.failure("Failed to fetch changes: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An unexpected error occurred during fetching {}: {}", localPath, e.getMessage(), e);
            return GitOperationResult.failure("An unexpected error occurred during fetching: " + e.getMessage());
        }
    }
}
