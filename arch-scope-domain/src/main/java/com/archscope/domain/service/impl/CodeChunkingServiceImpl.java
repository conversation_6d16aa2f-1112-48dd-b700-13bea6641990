package com.archscope.domain.service.impl;

import com.archscope.domain.model.parser.CodeChunk;
import com.archscope.domain.model.parser.LanguageType;
import com.archscope.domain.service.ChunkingStrategy;
import com.archscope.domain.service.CodeChunkingService;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 代码分块服务默认实现
 */
@Slf4j
public class CodeChunkingServiceImpl implements CodeChunkingService {

    // 大文件阈值，超过此大小的文件将被分块处理（默认为100KB）
    private static final long LARGE_FILE_THRESHOLD = 100 * 1024;

    // 语言特定的分块策略
    private final Map<LanguageType, ChunkingStrategy> chunkingStrategies;

    // 默认分块策略
    private final ChunkingStrategy defaultStrategy;

    public CodeChunkingServiceImpl() {
        chunkingStrategies = new HashMap<>();
        chunkingStrategies.put(LanguageType.JAVA, new JavaChunkingStrategy());
        chunkingStrategies.put(LanguageType.JAVASCRIPT, new JavaScriptChunkingStrategy());
        chunkingStrategies.put(LanguageType.TYPESCRIPT, new TypeScriptChunkingStrategy());
        // 可以添加其他语言的分块策略

        defaultStrategy = new DefaultChunkingStrategy();
    }

    @Override
    public List<CodeChunk> chunkFile(File file, LanguageType languageType) throws IOException {
        // 检查文件大小，决定是否需要分块
        if (file.length() <= LARGE_FILE_THRESHOLD) {
            log.debug("文件 {} 大小小于阈值，不进行分块", file.getPath());
            return List.of();
        }

        String content = Files.readString(file.toPath());
        return chunkContent(content, file.getName(), languageType);
    }

    @Override
    public List<CodeChunk> chunkContent(String content, String filename, LanguageType languageType) {
        // 获取语言特定的分块策略
        ChunkingStrategy strategy = chunkingStrategies.getOrDefault(languageType, defaultStrategy);

        // 提取上下文信息
        String context = extractContext(content, languageType);

        // 使用策略进行分块
        List<CodeChunk> chunks = strategy.chunk(content, filename, context);

        // 分析依赖关系
        return analyzeDependencies(chunks);
    }

    @Override
    public List<CodeChunk> analyzeDependencies(List<CodeChunk> chunks) {
        // 创建一个映射，用于快速查找代码块
        Map<String, CodeChunk> chunkMap = new HashMap<>();
        for (CodeChunk chunk : chunks) {
            chunkMap.put(chunk.getId(), chunk);
        }

        // 分析依赖关系
        for (CodeChunk chunk : chunks) {
            // 这里可以实现更复杂的依赖分析逻辑
            // 例如，分析导入语句、类继承关系等
            // 简单起见，这里只是示例

            // 示例：检查类继承关系
            if (chunk.getType() == com.archscope.domain.model.parser.ChunkType.CLASS) {
                Pattern extendsPattern = Pattern.compile("extends\\s+([\\w.]+)");
                Matcher extendsMatcher = extendsPattern.matcher(chunk.getContent());
                while (extendsMatcher.find()) {
                    String superClass = extendsMatcher.group(1);
                    chunk.getDependencies().add(superClass);
                }
            }
        }

        return chunks;
    }

    @Override
    public String extractContext(String content, LanguageType languageType) {
        // 获取语言特定的分块策略
        ChunkingStrategy strategy = chunkingStrategies.getOrDefault(languageType, defaultStrategy);

        // 使用策略提取上下文信息
        return strategy.extractContext(content);
    }

    /**
     * Java语言分块策略
     */
    private static class JavaChunkingStrategy implements ChunkingStrategy {

        private static final Pattern CLASS_PATTERN = Pattern.compile("(public|private|protected)?\\s*(abstract|final)?\\s*class\\s+(\\w+)\\s*.*?\\{", Pattern.DOTALL);
        private static final Pattern METHOD_PATTERN = Pattern.compile("(public|private|protected)?\\s*(static|final)?\\s*.*?\\s+(\\w+)\\s*\\(.*?\\)\\s*.*?\\{", Pattern.DOTALL);

        @Override
        public List<CodeChunk> chunk(String content, String filename, String context) {
            List<CodeChunk> chunks = new ArrayList<>();

            // 按类分块
            Matcher classMatcher = CLASS_PATTERN.matcher(content);
            while (classMatcher.find()) {
                int classStart = classMatcher.start();
                String className = classMatcher.group(3);

                // 找到类的结束位置（匹配闭合的大括号）
                int classEnd = findClosingBrace(content, classMatcher.end());
                if (classEnd == -1) {
                    classEnd = content.length();
                }

                String classContent = content.substring(classStart, classEnd + 1);

                // 创建类级别的代码块
                CodeChunk classChunk = CodeChunk.builder()
                        .id(filename + "#" + className)
                        .filename(filename)
                        .content(classContent)
                        .type(com.archscope.domain.model.parser.ChunkType.CLASS)
                        .startLine(countLines(content.substring(0, classStart)))
                        .endLine(countLines(content.substring(0, classEnd + 1)))
                        .context(context)
                        .build();

                chunks.add(classChunk);

                // 可选：进一步按方法分块
                Matcher methodMatcher = METHOD_PATTERN.matcher(classContent);
                while (methodMatcher.find()) {
                    int methodStart = methodMatcher.start();
                    String methodName = methodMatcher.group(3);

                    // 找到方法的结束位置（匹配闭合的大括号）
                    int methodEnd = findClosingBrace(classContent, methodMatcher.end());
                    if (methodEnd == -1) {
                        methodEnd = classContent.length();
                    }

                    String methodContent = classContent.substring(methodStart, methodEnd + 1);

                    // 创建方法级别的代码块
                    CodeChunk methodChunk = CodeChunk.builder()
                            .id(filename + "#" + className + "." + methodName)
                            .filename(filename)
                            .content(methodContent)
                            .type(com.archscope.domain.model.parser.ChunkType.METHOD)
                            .startLine(classChunk.getStartLine() + countLines(classContent.substring(0, methodStart)))
                            .endLine(classChunk.getStartLine() + countLines(classContent.substring(0, methodEnd + 1)))
                            .context(context + "\n" + classContent.substring(0, methodStart))
                            .build();

                    chunks.add(methodChunk);
                }
            }

            return chunks;
        }

        @Override
        public String extractContext(String content) {
            StringBuilder context = new StringBuilder();

            // 提取包声明
            Pattern packagePattern = Pattern.compile("package\\s+([\\w.]+)\\s*;");
            Matcher packageMatcher = packagePattern.matcher(content);
            if (packageMatcher.find()) {
                context.append(content.substring(packageMatcher.start(), packageMatcher.end())).append("\n");
            }

            // 提取导入语句
            Pattern importPattern = Pattern.compile("import\\s+([\\w.]+)\\s*;");
            Matcher importMatcher = importPattern.matcher(content);
            while (importMatcher.find()) {
                context.append(content.substring(importMatcher.start(), importMatcher.end())).append("\n");
            }

            return context.toString();
        }

        private int findClosingBrace(String content, int startPos) {
            int count = 1;
            for (int i = startPos; i < content.length(); i++) {
                char c = content.charAt(i);
                if (c == '{') {
                    count++;
                } else if (c == '}') {
                    count--;
                    if (count == 0) {
                        return i;
                    }
                }
            }
            return -1;
        }

        private int countLines(String text) {
            return text.split("\n").length;
        }
    }

    /**
     * JavaScript语言分块策略
     */
    private static class JavaScriptChunkingStrategy implements ChunkingStrategy {

        private static final Pattern CLASS_PATTERN = Pattern.compile("class\\s+(\\w+)\\s*(extends\\s+([\\w.]+))?\\s*\\{", Pattern.DOTALL);
        private static final Pattern FUNCTION_PATTERN = Pattern.compile("function\\s+(\\w+)\\s*\\(.*?\\)\\s*\\{", Pattern.DOTALL);

        @Override
        public List<CodeChunk> chunk(String content, String filename, String context) {
            List<CodeChunk> chunks = new ArrayList<>();

            // 按类分块
            Matcher classMatcher = CLASS_PATTERN.matcher(content);
            while (classMatcher.find()) {
                int classStart = classMatcher.start();
                String className = classMatcher.group(1);

                // 找到类的结束位置（匹配闭合的大括号）
                int classEnd = findClosingBrace(content, classMatcher.end());
                if (classEnd == -1) {
                    classEnd = content.length();
                }

                String classContent = content.substring(classStart, classEnd + 1);

                // 创建类级别的代码块
                CodeChunk classChunk = CodeChunk.builder()
                        .id(filename + "#" + className)
                        .filename(filename)
                        .content(classContent)
                        .type(com.archscope.domain.model.parser.ChunkType.CLASS)
                        .startLine(countLines(content.substring(0, classStart)))
                        .endLine(countLines(content.substring(0, classEnd + 1)))
                        .context(context)
                        .build();

                chunks.add(classChunk);
            }

            // 按函数分块
            Matcher functionMatcher = FUNCTION_PATTERN.matcher(content);
            while (functionMatcher.find()) {
                int functionStart = functionMatcher.start();
                String functionName = functionMatcher.group(1);

                // 找到函数的结束位置（匹配闭合的大括号）
                int functionEnd = findClosingBrace(content, functionMatcher.end());
                if (functionEnd == -1) {
                    functionEnd = content.length();
                }

                String functionContent = content.substring(functionStart, functionEnd + 1);

                // 创建函数级别的代码块
                CodeChunk functionChunk = CodeChunk.builder()
                        .id(filename + "#" + functionName)
                        .filename(filename)
                        .content(functionContent)
                        .type(com.archscope.domain.model.parser.ChunkType.FUNCTION)
                        .startLine(countLines(content.substring(0, functionStart)))
                        .endLine(countLines(content.substring(0, functionEnd + 1)))
                        .context(context)
                        .build();

                chunks.add(functionChunk);
            }

            return chunks;
        }

        @Override
        public String extractContext(String content) {
            StringBuilder context = new StringBuilder();

            // 提取导入语句
            Pattern importPattern = Pattern.compile("import\\s+.*?from\\s+['\"]([^'\"]+)['\"];");
            Matcher importMatcher = importPattern.matcher(content);
            while (importMatcher.find()) {
                context.append(content.substring(importMatcher.start(), importMatcher.end())).append("\n");
            }

            // 提取require语句
            Pattern requirePattern = Pattern.compile("const\\s+.*?=\\s+require\\(['\"]([^'\"]+)['\"]\\);");
            Matcher requireMatcher = requirePattern.matcher(content);
            while (requireMatcher.find()) {
                context.append(content.substring(requireMatcher.start(), requireMatcher.end())).append("\n");
            }

            return context.toString();
        }

        private int findClosingBrace(String content, int startPos) {
            int count = 1;
            for (int i = startPos; i < content.length(); i++) {
                char c = content.charAt(i);
                if (c == '{') {
                    count++;
                } else if (c == '}') {
                    count--;
                    if (count == 0) {
                        return i;
                    }
                }
            }
            return -1;
        }

        private int countLines(String text) {
            return text.split("\n").length;
        }
    }

    /**
     * TypeScript语言分块策略
     */
    private static class TypeScriptChunkingStrategy implements ChunkingStrategy {

        private static final Pattern CLASS_PATTERN = Pattern.compile("class\\s+(\\w+)\\s*(extends\\s+([\\w.]+)(\\s*<.*?>)?)?\\s*\\{", Pattern.DOTALL);
        private static final Pattern INTERFACE_PATTERN = Pattern.compile("interface\\s+(\\w+)\\s*(extends\\s+([\\w.]+))?\\s*\\{", Pattern.DOTALL);
        private static final Pattern FUNCTION_PATTERN = Pattern.compile("function\\s+(\\w+)\\s*\\(.*?\\)\\s*\\{", Pattern.DOTALL);

        @Override
        public List<CodeChunk> chunk(String content, String filename, String context) {
            List<CodeChunk> chunks = new ArrayList<>();

            // 按类分块
            Matcher classMatcher = CLASS_PATTERN.matcher(content);
            while (classMatcher.find()) {
                int classStart = classMatcher.start();
                String className = classMatcher.group(1);

                // 找到类的结束位置（匹配闭合的大括号）
                int classEnd = findClosingBrace(content, classMatcher.end());
                if (classEnd == -1) {
                    classEnd = content.length();
                }

                String classContent = content.substring(classStart, classEnd + 1);

                // 创建类级别的代码块
                CodeChunk classChunk = CodeChunk.builder()
                        .id(filename + "#" + className)
                        .filename(filename)
                        .content(classContent)
                        .type(com.archscope.domain.model.parser.ChunkType.CLASS)
                        .startLine(countLines(content.substring(0, classStart)))
                        .endLine(countLines(content.substring(0, classEnd + 1)))
                        .context(context)
                        .build();

                chunks.add(classChunk);
            }

            // 按接口分块
            Matcher interfaceMatcher = INTERFACE_PATTERN.matcher(content);
            while (interfaceMatcher.find()) {
                int interfaceStart = interfaceMatcher.start();
                String interfaceName = interfaceMatcher.group(1);

                // 找到接口的结束位置（匹配闭合的大括号）
                int interfaceEnd = findClosingBrace(content, interfaceMatcher.end());
                if (interfaceEnd == -1) {
                    interfaceEnd = content.length();
                }

                String interfaceContent = content.substring(interfaceStart, interfaceEnd + 1);

                // 创建接口级别的代码块
                CodeChunk interfaceChunk = CodeChunk.builder()
                        .id(filename + "#" + interfaceName)
                        .filename(filename)
                        .content(interfaceContent)
                        .type(com.archscope.domain.model.parser.ChunkType.INTERFACE)
                        .startLine(countLines(content.substring(0, interfaceStart)))
                        .endLine(countLines(content.substring(0, interfaceEnd + 1)))
                        .context(context)
                        .build();

                chunks.add(interfaceChunk);
            }

            // 按函数分块
            Matcher functionMatcher = FUNCTION_PATTERN.matcher(content);
            while (functionMatcher.find()) {
                int functionStart = functionMatcher.start();
                String functionName = functionMatcher.group(1);

                // 找到函数的结束位置（匹配闭合的大括号）
                int functionEnd = findClosingBrace(content, functionMatcher.end());
                if (functionEnd == -1) {
                    functionEnd = content.length();
                }

                String functionContent = content.substring(functionStart, functionEnd + 1);

                // 创建函数级别的代码块
                CodeChunk functionChunk = CodeChunk.builder()
                        .id(filename + "#" + functionName)
                        .filename(filename)
                        .content(functionContent)
                        .type(com.archscope.domain.model.parser.ChunkType.FUNCTION)
                        .startLine(countLines(content.substring(0, functionStart)))
                        .endLine(countLines(content.substring(0, functionEnd + 1)))
                        .context(context)
                        .build();

                chunks.add(functionChunk);
            }

            return chunks;
        }

        @Override
        public String extractContext(String content) {
            StringBuilder context = new StringBuilder();

            // 提取导入语句
            Pattern importPattern = Pattern.compile("import\\s+.*?from\\s+['\"]([^'\"]+)['\"];");
            Matcher importMatcher = importPattern.matcher(content);
            while (importMatcher.find()) {
                context.append(content.substring(importMatcher.start(), importMatcher.end())).append("\n");
            }

            // 提取类型定义
            Pattern typePattern = Pattern.compile("type\\s+(\\w+)\\s*=\\s*.*?;", Pattern.DOTALL);
            Matcher typeMatcher = typePattern.matcher(content);
            while (typeMatcher.find()) {
                context.append(content.substring(typeMatcher.start(), typeMatcher.end())).append("\n");
            }

            return context.toString();
        }

        private int findClosingBrace(String content, int startPos) {
            int count = 1;
            for (int i = startPos; i < content.length(); i++) {
                char c = content.charAt(i);
                if (c == '{') {
                    count++;
                } else if (c == '}') {
                    count--;
                    if (count == 0) {
                        return i;
                    }
                }
            }
            return -1;
        }

        private int countLines(String text) {
            return text.split("\n").length;
        }
    }

    /**
     * 默认分块策略
     */
    private static class DefaultChunkingStrategy implements ChunkingStrategy {

        @Override
        public List<CodeChunk> chunk(String content, String filename, String context) {
            // 默认策略不进行分块，返回空列表
            return new ArrayList<>();
        }

        @Override
        public String extractContext(String content) {
            // 默认策略不提取上下文，返回空字符串
            return "";
        }
    }
}
