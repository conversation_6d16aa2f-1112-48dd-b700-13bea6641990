package com.archscope.domain.service.impl;

import com.archscope.domain.model.parser.CodeChunk;
import com.archscope.domain.model.parser.CodeParser;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;
import com.archscope.domain.service.CodeChunkingService;
import com.archscope.domain.service.CodeRepositoryService;
import com.archscope.domain.service.IncrementalParseService;
import com.archscope.domain.cache.ParseResultCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 增量解析服务默认实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IncrementalParseServiceImpl implements IncrementalParseService {

    private final CodeParser codeParser;
    private final CodeRepositoryService codeRepositoryService;
    private final ParseResultCacheService parseResultCacheService;
    private final CodeChunkingService codeChunkingService;

    // 大文件阈值，超过此大小的文件将被分块处理（默认为100KB）
    private static final long LARGE_FILE_THRESHOLD = 100 * 1024;

    @Override
    public List<FileParseResult> parseIncrementally(Long repositoryId, String fromCommit, String toCommit) {
        log.info("开始增量解析仓库 {} 从提交 {} 到 {}", repositoryId, fromCommit, toCommit);

        try {
            // 1. 获取缓存的解析结果
            List<FileParseResult> cachedResults = getParseResultsFromCache(repositoryId, fromCommit);

            // 2. 获取变更的文件
            List<File> changedFiles = getChangedFiles(repositoryId, fromCommit, toCommit);
            log.info("检测到 {} 个变更文件", changedFiles.size());

            if (changedFiles.isEmpty()) {
                log.info("没有变更文件，直接返回缓存结果");
                return cachedResults;
            }

            // 3. 解析变更的文件
            List<FileParseResult> newResults = parseChangedFiles(changedFiles);
            log.info("成功解析 {} 个变更文件", newResults.size());

            // 4. 合并解析结果
            List<FileParseResult> mergedResults = mergeParseResults(cachedResults, newResults);

            // 5. 保存合并后的结果到缓存
            saveParseResultsToCache(repositoryId, toCommit, mergedResults);

            return mergedResults;
        } catch (Exception e) {
            log.error("增量解析失败", e);
            throw new RuntimeException("增量解析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<File> getChangedFiles(Long repositoryId, String fromCommit, String toCommit) {
        log.debug("获取仓库 {} 从提交 {} 到 {} 的变更文件", repositoryId, fromCommit, toCommit);

        // 调用CodeRepositoryService获取变更文件列表
        List<String> changedFilePaths = codeRepositoryService.getRepositoryChanges(repositoryId, fromCommit, toCommit);

        // 将文件路径转换为File对象
        return changedFilePaths.stream()
                .map(File::new)
                .filter(File::exists)
                .collect(Collectors.toList());
    }

    @Override
    public List<FileParseResult> getParseResultsFromCache(Long repositoryId, String commitId) {
        log.debug("从缓存获取仓库 {} 提交 {} 的解析结果", repositoryId, commitId);
        return parseResultCacheService.getParseResults(repositoryId, commitId);
    }

    @Override
    public void saveParseResultsToCache(Long repositoryId, String commitId, List<FileParseResult> parseResults) {
        log.debug("保存仓库 {} 提交 {} 的解析结果到缓存", repositoryId, commitId);
        parseResultCacheService.saveParseResults(repositoryId, commitId, parseResults);
    }

    @Override
    public List<FileParseResult> mergeParseResults(List<FileParseResult> existingResults, List<FileParseResult> newResults) {
        log.debug("合并解析结果: 现有结果 {} 个, 新结果 {} 个", existingResults.size(), newResults.size());

        // 创建一个新的结果列表，初始包含所有现有结果
        List<FileParseResult> mergedResults = new ArrayList<>(existingResults);

        // 创建一个映射，用于快速查找现有结果中的文件
        Map<String, Integer> filePathToIndex = new HashMap<>();
        for (int i = 0; i < existingResults.size(); i++) {
            filePathToIndex.put(existingResults.get(i).getFilePath(), i);
        }

        // 处理新的解析结果
        for (FileParseResult newResult : newResults) {
            String filePath = newResult.getFilePath();

            // 检查文件是否已存在于现有结果中
            if (filePathToIndex.containsKey(filePath)) {
                // 替换现有结果
                int index = filePathToIndex.get(filePath);
                mergedResults.set(index, newResult);
                log.debug("替换文件 {} 的解析结果", filePath);
            } else {
                // 添加新结果
                mergedResults.add(newResult);
                log.debug("添加文件 {} 的新解析结果", filePath);
            }
        }

        return mergedResults;
    }

    @Override
    public void clearCache(Long repositoryId) {
        log.info("清除仓库 {} 的解析缓存", repositoryId);
        parseResultCacheService.clearCache(repositoryId);
    }

    @Override
    public String getLastModifiedCommit(Long repositoryId, String filePath) {
        log.debug("获取仓库 {} 文件 {} 的最后修改提交", repositoryId, filePath);
        return codeRepositoryService.getFileLastCommit(repositoryId, filePath);
    }

    /**
     * 解析变更的文件
     *
     * @param changedFiles 变更的文件列表
     * @return 解析结果列表
     */
    private List<FileParseResult> parseChangedFiles(List<File> changedFiles) {
        log.debug("开始解析 {} 个变更文件", changedFiles.size());

        List<FileParseResult> results = new ArrayList<>();

        for (File file : changedFiles) {
            try {
                // 检查文件大小，决定是否需要分块
                if (file.length() > LARGE_FILE_THRESHOLD) {
                    log.debug("文件 {} 大小超过阈值，进行分块处理", file.getPath());
                    // 对大文件进行分块处理
                    List<CodeChunk> chunks = codeChunkingService.chunkFile(file, LanguageType.fromFilename(file.getName()));
                    if (!chunks.isEmpty()) {
                        // 解析每个代码块
                        FileParseResult result = parseFileChunks(file, chunks);
                        results.add(result);
                        log.debug("成功解析分块文件: {}", file.getPath());
                    } else {
                        // 如果分块失败，回退到普通解析
                        log.debug("文件 {} 分块失败，回退到普通解析", file.getPath());
                        FileParseResult result = codeParser.parseFile(file);
                        results.add(result);
                        log.debug("成功解析文件: {}", file.getPath());
                    }
                } else {
                    // 小文件直接解析
                    FileParseResult result = codeParser.parseFile(file);
                    results.add(result);
                    log.debug("成功解析文件: {}", file.getPath());
                }
            } catch (IOException e) {
                log.error("解析文件 {} 失败", file.getPath(), e);
                // 创建一个失败的解析结果
                FileParseResult failedResult = FileParseResult.builder()
                        .filename(file.getName())
                        .filePath(file.getPath())
                        .languageType(LanguageType.fromFilename(file.getName()))
                        .successful(false)
                        .errorMessage("解析失败: " + e.getMessage())
                        .build();
                results.add(failedResult);
            }
        }

        // 分析上下文关联性，找出可能受影响的文件
        List<File> relatedFiles = analyzeContextDependencies(changedFiles, results);
        if (!relatedFiles.isEmpty()) {
            log.debug("发现 {} 个相关联的文件需要重新解析", relatedFiles.size());
            // 解析相关联的文件
            for (File file : relatedFiles) {
                try {
                    FileParseResult result = codeParser.parseFile(file);
                    results.add(result);
                    log.debug("成功解析相关联文件: {}", file.getPath());
                } catch (IOException e) {
                    log.error("解析相关联文件 {} 失败", file.getPath(), e);
                }
            }
        }

        return results;
    }

    /**
     * 解析文件的代码块
     *
     * @param file 文件
     * @param chunks 代码块列表
     * @return 解析结果
     * @throws IOException 如果文件读取失败
     */
    private FileParseResult parseFileChunks(File file, List<CodeChunk> chunks) throws IOException {
        log.debug("解析文件 {} 的 {} 个代码块", file.getPath(), chunks.size());

        // 解析每个代码块
        List<FileParseResult> chunkResults = new ArrayList<>();
        for (CodeChunk chunk : chunks) {
            // 构建包含上下文的完整内容
            String contentWithContext = chunk.getContext() + "\n" + chunk.getContent();
            FileParseResult chunkResult = codeParser.parseFile(chunk.getFilename(), contentWithContext);
            chunkResults.add(chunkResult);
        }

        // 合并块解析结果
        return mergeChunkResults(file, chunkResults);
    }

    /**
     * 合并代码块的解析结果
     *
     * @param file 文件
     * @param chunkResults 代码块解析结果列表
     * @return 合并后的解析结果
     */
    private FileParseResult mergeChunkResults(File file, List<FileParseResult> chunkResults) {
        log.debug("合并文件 {} 的 {} 个代码块解析结果", file.getPath(), chunkResults.size());

        // 创建一个新的解析结果
        FileParseResult.FileParseResultBuilder resultBuilder = FileParseResult.builder()
                .filename(file.getName())
                .filePath(file.getPath())
                .languageType(LanguageType.fromFilename(file.getName()))
                .successful(true);

        // 合并包名
        for (FileParseResult chunkResult : chunkResults) {
            if (chunkResult.getPackageName() != null) {
                resultBuilder.packageName(chunkResult.getPackageName());
                break;
            }
        }

        // 合并导入列表
        Set<String> imports = new HashSet<>();
        for (FileParseResult chunkResult : chunkResults) {
            imports.addAll(chunkResult.getImports());
        }
        resultBuilder.imports(new ArrayList<>(imports));

        // 合并类定义列表
        Set<String> classNames = new HashSet<>();
        List<com.archscope.domain.model.parser.ClassDefinition> classDefinitions = new ArrayList<>();
        for (FileParseResult chunkResult : chunkResults) {
            for (com.archscope.domain.model.parser.ClassDefinition classDef : chunkResult.getClassDefinitions()) {
                if (!classNames.contains(classDef.getName())) {
                    classNames.add(classDef.getName());
                    classDefinitions.add(classDef);
                }
            }
        }
        resultBuilder.classDefinitions(classDefinitions);

        // 合并依赖关系列表
        Set<String> dependencyKeys = new HashSet<>();
        List<com.archscope.domain.model.parser.DependencyRelation> dependencies = new ArrayList<>();
        for (FileParseResult chunkResult : chunkResults) {
            for (com.archscope.domain.model.parser.DependencyRelation dependency : chunkResult.getDependencies()) {
                String key = dependency.getSourceClass() + "-" + dependency.getTargetClass();
                if (!dependencyKeys.contains(key)) {
                    dependencyKeys.add(key);
                    dependencies.add(dependency);
                }
            }
        }
        resultBuilder.dependencies(dependencies);

        // 合并文件注释
        StringBuilder fileComment = new StringBuilder();
        for (FileParseResult chunkResult : chunkResults) {
            if (chunkResult.getFileComment() != null) {
                fileComment.append(chunkResult.getFileComment()).append("\n");
            }
        }
        resultBuilder.fileComment(fileComment.toString().trim());

        return resultBuilder.build();
    }

    /**
     * 分析上下文关联性，找出可能受影响的文件
     *
     * @param changedFiles 变更的文件列表
     * @param parseResults 解析结果列表
     * @return 可能受影响的文件列表
     */
    private List<File> analyzeContextDependencies(List<File> changedFiles, List<FileParseResult> parseResults) {
        log.debug("分析 {} 个变更文件的上下文关联性", changedFiles.size());

        // 创建一个集合，用于存储可能受影响的文件路径
        Set<String> relatedFilePaths = new HashSet<>();

        // 提取变更文件中的类名
        Set<String> changedClasses = new HashSet<>();
        for (FileParseResult result : parseResults) {
            for (com.archscope.domain.model.parser.ClassDefinition classDef : result.getClassDefinitions()) {
                changedClasses.add(classDef.getFullyQualifiedName());
            }
        }

        // 从缓存中获取所有文件的解析结果
        // 注意：这里假设我们有一个方法可以获取所有文件的解析结果
        // 实际实现中，可能需要从数据库或其他存储中获取
        List<FileParseResult> allResults = getAllParseResults();

        // 查找依赖于变更类的文件
        for (FileParseResult result : allResults) {
            // 跳过变更文件本身
            if (parseResults.stream().anyMatch(r -> r.getFilePath().equals(result.getFilePath()))) {
                continue;
            }

            // 检查依赖关系
            boolean hasRelation = false;
            for (com.archscope.domain.model.parser.DependencyRelation dependency : result.getDependencies()) {
                if (changedClasses.contains(dependency.getTargetClass())) {
                    hasRelation = true;
                    break;
                }
            }

            if (hasRelation) {
                relatedFilePaths.add(result.getFilePath());
            }
        }

        // 将文件路径转换为File对象
        return relatedFilePaths.stream()
                .map(File::new)
                .filter(File::exists)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有文件的解析结果
     * 注意：这是一个示例方法，实际实现可能需要从数据库或其他存储中获取
     *
     * @return 所有文件的解析结果列表
     */
    private List<FileParseResult> getAllParseResults() {
        // 这里应该实现从存储中获取所有文件的解析结果的逻辑
        // 简单起见，这里返回空列表
        return new ArrayList<>();
    }
}
