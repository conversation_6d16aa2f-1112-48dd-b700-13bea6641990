package com.archscope.domain.service.impl;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.DocumentSearchService;
import com.archscope.domain.service.MarkdownService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * 基于Lunr.js的文档搜索服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LunrDocumentSearchService implements DocumentSearchService {

    private final MarkdownService markdownService;
    private final ObjectMapper objectMapper;
    
    private static final List<String> SEARCH_SCRIPTS = Arrays.asList(
            "/js/lunr.min.js",
            "/js/search.js"
    );
    
    private static final List<String> SEARCH_STYLES = Arrays.asList(
            "/css/search.css"
    );

    @Override
    public Path generateSearchIndex(List<DocumentVersion> documentVersions, Path outputDir) {
        log.info("生成搜索索引，文档版本数量: {}, 输出目录: {}", documentVersions.size(), outputDir);
        
        try {
            // 创建输出目录
            Path jsDir = outputDir.resolve("js");
            Files.createDirectories(jsDir);
            
            // 提取文档内容
            List<Map<String, Object>> documents = new ArrayList<>();
            for (DocumentVersion version : documentVersions) {
                try {
                    Map<String, Object> docContent = extractDocumentContent(version);
                    if (docContent != null && !docContent.isEmpty()) {
                        documents.add(docContent);
                    }
                } catch (Exception e) {
                    log.error("提取文档内容失败: {}", version.getId(), e);
                }
            }
            
            // 生成索引文件
            Path indexFile = jsDir.resolve("search-index.json");
            String json = objectMapper.writeValueAsString(documents);
            Files.writeString(indexFile, json);
            
            log.info("搜索索引生成完成: {}", indexFile);
            return indexFile;
            
        } catch (Exception e) {
            log.error("生成搜索索引失败", e);
            throw new RuntimeException("生成搜索索引失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> extractDocumentContent(DocumentVersion documentVersion) {
        log.debug("提取文档内容，文档ID: {}", documentVersion.getId());
        
        try {
            // 读取文档内容
            String contentPath = documentVersion.getContentPath();
            if (!StringUtils.hasText(contentPath)) {
                log.warn("文档内容路径为空: {}", documentVersion.getId());
                return Collections.emptyMap();
            }
            
            Path contentFilePath = Paths.get(contentPath);
            if (!Files.exists(contentFilePath)) {
                log.warn("文档内容文件不存在: {}", contentPath);
                return Collections.emptyMap();
            }
            
            String markdown = Files.readString(contentFilePath);
            
            // 提取标题
            String title = markdownService.extractTitle(markdown);
            
            // 提取纯文本内容（去除Markdown标记）
            String plainText = markdownService.extractPlainText(markdown);
            
            // 创建文档对象
            Map<String, Object> document = new HashMap<>();
            document.put("id", documentVersion.getId().toString());
            document.put("title", title);
            document.put("content", plainText);
            document.put("url", documentVersion.getDocType().name().toLowerCase() + ".html");
            document.put("type", documentVersion.getDocType().getDisplayName());
            document.put("projectId", documentVersion.getProjectId().toString());
            document.put("version", documentVersion.getVersionTag());
            
            return document;
            
        } catch (Exception e) {
            log.error("提取文档内容失败: {}", documentVersion.getId(), e);
            return Collections.emptyMap();
        }
    }

    @Override
    public List<String> getSearchScripts() {
        return SEARCH_SCRIPTS;
    }

    @Override
    public List<String> getSearchStyles() {
        return SEARCH_STYLES;
    }
}
