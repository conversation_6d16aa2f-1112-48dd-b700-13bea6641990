package com.archscope.domain.service.impl;

import com.archscope.domain.entity.ParseResultCache;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.repository.ParseResultCacheRepository;
import com.archscope.domain.service.ParseResultCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 解析结果缓存服务实现类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ParseResultCacheServiceImpl implements ParseResultCacheService {

    private final ParseResultCacheRepository parseResultCacheRepository;

    @Override
    public List<FileParseResult> getParseResults(Long repositoryId, String commitId) {
        log.debug("获取仓库 {} 提交 {} 的解析结果", repositoryId, commitId);
        
        Optional<ParseResultCache> cacheOptional = parseResultCacheRepository.findByRepositoryIdAndCommitId(repositoryId, commitId);
        
        if (cacheOptional.isPresent()) {
            ParseResultCache cache = cacheOptional.get();
            if (cache.isValid()) {
                log.debug("从缓存获取到 {} 个解析结果", cache.getParseResults().size());
                return cache.getParseResults();
            } else {
                log.debug("缓存已失效，返回空结果");
                return Collections.emptyList();
            }
        } else {
            log.debug("缓存不存在，返回空结果");
            return Collections.emptyList();
        }
    }

    @Override
    public void saveParseResults(Long repositoryId, String commitId, List<FileParseResult> parseResults) {
        log.debug("保存仓库 {} 提交 {} 的解析结果，共 {} 个", repositoryId, commitId, parseResults.size());
        
        // 检查是否已存在缓存
        Optional<ParseResultCache> existingCache = parseResultCacheRepository.findByRepositoryIdAndCommitId(repositoryId, commitId);
        
        ParseResultCache cache;
        if (existingCache.isPresent()) {
            // 更新现有缓存
            cache = existingCache.get();
            cache.setParseResults(new ArrayList<>(parseResults));
            cache.setUpdatedAt(LocalDateTime.now());
            cache.setValid(true);
            // 计算缓存大小（简单估算）
            cache.setCacheSize(estimateCacheSize(parseResults));
        } else {
            // 创建新缓存
            cache = ParseResultCache.builder()
                    .repositoryId(repositoryId)
                    .commitId(commitId)
                    .parseResults(new ArrayList<>(parseResults))
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .valid(true)
                    .cacheSize(estimateCacheSize(parseResults))
                    .build();
        }
        
        // 保存缓存
        parseResultCacheRepository.save(cache);
        log.debug("解析结果缓存保存成功");
    }

    @Override
    public Optional<ParseResultCache> getCache(Long repositoryId, String commitId) {
        log.debug("获取仓库 {} 提交 {} 的缓存", repositoryId, commitId);
        return parseResultCacheRepository.findByRepositoryIdAndCommitId(repositoryId, commitId);
    }

    @Override
    public void clearCache(Long repositoryId) {
        log.info("清除仓库 {} 的所有缓存", repositoryId);
        parseResultCacheRepository.deleteAllByRepositoryId(repositoryId);
    }

    @Override
    public boolean cacheExists(Long repositoryId, String commitId) {
        boolean exists = parseResultCacheRepository.existsByRepositoryIdAndCommitId(repositoryId, commitId);
        log.debug("检查仓库 {} 提交 {} 的缓存是否存在: {}", repositoryId, commitId, exists);
        return exists;
    }

    @Override
    public List<ParseResultCache> getAllCaches(Long repositoryId) {
        log.debug("获取仓库 {} 的所有缓存", repositoryId);
        return parseResultCacheRepository.findAllByRepositoryId(repositoryId);
    }

    @Override
    public void deleteCache(Long cacheId) {
        log.debug("删除缓存 {}", cacheId);
        parseResultCacheRepository.deleteById(cacheId);
    }
    
    /**
     * 估算缓存大小
     * 
     * @param parseResults 解析结果列表
     * @return 估算的缓存大小（字节）
     */
    private long estimateCacheSize(List<FileParseResult> parseResults) {
        // 简单估算，实际应用中可能需要更精确的计算
        long size = 0;
        for (FileParseResult result : parseResults) {
            // 基本大小
            size += 1000; // 假设每个结果对象的基本大小
            
            // 文件名和路径
            size += (result.getFilename() != null ? result.getFilename().length() * 2 : 0);
            size += (result.getFilePath() != null ? result.getFilePath().length() * 2 : 0);
            
            // 包名
            size += (result.getPackageName() != null ? result.getPackageName().length() * 2 : 0);
            
            // 导入列表
            size += result.getImports().size() * 50; // 假设每个导入平均50字节
            
            // 类定义列表
            size += result.getClassDefinitions().size() * 500; // 假设每个类定义平均500字节
            
            // 依赖关系列表
            size += result.getDependencies().size() * 200; // 假设每个依赖关系平均200字节
            
            // 其他字段
            size += (result.getFileComment() != null ? result.getFileComment().length() * 2 : 0);
            size += (result.getErrorMessage() != null ? result.getErrorMessage().length() * 2 : 0);
        }
        
        return size;
    }
}
