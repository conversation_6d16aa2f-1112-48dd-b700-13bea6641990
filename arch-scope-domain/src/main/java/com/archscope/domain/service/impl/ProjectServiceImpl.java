package com.archscope.domain.service.impl;

import com.archscope.domain.service.ProjectService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.entity.User;
import com.archscope.domain.repository.ProjectRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目服务实现
 */
@Service
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository projectRepository;

    @Override
    @Transactional
    public Project registerProject(String name, String description, String repositoryUrl, String branch) {
        // 验证仓库URL
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("仓库地址不能为空");
        }

        // TODO 获取当前用户
        User currentUser = User.builder().id(798L).build(); // userService.getCurrentUser();

        // 创建新项目
        Project project = Project.builder()
                .name(name)
                .description(description)
                .repositoryUrl(repositoryUrl)
                .branch(branch != null && !branch.isEmpty() ? branch : "main")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .creatorId(currentUser.getId())
                .status("PENDING")  // 初始状态：待分析
                .active(true)
                .analysisCount(0)
                .documentationVersion(0)
                .build();

        return projectRepository.save(project);
    }

    @Override
    public List<Project> getAllProjects() {
        // TODO 获取当前用户
        User currentUser = User.builder().id(798L).build(); // userService.getCurrentUser();
        
        // TODO: 根据用户权限过滤项目
        // 如果是管理员，可以查看所有项目
        // 如果是普通用户，只能查看有权限的项目
        
        return projectRepository.findAll();
    }

    @Override
    public Project getProjectById(Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在"));
        
        // 检查权限 (TODO: 实现更复杂的权限检查)
        User currentUser = User.builder().id(798L).build(); // userService.getCurrentUser();
        
        return project;
    }

    @Override
    @Transactional
    public Project analyzeProject(Long projectId) {
        Project project = getProjectById(projectId);
        
        // 更新项目状态
        project.setStatus("ANALYZING");
        project.setUpdatedAt(LocalDateTime.now());
        projectRepository.save(project);
        
        // TODO: 实现实际的代码分析逻辑
        // 这里应该调用代码分析服务，可能是异步操作
        
        // 模拟分析完成
        project.setStatus("COMPLETED");
        project.setLastAnalyzedAt(LocalDateTime.now());
        project.setAnalysisCount(project.getAnalysisCount() + 1);
        
        return projectRepository.save(project);
    }

    @Override
    @Transactional
    public Project generateDocumentation(Long projectId) {
        Project project = getProjectById(projectId);
        
        // 检查项目是否已分析
        if (!"COMPLETED".equals(project.getStatus())) {
            throw new IllegalStateException("项目需要先完成代码分析才能生成文档");
        }
        
        // TODO: 实现文档生成逻辑
        // 这里应该调用文档生成服务
        
        // 更新文档版本
        project.setDocumentationVersion(project.getDocumentationVersion() + 1);
        project.setUpdatedAt(LocalDateTime.now());
        
        return projectRepository.save(project);
    }
} 