package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.valueobject.TaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务队列服务默认实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskQueueServiceImpl implements TaskQueueService {

    private final TaskRepository taskRepository;

    @Override
    public Task enqueueTask(Task task) {
        log.info("将任务添加到队列: {}", task.getName());

        // 设置任务初始状态和时间
        if (task.getStatus() == null) {
            task.setStatus(TaskStatus.PENDING);
        }

        if (task.getCreatedAt() == null) {
            task.setCreatedAt(LocalDateTime.now());
        }

        task.setUpdatedAt(LocalDateTime.now());

        // 保存任务
        return taskRepository.save(task);
    }

    @Override
    public Optional<Task> dequeueTask() {
        log.debug("从队列获取下一个待执行任务");
        return taskRepository.findNextPendingTask();
    }

    @Override
    public Optional<Task> dequeueTaskByType(String taskType) {
        log.debug("从队列获取下一个类型为 {} 的待执行任务", taskType);
        return taskRepository.findNextPendingTaskByType(taskType);
    }

    @Override
    public List<Task> getPendingTasks() {
        log.debug("获取所有待执行任务");
        return taskRepository.findAllByStatus(TaskStatus.PENDING);
    }

    @Override
    public List<Task> getPendingTasksByType(String taskType) {
        log.debug("获取所有类型为 {} 的待执行任务", taskType);
        return taskRepository.findAllByStatusAndTaskType(TaskStatus.PENDING, taskType);
    }

    @Override
    public List<Task> getPendingTasksByProject(Long projectId) {
        log.debug("获取项目 {} 的所有待执行任务", projectId);
        return taskRepository.findAllByStatusAndProjectId(TaskStatus.PENDING, projectId);
    }

    @Override
    public Task updateTaskStatus(Long taskId, String status) {
        log.info("更新任务 {} 状态为 {}", taskId, status);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

        TaskStatus taskStatus;
        try {
            taskStatus = TaskStatus.valueOf(status);
        } catch (IllegalArgumentException e) {
            log.warn("无效的任务状态: {}, 使用详细状态代替", status);
            task.setDetailedStatus(status);
            taskStatus = TaskStatus.IN_PROGRESS; // 默认设置为进行中
        }

        task.setStatus(taskStatus);
        task.setUpdatedAt(LocalDateTime.now());

        return taskRepository.save(task);
    }

    @Override
    public Task updateTaskProgress(Long taskId, Integer progress) {
        log.debug("更新任务 {} 进度为 {}%", taskId, progress);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

        // 验证进度值
        if (progress < 0 || progress > 100) {
            throw new IllegalArgumentException("进度值必须在0-100之间");
        }

        task.setProgress(progress);
        task.setUpdatedAt(LocalDateTime.now());

        // 如果进度为100%，自动更新状态为已完成
        if (progress == 100 && task.getStatus() != TaskStatus.COMPLETED) {
            task.setStatus(TaskStatus.COMPLETED);
            log.info("任务 {} 进度达到100%，自动更新状态为已完成", taskId);
        }

        return taskRepository.save(task);
    }

    @Override
    public Task recordTaskError(Long taskId, String errorMessage) {
        log.warn("记录任务 {} 错误: {}", taskId, errorMessage);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

        task.setErrorLog(errorMessage);
        task.setStatus(TaskStatus.FAILED);
        task.setUpdatedAt(LocalDateTime.now());

        return taskRepository.save(task);
    }

    @Override
    public Task completeTask(Long taskId, String result) {
        log.info("完成任务 {}", taskId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

        task.setStatus(TaskStatus.COMPLETED);
        task.setProgress(100);
        task.setResult(result);
        task.setUpdatedAt(LocalDateTime.now());

        return taskRepository.save(task);
    }

    @Override
    public Task cancelTask(Long taskId) {
        log.info("取消任务 {}", taskId);

        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

        task.setStatus(TaskStatus.CANCELLED);
        task.setUpdatedAt(LocalDateTime.now());

        return taskRepository.save(task);
    }

    @Override
    public Optional<Task> getTaskById(Long taskId) {
        log.debug("获取任务 {} 详情", taskId);
        return taskRepository.findById(taskId);
    }

    @Override
    public int cleanupExpiredTasks(int days) {
        log.info("清理 {} 天前的已完成任务", days);
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        return taskRepository.deleteCompletedTasksOlderThan(cutoffDate);
    }
}
