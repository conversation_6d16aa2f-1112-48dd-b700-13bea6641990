package com.archscope.domain.service.impl;

import com.archscope.domain.model.parser.CodeChunk;
import com.archscope.domain.model.parser.ChunkType;
import com.archscope.domain.model.parser.LanguageType;
import com.archscope.domain.service.CodeChunkingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class CodeChunkingServiceImplTest {
    
    private CodeChunkingService codeChunkingService;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        codeChunkingService = new CodeChunkingServiceImpl();
    }
    
    @Test
    void testChunkJavaFile() throws IOException {
        // 创建一个Java测试文件
        String javaContent = "package com.example;\n\n" +
                "import java.util.List;\n" +
                "import java.util.ArrayList;\n\n" +
                "/**\n" +
                " * 示例类\n" +
                " */\n" +
                "public class Example {\n" +
                "    private List<String> items = new ArrayList<>();\n\n" +
                "    /**\n" +
                "     * 添加项目\n" +
                "     */\n" +
                "    public void addItem(String item) {\n" +
                "        items.add(item);\n" +
                "    }\n\n" +
                "    /**\n" +
                "     * 获取项目\n" +
                "     */\n" +
                "    public List<String> getItems() {\n" +
                "        return items;\n" +
                "    }\n" +
                "}";
        
        Path javaFile = tempDir.resolve("Example.java");
        Files.writeString(javaFile, javaContent);
        
        // 设置文件大小超过阈值
        // 注意：这里我们直接使用chunkContent方法，因为chunkFile方法会检查文件大小
        List<CodeChunk> chunks = codeChunkingService.chunkContent(javaContent, "Example.java", LanguageType.JAVA);
        
        // 验证结果
        assertNotNull(chunks);
        assertFalse(chunks.isEmpty());
        
        // 应该至少有一个类级别的代码块
        boolean hasClassChunk = false;
        for (CodeChunk chunk : chunks) {
            if (chunk.getType() == ChunkType.CLASS) {
                hasClassChunk = true;
                assertEquals("Example.java#Example", chunk.getId());
                assertTrue(chunk.getContent().contains("public class Example"));
                break;
            }
        }
        assertTrue(hasClassChunk);
        
        // 应该有方法级别的代码块
        boolean hasMethodChunks = false;
        for (CodeChunk chunk : chunks) {
            if (chunk.getType() == ChunkType.METHOD) {
                hasMethodChunks = true;
                assertTrue(chunk.getId().startsWith("Example.java#Example."));
                break;
            }
        }
        assertTrue(hasMethodChunks);
        
        // 验证上下文信息
        for (CodeChunk chunk : chunks) {
            assertNotNull(chunk.getContext());
            assertTrue(chunk.getContext().contains("package com.example;"));
            assertTrue(chunk.getContext().contains("import java.util.List;"));
        }
    }
    
    @Test
    void testChunkJavaScriptFile() throws IOException {
        // 创建一个JavaScript测试文件
        String jsContent = "import React from 'react';\n" +
                "import { useState } from 'react';\n\n" +
                "/**\n" +
                " * 示例组件\n" +
                " */\n" +
                "class ExampleComponent extends React.Component {\n" +
                "    constructor(props) {\n" +
                "        super(props);\n" +
                "        this.state = { count: 0 };\n" +
                "    }\n\n" +
                "    increment() {\n" +
                "        this.setState({ count: this.state.count + 1 });\n" +
                "    }\n\n" +
                "    render() {\n" +
                "        return (\n" +
                "            <div>\n" +
                "                <p>Count: {this.state.count}</p>\n" +
                "                <button onClick={() => this.increment()}>Increment</button>\n" +
                "            </div>\n" +
                "        );\n" +
                "    }\n" +
                "}\n\n" +
                "function useCounter() {\n" +
                "    const [count, setCount] = useState(0);\n" +
                "    const increment = () => setCount(count + 1);\n" +
                "    return { count, increment };\n" +
                "}";
        
        Path jsFile = tempDir.resolve("ExampleComponent.js");
        Files.writeString(jsFile, jsContent);
        
        // 设置文件大小超过阈值
        // 注意：这里我们直接使用chunkContent方法，因为chunkFile方法会检查文件大小
        List<CodeChunk> chunks = codeChunkingService.chunkContent(jsContent, "ExampleComponent.js", LanguageType.JAVASCRIPT);
        
        // 验证结果
        assertNotNull(chunks);
        assertFalse(chunks.isEmpty());
        
        // 应该至少有一个类级别的代码块
        boolean hasClassChunk = false;
        for (CodeChunk chunk : chunks) {
            if (chunk.getType() == ChunkType.CLASS) {
                hasClassChunk = true;
                assertEquals("ExampleComponent.js#ExampleComponent", chunk.getId());
                assertTrue(chunk.getContent().contains("class ExampleComponent extends React.Component"));
                break;
            }
        }
        assertTrue(hasClassChunk);
        
        // 应该有函数级别的代码块
        boolean hasFunctionChunk = false;
        for (CodeChunk chunk : chunks) {
            if (chunk.getType() == ChunkType.FUNCTION) {
                hasFunctionChunk = true;
                assertEquals("ExampleComponent.js#useCounter", chunk.getId());
                assertTrue(chunk.getContent().contains("function useCounter()"));
                break;
            }
        }
        assertTrue(hasFunctionChunk);
        
        // 验证上下文信息
        for (CodeChunk chunk : chunks) {
            assertNotNull(chunk.getContext());
            assertTrue(chunk.getContext().contains("import React from 'react';"));
        }
    }
    
    @Test
    void testChunkTypeScriptFile() throws IOException {
        // 创建一个TypeScript测试文件
        String tsContent = "import React from 'react';\n" +
                "import { useState } from 'react';\n\n" +
                "interface Props {\n" +
                "    initialCount: number;\n" +
                "}\n\n" +
                "interface State {\n" +
                "    count: number;\n" +
                "}\n\n" +
                "/**\n" +
                " * 示例组件\n" +
                " */\n" +
                "class ExampleComponent extends React.Component<Props, State> {\n" +
                "    constructor(props: Props) {\n" +
                "        super(props);\n" +
                "        this.state = { count: props.initialCount };\n" +
                "    }\n\n" +
                "    increment(): void {\n" +
                "        this.setState({ count: this.state.count + 1 });\n" +
                "    }\n\n" +
                "    render(): React.ReactNode {\n" +
                "        return (\n" +
                "            <div>\n" +
                "                <p>Count: {this.state.count}</p>\n" +
                "                <button onClick={() => this.increment()}>Increment</button>\n" +
                "            </div>\n" +
                "        );\n" +
                "    }\n" +
                "}\n\n" +
                "function useCounter(initialCount: number = 0) {\n" +
                "    const [count, setCount] = useState(initialCount);\n" +
                "    const increment = () => setCount(count + 1);\n" +
                "    return { count, increment };\n" +
                "}";
        
        Path tsFile = tempDir.resolve("ExampleComponent.ts");
        Files.writeString(tsFile, tsContent);
        
        // 设置文件大小超过阈值
        // 注意：这里我们直接使用chunkContent方法，因为chunkFile方法会检查文件大小
        List<CodeChunk> chunks = codeChunkingService.chunkContent(tsContent, "ExampleComponent.ts", LanguageType.TYPESCRIPT);
        
        // 验证结果
        assertNotNull(chunks);
        assertFalse(chunks.isEmpty());
        
        // 应该至少有一个接口级别的代码块
        boolean hasInterfaceChunk = false;
        for (CodeChunk chunk : chunks) {
            if (chunk.getType() == ChunkType.INTERFACE) {
                hasInterfaceChunk = true;
                assertTrue(chunk.getId().equals("ExampleComponent.ts#Props") || chunk.getId().equals("ExampleComponent.ts#State"));
                assertTrue(chunk.getContent().contains("interface Props") || chunk.getContent().contains("interface State"));
                break;
            }
        }
        assertTrue(hasInterfaceChunk);
        
        // 应该有类级别的代码块
        boolean hasClassChunk = false;
        for (CodeChunk chunk : chunks) {
            if (chunk.getType() == ChunkType.CLASS) {
                hasClassChunk = true;
                assertEquals("ExampleComponent.ts#ExampleComponent", chunk.getId());
                assertTrue(chunk.getContent().contains("class ExampleComponent extends React.Component<Props, State>"));
                break;
            }
        }
        assertTrue(hasClassChunk);
        
        // 应该有函数级别的代码块
        boolean hasFunctionChunk = false;
        for (CodeChunk chunk : chunks) {
            if (chunk.getType() == ChunkType.FUNCTION) {
                hasFunctionChunk = true;
                assertEquals("ExampleComponent.ts#useCounter", chunk.getId());
                assertTrue(chunk.getContent().contains("function useCounter(initialCount: number = 0)"));
                break;
            }
        }
        assertTrue(hasFunctionChunk);
        
        // 验证上下文信息
        for (CodeChunk chunk : chunks) {
            assertNotNull(chunk.getContext());
            assertTrue(chunk.getContext().contains("import React from 'react';"));
        }
    }
    
    @Test
    void testExtractContext() {
        // Java上下文
        String javaContent = "package com.example;\n\n" +
                "import java.util.List;\n" +
                "import java.util.ArrayList;\n\n" +
                "public class Example {\n" +
                "    // 类内容\n" +
                "}";
        
        String javaContext = codeChunkingService.extractContext(javaContent, LanguageType.JAVA);
        assertNotNull(javaContext);
        assertTrue(javaContext.contains("package com.example;"));
        assertTrue(javaContext.contains("import java.util.List;"));
        assertTrue(javaContext.contains("import java.util.ArrayList;"));
        
        // JavaScript上下文
        String jsContent = "import React from 'react';\n" +
                "import { useState } from 'react';\n\n" +
                "class ExampleComponent extends React.Component {\n" +
                "    // 类内容\n" +
                "}";
        
        String jsContext = codeChunkingService.extractContext(jsContent, LanguageType.JAVASCRIPT);
        assertNotNull(jsContext);
        assertTrue(jsContext.contains("import React from 'react';"));
        assertTrue(jsContext.contains("import { useState } from 'react';"));
        
        // TypeScript上下文
        String tsContent = "import React from 'react';\n" +
                "import { useState } from 'react';\n\n" +
                "type CounterProps = {\n" +
                "    initialCount: number;\n" +
                "};\n\n" +
                "class ExampleComponent extends React.Component<CounterProps> {\n" +
                "    // 类内容\n" +
                "}";
        
        String tsContext = codeChunkingService.extractContext(tsContent, LanguageType.TYPESCRIPT);
        assertNotNull(tsContext);
        assertTrue(tsContext.contains("import React from 'react';"));
        assertTrue(tsContext.contains("import { useState } from 'react';"));
        assertTrue(tsContext.contains("type CounterProps = {"));
    }
}
