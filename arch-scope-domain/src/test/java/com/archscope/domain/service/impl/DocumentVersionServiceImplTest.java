package com.archscope.domain.service.impl;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.repository.DocumentVersionRepository;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.domain.valueobject.DocumentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentVersionServiceImplTest {
    
    @Mock
    private DocumentVersionRepository documentVersionRepository;
    
    private DocumentVersionService documentVersionService;
    
    @BeforeEach
    void setUp() {
        documentVersionService = new DocumentVersionServiceImpl(documentVersionRepository);
    }
    
    @Test
    void testCreateDocumentVersion() {
        // 准备测试数据
        DocumentVersion documentVersion = createSampleDocumentVersion();
        
        // 模拟行为
        when(documentVersionRepository.save(any(DocumentVersion.class))).thenReturn(documentVersion);
        
        // 执行测试
        DocumentVersion result = documentVersionService.createDocumentVersion(documentVersion);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(documentVersion.getProjectId(), result.getProjectId());
        assertEquals(documentVersion.getCommitId(), result.getCommitId());
        assertEquals(documentVersion.getDocType(), result.getDocType());
        
        // 验证方法调用
        verify(documentVersionRepository).save(any(DocumentVersion.class));
    }
    
    @Test
    void testGetDocumentVersionById() {
        // 准备测试数据
        Long id = 1L;
        DocumentVersion documentVersion = createSampleDocumentVersion();
        documentVersion.setId(id);
        
        // 模拟行为
        when(documentVersionRepository.findById(id)).thenReturn(Optional.of(documentVersion));
        
        // 执行测试
        Optional<DocumentVersion> result = documentVersionService.getDocumentVersionById(id);
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(id, result.get().getId());
        
        // 验证方法调用
        verify(documentVersionRepository).findById(id);
    }
    
    @Test
    void testGetDocumentVersionsByProjectId() {
        // 准备测试数据
        Long projectId = 1L;
        List<DocumentVersion> documentVersions = Arrays.asList(
                createSampleDocumentVersion(),
                createSampleDocumentVersion()
        );
        
        // 模拟行为
        when(documentVersionRepository.findByProjectId(projectId)).thenReturn(documentVersions);
        
        // 执行测试
        List<DocumentVersion> result = documentVersionService.getDocumentVersionsByProjectId(projectId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证方法调用
        verify(documentVersionRepository).findByProjectId(projectId);
    }
    
    @Test
    void testGetDocumentVersionsByProjectIdAndType() {
        // 准备测试数据
        Long projectId = 1L;
        DocumentType docType = DocumentType.ARCHITECTURE;
        List<DocumentVersion> documentVersions = Arrays.asList(
                createSampleDocumentVersion(),
                createSampleDocumentVersion()
        );
        
        // 模拟行为
        when(documentVersionRepository.findByProjectIdAndDocType(projectId, docType)).thenReturn(documentVersions);
        
        // 执行测试
        List<DocumentVersion> result = documentVersionService.getDocumentVersionsByProjectIdAndType(projectId, docType);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证方法调用
        verify(documentVersionRepository).findByProjectIdAndDocType(projectId, docType);
    }
    
    @Test
    void testGetLatestDocumentVersionByProjectIdAndType() {
        // 准备测试数据
        Long projectId = 1L;
        DocumentType docType = DocumentType.ARCHITECTURE;
        DocumentVersion documentVersion = createSampleDocumentVersion();
        
        // 模拟行为
        when(documentVersionRepository.findLatestByProjectIdAndDocType(projectId, docType)).thenReturn(Optional.of(documentVersion));
        
        // 执行测试
        Optional<DocumentVersion> result = documentVersionService.getLatestDocumentVersionByProjectIdAndType(projectId, docType);
        
        // 验证结果
        assertTrue(result.isPresent());
        
        // 验证方法调用
        verify(documentVersionRepository).findLatestByProjectIdAndDocType(projectId, docType);
    }
    
    @Test
    void testUpdateDocumentVersion() {
        // 准备测试数据
        DocumentVersion documentVersion = createSampleDocumentVersion();
        documentVersion.setId(1L);
        
        // 模拟行为
        when(documentVersionRepository.update(any(DocumentVersion.class))).thenReturn(documentVersion);
        
        // 执行测试
        DocumentVersion result = documentVersionService.updateDocumentVersion(documentVersion);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证方法调用
        verify(documentVersionRepository).update(any(DocumentVersion.class));
    }
    
    @Test
    void testDeleteDocumentVersion() {
        // 准备测试数据
        Long id = 1L;
        
        // 执行测试
        documentVersionService.deleteDocumentVersion(id);
        
        // 验证方法调用
        verify(documentVersionRepository).delete(id);
    }
    
    @Test
    void testDeleteDocumentVersionsByProjectId() {
        // 准备测试数据
        Long projectId = 1L;
        
        // 模拟行为
        when(documentVersionRepository.deleteByProjectId(projectId)).thenReturn(5);
        
        // 执行测试
        int result = documentVersionService.deleteDocumentVersionsByProjectId(projectId);
        
        // 验证结果
        assertEquals(5, result);
        
        // 验证方法调用
        verify(documentVersionRepository).deleteByProjectId(projectId);
    }
    
    @Test
    void testPublishDocumentVersion() {
        // 准备测试数据
        Long id = 1L;
        DocumentVersion documentVersion = createSampleDocumentVersion();
        documentVersion.setId(id);
        documentVersion.setIsPublished(false);
        documentVersion.setStatus("DRAFT");
        
        // 模拟行为
        when(documentVersionRepository.findById(id)).thenReturn(Optional.of(documentVersion));
        when(documentVersionRepository.update(any(DocumentVersion.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // 执行测试
        DocumentVersion result = documentVersionService.publishDocumentVersion(id);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getIsPublished());
        assertEquals("PUBLISHED", result.getStatus());
        
        // 验证方法调用
        verify(documentVersionRepository).findById(id);
        verify(documentVersionRepository).update(any(DocumentVersion.class));
    }
    
    @Test
    void testUnpublishDocumentVersion() {
        // 准备测试数据
        Long id = 1L;
        DocumentVersion documentVersion = createSampleDocumentVersion();
        documentVersion.setId(id);
        documentVersion.setIsPublished(true);
        documentVersion.setStatus("PUBLISHED");
        
        // 模拟行为
        when(documentVersionRepository.findById(id)).thenReturn(Optional.of(documentVersion));
        when(documentVersionRepository.update(any(DocumentVersion.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // 执行测试
        DocumentVersion result = documentVersionService.unpublishDocumentVersion(id);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.getIsPublished());
        assertEquals("DRAFT", result.getStatus());
        
        // 验证方法调用
        verify(documentVersionRepository).findById(id);
        verify(documentVersionRepository).update(any(DocumentVersion.class));
    }
    
    @Test
    void testGenerateVersionTag_FirstVersion() {
        // 准备测试数据
        Long projectId = 1L;
        DocumentType docType = DocumentType.ARCHITECTURE;
        
        // 模拟行为
        when(documentVersionRepository.findLatestByProjectIdAndDocType(projectId, docType)).thenReturn(Optional.empty());
        
        // 执行测试
        String result = documentVersionService.generateVersionTag(projectId, docType);
        
        // 验证结果
        assertEquals("v1.0.0", result);
        
        // 验证方法调用
        verify(documentVersionRepository).findLatestByProjectIdAndDocType(projectId, docType);
    }
    
    @Test
    void testGenerateVersionTag_IncrementPatch() {
        // 准备测试数据
        Long projectId = 1L;
        DocumentType docType = DocumentType.ARCHITECTURE;
        DocumentVersion documentVersion = createSampleDocumentVersion();
        documentVersion.setVersionTag("v1.0.0");
        
        // 模拟行为
        when(documentVersionRepository.findLatestByProjectIdAndDocType(projectId, docType)).thenReturn(Optional.of(documentVersion));
        
        // 执行测试
        String result = documentVersionService.generateVersionTag(projectId, docType);
        
        // 验证结果
        assertEquals("v1.0.1", result);
        
        // 验证方法调用
        verify(documentVersionRepository).findLatestByProjectIdAndDocType(projectId, docType);
    }
    
    @Test
    void testGenerateVersionTag_IncrementMinor() {
        // 准备测试数据
        Long projectId = 1L;
        DocumentType docType = DocumentType.ARCHITECTURE;
        DocumentVersion documentVersion = createSampleDocumentVersion();
        documentVersion.setVersionTag("v1.0.9");
        
        // 模拟行为
        when(documentVersionRepository.findLatestByProjectIdAndDocType(projectId, docType)).thenReturn(Optional.of(documentVersion));
        
        // 执行测试
        String result = documentVersionService.generateVersionTag(projectId, docType);
        
        // 验证结果
        assertEquals("v1.1.0", result);
        
        // 验证方法调用
        verify(documentVersionRepository).findLatestByProjectIdAndDocType(projectId, docType);
    }
    
    @Test
    void testGenerateVersionTag_IncrementMajor() {
        // 准备测试数据
        Long projectId = 1L;
        DocumentType docType = DocumentType.ARCHITECTURE;
        DocumentVersion documentVersion = createSampleDocumentVersion();
        documentVersion.setVersionTag("v1.9.9");
        
        // 模拟行为
        when(documentVersionRepository.findLatestByProjectIdAndDocType(projectId, docType)).thenReturn(Optional.of(documentVersion));
        
        // 执行测试
        String result = documentVersionService.generateVersionTag(projectId, docType);
        
        // 验证结果
        assertEquals("v2.0.0", result);
        
        // 验证方法调用
        verify(documentVersionRepository).findLatestByProjectIdAndDocType(projectId, docType);
    }
    
    /**
     * 创建示例文档版本
     * 
     * @return 文档版本
     */
    private DocumentVersion createSampleDocumentVersion() {
        return DocumentVersion.builder()
                .projectId(1L)
                .commitId("abc123")
                .docType(DocumentType.ARCHITECTURE)
                .contentPath("/path/to/content")
                .timestamp(LocalDateTime.now())
                .versionTag("v1.0.0")
                .title("架构设计文档")
                .description("自动生成的架构设计文档")
                .author("系统")
                .lastModified(LocalDateTime.now())
                .isPublished(false)
                .status("DRAFT")
                .build();
    }
}
