package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectDTO {
    private Long id;
    private String name;
    private String description;
    private String repositoryUrl;
    private String branch;
    private String status;
    private Double rating;
    private Long linesOfCode;
    private Integer fileCount;
    private Integer contributorCount;
    private String icon;
    private String type;
    private List<Long> memberIds;
    private List<Long> documentIds;
    private List<Long> taskIds;
    private Long creatorId;
    private Boolean active;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastAnalyzedAt;
} 