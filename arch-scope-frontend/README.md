# 架构鹰眼 ArchScope - 前端项目

这是架构鹰眼ArchScope系统的前端项目，基于Vue 3 + TypeScript + Tailwind CSS开发。

## 技术栈

- Vue 3
- TypeScript
- Vue Router
- Pinia 状态管理
- Tailwind CSS
- Vite

## 开发环境配置

### 安装依赖

```bash
npm install
```

### 本地开发服务

```bash
npm run dev
```

### 编译生产环境代码

```bash
npm run build
```

### 代码检查和修复

```bash
npm run lint
```

## 项目结构

> 界面原型在 /docs/prototype/ 目录下面

```
/arch-scope-frontend/
├── public/             # 静态资源
├── src/
│   ├── assets/         # 静态文件 (图片、字体等)
│   ├── components/     # 可复用组件
│   ├── layouts/        # 页面布局组件
│   ├── router/         # Vue Router 配置
│   ├── stores/         # Pinia 状态管理
│   ├── views/          # 页面组件
│   │   ├── projects/   # 项目相关页面
│   │   ├── tasks/      # 任务相关页面
│   │   └── ...
│   ├── App.vue         # 根组件
│   ├── main.ts         # 入口文件
│   └── styles/         # 全局样式
├── index.html          # HTML入口
├── vite.config.ts      # Vite 配置
├── tsconfig.json       # TypeScript 配置
└── ...
```

## API 接口

前端通过后端API接口获取数据，主要包括：

- `/api/projects` - 项目管理相关API
- `/api/tasks` - 任务管理相关API
- `/api/analysis` - 代码分析相关API
- `/api/docs` - 文档生成相关API

详细API文档请参考后端开发指南。

## 开发规范

- 组件采用PascalCase命名（如ProjectList.vue）
- TypeScript类型定义清晰
- 使用Composition API和setup语法糖
- 使用Pinia进行状态管理
- 页面响应式设计，支持移动端访问 