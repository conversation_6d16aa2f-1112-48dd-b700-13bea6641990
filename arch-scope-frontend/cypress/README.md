# 任务管理页面 E2E 测试

本目录包含了任务管理页面的端到端（E2E）测试，使用 Cypress 框架实现。

## 测试覆盖范围

### 任务列表页面 (`task-management.cy.ts`)

#### 核心功能测试
- ✅ 页面基础功能
  - 页面正确加载
  - 表格列标题显示
  - 任务数据显示
- ✅ 搜索功能
  - 按项目名称搜索
  - 按任务名称搜索
  - 搜索不存在的内容
  - 清空搜索框
- ✅ 状态筛选功能
  - 按不同状态筛选任务
  - 搜索和筛选组合使用
- ✅ 任务详情跳转
  - 点击任务行跳转
  - 点击详情按钮跳转
- ✅ 刷新功能
- ✅ 分页信息显示
- ✅ 响应式设计测试
- ✅ 错误处理

### 任务详情页面 (`task-detail.cy.ts`)

#### 核心功能测试
- ✅ 页面基础功能
  - 页面正确加载
  - 任务ID在URL中显示
  - 返回按钮功能
- ✅ 任务信息显示
  - 任务基本信息
  - 任务时间信息
  - 任务进度信息
- ✅ 任务状态处理
  - 处理中任务状态
  - 已完成任务状态
  - 失败任务状态
- ✅ 任务日志和输出
  - 执行日志显示
  - 日志刷新功能
  - 输出结果显示
- ✅ 任务操作
  - 重新执行失败任务
  - 取消正在执行的任务
  - 删除任务
- ✅ 实时更新
- ✅ 错误处理
- ✅ 响应式设计测试

## 测试数据

### Fixtures 文件
- `tasks.json` - 模拟任务数据和测试用例
- `task-processing.json` - 处理中的任务数据
- `task-completed.json` - 已完成的任务数据
- `task-failed.json` - 失败的任务数据

### 自定义命令

在 `cypress/support/commands.ts` 中定义了以下自定义命令：

- `cy.visitTaskList()` - 访问任务列表页面
- `cy.visitTaskDetail(taskId)` - 访问任务详情页面
- `cy.searchTasks(searchTerm)` - 搜索任务
- `cy.filterTasksByStatus(status)` - 按状态筛选任务
- `cy.waitForPageLoad()` - 等待页面加载完成
- `cy.checkTaskTableExists()` - 检查任务表格是否存在
- `cy.getTaskRowCount()` - 获取任务行数量

## 运行测试

### 前提条件

1. 确保前端开发服务器正在运行：
```bash
npm run dev
```

2. 确保后端API服务器正在运行（可选，测试中会模拟API响应）

### 运行命令

#### 1. 交互式运行（推荐用于开发）
```bash
# 打开 Cypress 测试界面
npm run test:e2e:open

# 或者同时启动开发服务器和测试界面
npm run test:e2e:dev:open
```

#### 2. 无头模式运行（推荐用于CI/CD）
```bash
# 运行所有 E2E 测试
npm run test:e2e

# 或者同时启动开发服务器和运行测试
npm run test:e2e:dev
```

#### 3. 运行特定测试文件
```bash
# 只运行任务管理页面测试
npx cypress run --spec "cypress/e2e/task-management.cy.ts"

# 只运行任务详情页面测试
npx cypress run --spec "cypress/e2e/task-detail.cy.ts"
```

## 测试配置

### Cypress 配置 (`cypress.config.ts`)

- **baseUrl**: `http://localhost:3000`
- **viewportWidth**: 1280
- **viewportHeight**: 720
- **defaultCommandTimeout**: 10000ms
- **video**: 关闭（节省空间）
- **screenshotOnRunFailure**: 开启

### 环境变量

可以通过环境变量自定义测试配置：

```bash
# 自定义基础URL
CYPRESS_baseUrl=http://localhost:3001 npm run test:e2e

# 启用视频录制
CYPRESS_video=true npm run test:e2e
```

## 测试最佳实践

### 1. 使用 data-cy 属性
所有测试都使用 `data-cy` 属性来选择元素，确保测试的稳定性：

```html
<button data-cy="refresh-button">刷新任务</button>
<input data-cy="search-input" placeholder="搜索任务..." />
<table data-cy="task-table">...</table>
```

### 2. 模拟API响应
使用 `cy.intercept()` 模拟API响应，确保测试的独立性：

```javascript
cy.intercept('GET', '/api/tasks', { fixture: 'tasks.json' }).as('getTasks')
```

### 3. 等待异步操作
使用自定义命令等待页面加载和异步操作完成：

```javascript
cy.visitTaskList()
cy.waitForPageLoad()
```

### 4. 测试不同设备尺寸
测试响应式设计：

```javascript
cy.viewport('iphone-6')
cy.viewport('ipad-2')
```

## 故障排除

### 常见问题

1. **测试超时**
   - 检查开发服务器是否正在运行
   - 增加 `defaultCommandTimeout` 配置

2. **元素未找到**
   - 确保页面已完全加载
   - 检查 `data-cy` 属性是否正确

3. **API请求失败**
   - 检查API模拟配置
   - 确保fixture文件存在

### 调试技巧

1. 使用 `cy.debug()` 暂停测试执行
2. 使用 `cy.screenshot()` 截图调试
3. 在交互式模式下查看测试执行过程

## 持续集成

在CI/CD流水线中运行测试：

```yaml
# GitHub Actions 示例
- name: Run E2E Tests
  run: |
    npm ci
    npm run build
    npm run test:e2e:dev
```

## 贡献指南

添加新测试时请遵循以下规范：

1. 使用描述性的测试名称
2. 添加适当的 `data-cy` 属性
3. 使用自定义命令提高代码复用
4. 添加必要的fixture数据
5. 测试正常流程和异常情况
6. 更新本README文档
