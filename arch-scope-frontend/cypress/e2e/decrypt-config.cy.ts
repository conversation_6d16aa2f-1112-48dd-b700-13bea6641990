describe('解密配置功能测试', () => {
  beforeEach(() => {
    // 访问项目详情页面
    cy.visit('/projects/1')
  })

  it('应该显示解密配置按钮', () => {
    // 检查解密配置按钮是否存在
    cy.contains('解密配置（已启用）').should('be.visible')
  })

  it('应该能够打开解密配置弹窗', () => {
    // 点击解密配置按钮
    cy.contains('解密配置（已启用）').click()
    
    // 检查弹窗是否打开
    cy.get('[role="dialog"]').should('be.visible')
    cy.contains('解密配置').should('be.visible')
  })

  it('启用解密功能选项应该默认选中', () => {
    // 打开解密配置弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 检查"启用解密功能"复选框是否选中
    cy.get('#enable-decrypt').should('be.checked')
    
    // 检查标签文本
    cy.contains('启用解密功能').should('be.visible')
  })

  it('应该能够配置解密参数', () => {
    // 打开解密配置弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 确保启用解密功能是选中的
    cy.get('#enable-decrypt').should('be.checked')
    
    // 输入解密密钥
    cy.get('#decrypt-key').type('test-secret-key')
    
    // 选择解密算法
    cy.get('#decrypt-algorithm').select('AES-128')
    
    // 输入配置文件路径
    cy.get('#config-path').type('/config/test.properties')
    
    // 保存配置
    cy.contains('保存配置').click()
    
    // 检查弹窗是否关闭
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('应该能够切换密钥可见性', () => {
    // 打开解密配置弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 输入密钥
    cy.get('#decrypt-key').type('secret-password')
    
    // 检查密钥是否隐藏
    cy.get('#decrypt-key').should('have.attr', 'type', 'password')
    
    // 点击显示/隐藏按钮
    cy.get('#decrypt-key').parent().find('button').click()
    
    // 检查密钥是否显示
    cy.get('#decrypt-key').should('have.attr', 'type', 'text')
    
    // 再次点击隐藏
    cy.get('#decrypt-key').parent().find('button').click()
    cy.get('#decrypt-key').should('have.attr', 'type', 'password')
  })

  it('应该能够取消配置', () => {
    // 打开解密配置弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 点击取消按钮
    cy.contains('取消').click()
    
    // 检查弹窗是否关闭
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('应该在启用解密功能时显示相关配置项', () => {
    // 打开解密配置弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 确保启用解密功能是选中的
    cy.get('#enable-decrypt').should('be.checked')
    
    // 检查相关配置项是否显示
    cy.get('#decrypt-key').should('be.visible')
    cy.get('#decrypt-algorithm').should('be.visible')
    cy.get('#config-path').should('be.visible')
    
    // 取消选中启用解密功能
    cy.get('#enable-decrypt').uncheck()
    
    // 检查相关配置项是否隐藏
    cy.get('#decrypt-key').should('not.exist')
    cy.get('#decrypt-algorithm').should('not.exist')
    cy.get('#config-path').should('not.exist')
  })

  it('应该验证必填字段', () => {
    // 打开解密配置弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 确保启用解密功能是选中的
    cy.get('#enable-decrypt').should('be.checked')
    
    // 不输入密钥，直接保存
    cy.contains('保存配置').click()
    
    // 应该显示验证错误（这里假设会有alert或其他提示）
    // 注意：实际实现中可能需要根据具体的错误提示方式调整
  })

  it('应该保持弹窗打开时的状态一致性', () => {
    // 打开解密配置弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 验证初始状态
    cy.get('#enable-decrypt').should('be.checked')
    cy.get('#decrypt-algorithm').should('have.value', 'AES-256')
    
    // 关闭弹窗
    cy.contains('取消').click()
    
    // 重新打开弹窗
    cy.contains('解密配置（已启用）').click()
    
    // 验证状态保持一致
    cy.get('#enable-decrypt').should('be.checked')
    cy.get('#decrypt-algorithm').should('have.value', 'AES-256')
  })
})
