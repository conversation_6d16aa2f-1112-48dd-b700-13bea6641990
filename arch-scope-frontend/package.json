{"name": "arch-scope-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:dev": "start-server-and-test dev http://localhost:3000 'cypress run'", "test:e2e:dev:open": "start-server-and-test dev http://localhost:3000 'cypress open'"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "axios": "^1.6.7", "element-plus": "^2.9.9", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@cypress/vite-dev-server": "^6.0.3", "@cypress/vue": "^6.0.2", "@types/node": "^20.11.30", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.18", "cypress": "^14.4.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "postcss": "^8.4.36", "start-server-and-test": "^2.0.12", "tailwindcss": "^3.4.1", "typescript": "^5.4.3", "vite": "^6.3.4", "vite-plugin-vue-mcp": "^0.3.2", "vue-tsc": "^2.0.7"}}