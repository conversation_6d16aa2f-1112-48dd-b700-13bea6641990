<template>
  <nav class="bg-gray-800 p-4">
    <div class="container mx-auto flex justify-between items-center">
      <!-- Logo and Title -->
      <router-link to="/" class="text-white flex items-center">
        <div class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-2">
          <img src="@/assets/logo.png" alt="ArchScope" class="h-8 w-8">
        </div>
        <span class="text-2xl font-bold">ArchScope</span>
      </router-link>

      <!-- 导航菜单 -->
      <div class="flex space-x-4">
        <router-link
          to="/projects"
          class="px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out"
          :class="[$route.path.includes('/projects') && !$route.path.includes('/projects/new') ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:text-white']"
        >
          项目列表
        </router-link>
        <router-link
          to="/tasks"
          class="px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out"
          :class="[$route.path.includes('/tasks') ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:text-white']"
        >
          任务队列
        </router-link>
        <router-link
          to="/projects/new"
          class="px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out"
          :class="[$route.path.includes('/projects/new') ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:text-white']"
        >
          注册项目
        </router-link>
      </div>
    </div>
  </nav>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useRoute } from 'vue-router'

export default defineComponent({
  name: 'AppHeader',

  setup() {
    const route = useRoute()

    return {
      route
    }
  }
})
</script>