<template>
  <div v-if="visible" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <!-- 背景遮罩 -->
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="closeModal"></div>

      <!-- 垂直居中的技巧 -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <!-- 弹窗内容 -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full animate-slide-up">
        <!-- 弹窗头部 -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
              <i class="fas fa-shield-alt text-indigo-600"></i>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                解密配置
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  配置项目的解密功能，用于处理加密的配置文件和敏感信息。
                </p>
              </div>
            </div>
            <button
              @click="closeModal"
              class="ml-auto flex-shrink-0 bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <span class="sr-only">关闭</span>
              <i class="fas fa-times h-6 w-6"></i>
            </button>
          </div>
        </div>

        <!-- 弹窗主体内容 -->
        <div class="bg-white px-4 pb-4 sm:p-6 sm:pt-0">
          <div class="space-y-4">
            <!-- 启用解密功能选项 -->
            <div class="flex items-center">
              <input
                id="enable-decrypt"
                v-model="config.enabled"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              >
              <label for="enable-decrypt" class="ml-3 block text-sm font-medium text-gray-700">
                启用解密功能
              </label>
            </div>

            <!-- 解密密钥配置 -->
            <div v-if="config.enabled" class="animate-slide-down">
              <label for="decrypt-key" class="block text-sm font-medium text-gray-700 mb-2">
                解密密钥
              </label>
              <div class="relative">
                <input
                  id="decrypt-key"
                  v-model="config.decryptKey"
                  :type="showKey ? 'text' : 'password'"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pr-10"
                  placeholder="请输入解密密钥"
                >
                <button
                  type="button"
                  @click="toggleKeyVisibility"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <i :class="showKey ? 'fas fa-eye-slash' : 'fas fa-eye'" class="h-5 w-5"></i>
                </button>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                用于解密配置文件中的敏感信息
              </p>
            </div>

            <!-- 解密算法选择 -->
            <div v-if="config.enabled" class="animate-slide-down" style="animation-delay: 0.1s;">
              <label for="decrypt-algorithm" class="block text-sm font-medium text-gray-700 mb-2">
                解密算法
              </label>
              <select
                id="decrypt-algorithm"
                v-model="config.algorithm"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="AES-256">AES-256</option>
                <option value="AES-128">AES-128</option>
                <option value="DES">DES</option>
              </select>
            </div>

            <!-- 配置文件路径 -->
            <div v-if="config.enabled" class="animate-slide-down" style="animation-delay: 0.2s;">
              <label for="config-path" class="block text-sm font-medium text-gray-700 mb-2">
                加密配置文件路径
              </label>
              <input
                id="config-path"
                v-model="config.configPath"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="例如: /config/encrypted.properties"
              >
              <p class="mt-1 text-xs text-gray-500">
                指定需要解密的配置文件路径
              </p>
            </div>
          </div>
        </div>

        <!-- 弹窗底部按钮 -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="button"
            @click="saveConfig"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm animate-button"
            :disabled="loading"
          >
            <span v-if="loading">保存中...</span>
            <span v-else>保存配置</span>
          </button>
          <button
            type="button"
            @click="closeModal"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'

interface DecryptConfig {
  enabled: boolean
  decryptKey: string
  algorithm: string
  configPath: string
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  projectId: {
    type: String,
    required: true
  },
  initialConfig: {
    type: Object as () => DecryptConfig,
    default: () => ({
      enabled: true, // 默认启用解密功能
      decryptKey: '',
      algorithm: 'AES-256',
      configPath: ''
    })
  }
})

const emit = defineEmits(['close', 'save'])

const loading = ref(false)
const showKey = ref(false)

const config = reactive<DecryptConfig>({
  enabled: true, // 确保默认启用
  decryptKey: '',
  algorithm: 'AES-256',
  configPath: ''
})

// 监听初始配置变化
watch(() => props.initialConfig, (newConfig) => {
  if (newConfig) {
    Object.assign(config, {
      enabled: true, // 强制启用，修复未选中问题
      ...newConfig
    })
  }
}, { immediate: true, deep: true })

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    // 弹窗打开时重置配置，确保启用状态正确
    Object.assign(config, {
      enabled: true, // 确保每次打开都是启用状态
      ...props.initialConfig
    })
  }
})

const toggleKeyVisibility = () => {
  showKey.value = !showKey.value
}

const closeModal = () => {
  emit('close')
}

const saveConfig = async () => {
  try {
    loading.value = true

    // 验证必填字段
    if (config.enabled && !config.decryptKey.trim()) {
      alert('请输入解密密钥')
      return
    }

    // 发送保存事件
    emit('save', { ...config })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    closeModal()
  } catch (error) {
    console.error('保存解密配置失败:', error)
    alert('保存失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时确保启用状态正确
onMounted(() => {
  config.enabled = true
})
</script>

<style scoped>
/* 动画效果 */
.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-button {
  transition: all 0.2s ease;
}

.animate-button:hover {
  transform: translateY(-1px);
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 复选框样式增强 */
input[type="checkbox"]:checked {
  background-color: #4F46E5;
  border-color: #4F46E5;
}

input[type="checkbox"]:focus {
  ring-color: #4F46E5;
}
</style>
