<template>
  <div class="document-content">
    <div v-if="loading" class="flex justify-center items-center py-10">
      <div class="spinner inline-block w-8 h-8 border-4 border-t-indigo-500 border-r-transparent border-b-indigo-500 border-l-transparent rounded-full animate-spin"></div>
      <p class="ml-3 text-gray-600">加载文档中...</p>
    </div>
    
    <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
      <strong class="font-bold">加载失败！</strong>
      <span class="block sm:inline"> {{ error }}</span>
    </div>
    
    <div v-else-if="!content" class="text-center py-10">
      <p class="text-gray-500">暂无文档内容</p>
    </div>
    
    <div v-else class="prose max-w-none" v-html="content"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'

export default defineComponent({
  name: 'DocumentContent',
  
  props: {
    content: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    }
  },
  
  setup() {
    return {}
  }
})
</script>

<style scoped>
.prose h1 {
  font-size: 2.25rem;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 700;
  color: #1E293B; /* Slate-800 */
}

.prose h2 {
  font-size: 1.75rem;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #334155; /* Slate-700 */
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #E2E8F0; /* Slate-200 */
}

.prose h3 {
  font-size: 1.5rem;
  margin-top: 1.75em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #475569; /* Slate-600 */
}

.prose h4 {
  font-size: 1.25rem;
  margin-top: 1.75em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #64748B; /* Slate-500 */
}

.prose ul {
  list-style: disc;
  margin-left: 1.5em;
}

.prose li {
  margin-bottom: 0.5em;
}

.prose p {
  line-height: 1.7;
  margin-bottom: 1.25em;
}

.prose code {
  background-color: #F1F5F9; /* Slate-100 */
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}
</style>
