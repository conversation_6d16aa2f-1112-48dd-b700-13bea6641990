<template>
  <div class="min-h-screen bg-gray-100 font-sans">
    <!-- 顶部导航栏 - 按照界面原型设计 -->
    <nav class="bg-gray-800 p-4">
      <div class="container mx-auto flex justify-between items-center">
        <router-link to="/projects" class="text-white flex items-center">
          <div
            class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-2"
          >
            <img src="@/assets/logo.png" alt="ArchScope" class="h-8 w-8" />
          </div>
          <span class="text-2xl font-bold">ArchScope</span>
        </router-link>
        <div class="flex space-x-4">
          <router-link
            to="/projects"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{
              'bg-indigo-600 text-white': $route.path.startsWith('/projects'),
            }"
          >
            项目列表
          </router-link>
          <router-link
            to="/tasks"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{
              'bg-indigo-600 text-white': $route.path.startsWith('/tasks'),
            }"
          >
            任务队列
          </router-link>
          <router-link
            to="/projects/new"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{
              'bg-indigo-600 text-white': $route.path === '/projects/new',
            }"
          >
            注册项目
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="min-h-screen bg-gray-100">
      <slot></slot>
    </main>
  </div>
</template>

<script setup lang="ts">
// 简化的布局组件，按照界面原型设计
</script>
