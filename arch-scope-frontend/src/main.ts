import { createApp } from 'vue'
import { createPinia } from 'pinia'
import axios from 'axios'
import App from './App.vue'
import router from './router'
// 由于使用统一的权限控制，不再需要导入 authStore
// import { useAuthStore } from './stores/auth'
import './styles/main.css'
// 引入 FontAwesome
import '@fortawesome/fontawesome-free/css/all.min.css'

// 配置Axios默认设置
axios.defaults.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8080'
axios.defaults.headers.common['Content-Type'] = 'application/json'
axios.defaults.headers.common['Accept'] = 'application/json'
axios.defaults.timeout = 30000 // 30秒超时

// 由于使用统一的权限控制，不需要添加认证令牌
// 统一权限控制系统会自动处理认证

// 创建应用实例
const app = createApp(App)

// 添加全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err)
  console.error('组件:', vm)
  console.error('信息:', info)
}

// 使用Pinia进行状态管理
const pinia = createPinia()
app.use(pinia)

// 使用Vue Router进行路由管理
app.use(router)

// 应用挂载到DOM
app.mount('#app')

// 由于使用统一的权限控制，不再需要初始化认证状态
// const authStore = useAuthStore()
// authStore.initialize().catch(error => {
//   console.error('Failed to initialize auth store:', error)
// })