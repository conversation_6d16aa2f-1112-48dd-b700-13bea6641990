import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
// 由于使用统一的权限控制，不再需要导入 authStore
// import { useAuthStore } from '@/stores/auth'

// Pages
import HomeView from '@/views/HomeView.vue'
// 由于使用统一的权限控制，不再需要登录和注册页面
// import Login from '@/views/auth/Login.vue'
// import Register from '@/views/auth/Register.vue'
import ProjectList from '@/views/projects/ProjectList.vue'
import ProjectDetail from '@/views/projects/ProjectDetail.vue'
import RegisterProject from '@/views/projects/RegisterProject.vue'
import DocumentView from '@/views/projects/DocumentView.vue'
import TaskListView from '@/views/tasks/TaskListView.vue'
import TaskDetailView from '@/views/tasks/TaskDetailView.vue'
import NotFound from '@/views/NotFound.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: HomeView
  },
  // 移除登录和注册页面，因为使用统一的权限控制
  {
    path: '/projects',
    name: 'ProjectList',
    component: ProjectList
    // 移除requiresAuth，允许直接访问
  },
  {
    path: '/projects/new',
    name: 'RegisterProject',
    component: RegisterProject
    // 移除requiresAuth，允许直接访问
  },
  {
    path: '/projects/:id',
    name: 'ProjectDetail',
    component: ProjectDetail
    // 移除requiresAuth，允许直接访问
  },
  {
    path: '/projects/:id/documents',
    name: 'DocumentView',
    component: DocumentView
    // 移除requiresAuth，允许直接访问
  },
  {
    path: '/tasks',
    name: 'TaskList',
    component: TaskListView
    // 移除requiresAuth，允许直接访问
  },
  {
    path: '/tasks/:id',
    name: 'TaskDetail',
    component: TaskDetailView
    // 移除requiresAuth，允许直接访问
  },
  {
    path: '/:catchAll(.*)',
    name: 'NotFound',
    component: NotFound
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局导航守卫
router.beforeEach((to, from, next) => {
  // 由于使用统一的权限控制，不再需要检查认证状态
  // 所有路由都允许直接访问
  next()
})

export default router