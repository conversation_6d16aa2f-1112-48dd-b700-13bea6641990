import { defineStore } from 'pinia';
import axios from 'axios';
import { ref } from 'vue';

export interface Project {
  id: number;
  name: string;
  description: string;
  repositoryUrl: string;
  branch: string;
  createdAt: string;
  updatedAt: string;
  lastAnalyzedAt: string | null;
  creatorId: number;
  status: string;
  active: boolean;
  documentationPath: string | null;
  analysisCount: number;
  documentationVersion: number;
}

export interface DocumentVersion {
  id: number;
  projectId: number;
  commitId: string;
  contentPath: string;
  timestamp: string;
  docType: string;
  versionTag: string;
  title: string;
  description: string;
  author: string;
  lastModified: string;
  isPublished: boolean;
  status: string;
}

export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref<Project[]>([]);
  const currentProject = ref<Project | null>(null);
  const loading = ref(false);

  // 获取所有项目
  const fetchProjects = async () => {
    loading.value = true;
    try {
      const response = await axios.get('/api/projects');
      projects.value = response.data;
      return projects.value;
    } finally {
      loading.value = false;
    }
  };

  // 获取项目详情
  const fetchProjectById = async (id: number) => {
    loading.value = true;
    try {
      const response = await axios.get(`/api/projects/${id}`);
      currentProject.value = response.data;
      return currentProject.value;
    } finally {
      loading.value = false;
    }
  };

  // 注册新项目
  const registerProject = async (
    name: string,
    description: string,
    repositoryUrl: string,
    branch: string = 'main'
  ) => {
    loading.value = true;
    try {
      const response = await axios.post('/api/projects/register', {
        name,
        description,
        repositoryUrl,
        branch
      });

      // 如果注册成功，刷新项目列表
      await fetchProjects();

      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 触发项目代码分析
  const analyzeProject = async (projectId: number) => {
    loading.value = true;
    try {
      const response = await axios.post(`/api/projects/${projectId}/analyze`);

      // 更新当前项目状态
      if (currentProject.value && currentProject.value.id === projectId) {
        currentProject.value = response.data;
      }

      // 更新项目列表中的项目
      const index = projects.value.findIndex(p => p.id === projectId);
      if (index !== -1) {
        projects.value[index] = response.data;
      }

      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 生成项目文档
  const generateDocumentation = async (projectId: number) => {
    loading.value = true;
    try {
      const response = await axios.post(`/api/projects/${projectId}/generate-docs`);

      // 更新当前项目状态
      if (currentProject.value && currentProject.value.id === projectId) {
        currentProject.value = response.data;
      }

      // 更新项目列表中的项目
      const index = projects.value.findIndex(p => p.id === projectId);
      if (index !== -1) {
        projects.value[index] = response.data;
      }

      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 获取项目文档版本列表
  const fetchDocumentVersions = async (projectId: number) => {
    loading.value = true;
    try {
      const response = await axios.get(`/api/document-versions/project/${projectId}`);
      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 获取文档内容
  const fetchDocumentContent = async (projectId: number, versionId: number, docType: string) => {
    loading.value = true;
    try {
      const response = await axios.get(`/api/document-versions/${versionId}/content`, {
        params: { docType }
      });
      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 比较文档版本
  const compareDocumentVersions = async (fromVersionId: number, toVersionId: number) => {
    loading.value = true;
    try {
      const response = await axios.get(`/api/document-versions/compare`, {
        params: { fromVersionId, toVersionId }
      });
      return response.data;
    } finally {
      loading.value = false;
    }
  };

  return {
    // 状态
    projects,
    currentProject,
    loading,

    // 方法
    fetchProjects,
    fetchProjectById,
    registerProject,
    analyzeProject,
    generateDocumentation,
    fetchDocumentVersions,
    fetchDocumentContent,
    compareDocumentVersions
  };
});