import { get, post, put, del } from './request';

// 定义接口类型
export interface Project {
  id: string;
  name: string;
  description: string;
  repoType: string;
  repoUrl: string;
  createdAt: string;
  updatedAt: string;
  languages: string[];
}

export interface ProjectForm {
  name: string;
  description: string;
  repoType: string;
  repoUrl: string;
  languages: string[];
}

export interface DocGeneration {
  id: string;
  version: string;
  generatedAt: string;
  summary: string;
}

export interface HealthMetric {
  name: string;
  score: number;
}

export interface HealthAssessment {
  score: number;
  metrics: HealthMetric[];
}

export interface HealthTrendPoint {
  week: string; // 周标识，如 "2024-W01"
  score: number; // 健康度分数 0-100
  date: string; // 该周的日期
}

export interface HealthTrend {
  projectId: string;
  points: HealthTrendPoint[];
  period: 'weekly' | 'daily' | 'monthly';
  totalWeeks: number;
}

export interface ProjectDetail extends Project {
  docGenerations?: DocGeneration[];
  healthAssessment?: HealthAssessment;
}

export interface Task {
  id: string;
  name: string;
  type: 'doc_generation' | 'code_analysis' | 'health_check';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  completedAt?: string;
  result?: string;
}

export interface TaskForm {
  name: string;
  type: string;
  projectId: string;
  config?: Record<string, any>;
}

// 项目管理API
export const projectAPI = {
  // 获取项目列表
  getProjects() {
    return get<{ data: Project[] }>('/projects');
  },

  // 获取项目详情
  getProjectById(id: string) {
    return get<{ data: ProjectDetail }>(`/projects/${id}`);
  },

  // 创建项目
  createProject(data: ProjectForm) {
    return post<{ data: Project }>('/projects', data);
  },

  // 更新项目
  updateProject(id: string, data: ProjectForm) {
    return put<{ data: Project }>(`/projects/${id}`, data);
  },

  // 删除项目
  deleteProject(id: string) {
    return del<{ success: boolean }>(`/projects/${id}`);
  },

  // 生成项目文档
  generateDocs(id: string) {
    return post<{ data: { taskId: string } }>(`/projects/${id}/generate-docs`);
  },

  // 获取项目健康度趋势
  getHealthTrend(id: string, weeks: number = 40) {
    return get<{ data: HealthTrend }>(`/projects/${id}/health/trend?weeks=${weeks}`);
  }
};

// 任务管理API
export const taskAPI = {
  // 获取任务列表
  getTasks(projectId: string) {
    return get<{ data: Task[] }>(`/projects/${projectId}/tasks`);
  },

  // 获取任务详情
  getTaskById(projectId: string, taskId: string) {
    return get<{ data: Task }>(`/projects/${projectId}/tasks/${taskId}`);
  },

  // 创建任务
  createTask(data: TaskForm) {
    return post<{ data: Task }>(`/projects/${data.projectId}/tasks`, data);
  },

  // 取消任务
  cancelTask(projectId: string, taskId: string) {
    return post<{ success: boolean }>(`/projects/${projectId}/tasks/${taskId}/cancel`);
  },

  // 重试任务
  retryTask(projectId: string, taskId: string) {
    return post<{ data: Task }>(`/projects/${projectId}/tasks/${taskId}/retry`);
  },

  // 获取任务结果
  getTaskResult(projectId: string, taskId: string) {
    return get<{ data: any }>(`/projects/${projectId}/tasks/${taskId}/result`);
  }
};