<template>
  <div class="py-6">
    <div class="flex items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">项目文档</h1>
      <span class="ml-3 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
        ID: {{ projectId }}
      </span>
    </div>
    
    <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
      <div class="p-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h2 class="text-xl font-bold text-gray-800">{{ project.name }}</h2>
            <p class="text-gray-500">{{ project.description }}</p>
          </div>
          <div class="flex space-x-2">
            <button class="px-3 py-1 border border-gray-300 rounded text-gray-700 hover:bg-gray-50" @click="refreshDocuments">
              刷新
            </button>
            <router-link :to="`/projects/${projectId}`" class="px-3 py-1 border border-blue-500 text-blue-500 rounded hover:bg-blue-50">
              返回项目
            </router-link>
          </div>
        </div>
      </div>
      
      <!-- 文档内容 -->
      <div class="border-t border-b">
        <div class="flex p-4 bg-gray-50">
          <div class="w-64 pr-4 border-r">
            <!-- 文档导航 -->
            <h3 class="text-lg font-medium text-gray-900 mb-3">文档类型</h3>
            <ul class="space-y-2">
              <li v-for="docType in docTypes" :key="docType.id">
                <a 
                  href="#" 
                  @click.prevent="selectDocType(docType.id)"
                  :class="[
                    'block px-3 py-2 rounded-md',
                    selectedDocType === docType.id 
                      ? 'bg-blue-100 text-blue-700 font-medium' 
                      : 'text-gray-700 hover:bg-gray-100'
                  ]"
                >
                  <i :class="['fas', docType.icon, 'mr-2']"></i>
                  {{ docType.name }}
                </a>
              </li>
            </ul>
            
            <h3 class="text-lg font-medium text-gray-900 mt-6 mb-3">版本</h3>
            <select 
              v-model="selectedVersion" 
              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option v-for="version in versions" :key="version" :value="version">{{ version }}</option>
            </select>
            
            <div class="mt-4">
              <button 
                class="w-full px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                @click="compareVersions"
              >
                <i class="fas fa-code-compare mr-2"></i> 版本比较
              </button>
            </div>
          </div>
          
          <div class="flex-1 p-4">
            <!-- 文档内容 -->
            <div v-if="loading" class="flex justify-center items-center h-64">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
            
            <div v-else-if="documentContent" class="prose max-w-none" v-html="documentContent"></div>
            
            <div v-else class="text-center py-12 text-gray-500">
              <i class="fas fa-file-alt text-4xl mb-4"></i>
              <p>请选择一个文档类型查看内容</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const projectId = route.params.id;

// 模拟数据 - 实际应用中应从API获取
const project = ref({
  name: '示例项目',
  description: '这是一个示例项目，用于展示文档功能'
});

const docTypes = [
  { id: 'product_intro', name: '产品简介', icon: 'fa-home' },
  { id: 'architecture', name: '架构设计', icon: 'fa-sitemap' },
  { id: 'api', name: '接口文档', icon: 'fa-file-code' },
  { id: 'user_manual', name: '用户手册', icon: 'fa-book-open' },
  { id: 'extension', name: '扩展能力', icon: 'fa-puzzle-piece' },
  { id: 'llms_txt', name: 'LLMs.txt', icon: 'fa-file-alt' }
];

const versions = ['v1.0.0', 'v0.9.0', 'v0.8.0'];
const selectedDocType = ref('');
const selectedVersion = ref(versions[0]);
const documentContent = ref('');
const loading = ref(false);

// 选择文档类型
const selectDocType = (docTypeId) => {
  selectedDocType.value = docTypeId;
  loadDocumentContent();
};

// 加载文档内容
const loadDocumentContent = async () => {
  if (!selectedDocType.value) return;
  
  loading.value = true;
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟文档内容
    documentContent.value = `
      <h1>示例文档内容</h1>
      <p>这是 <strong>${selectedDocType.value}</strong> 文档的示例内容，版本 <em>${selectedVersion.value}</em></p>
      <h2>章节一</h2>
      <p>这是章节一的内容，包含一些示例文本。这里可以包含项目的详细信息、架构设计、API文档等。</p>
      <pre><code class="language-java">
public class Example {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
      </code></pre>
      <h2>章节二</h2>
      <p>这是章节二的内容，包含更多示例文本。在实际应用中，这里会显示从服务器获取的真实文档内容。</p>
      <ul>
        <li>列表项一</li>
        <li>列表项二</li>
        <li>列表项三</li>
      </ul>
    `;
  } catch (error) {
    console.error('加载文档内容失败:', error);
    documentContent.value = '<p class="text-red-500">加载文档内容失败，请稍后重试。</p>';
  } finally {
    loading.value = false;
  }
};

// 比较版本
const compareVersions = () => {
  // 实际应用中应跳转到版本比较页面
  alert(`比较版本: ${selectedVersion.value} 与其他版本`);
};

// 刷新文档
const refreshDocuments = () => {
  loadDocumentContent();
};

// 初始化
onMounted(() => {
  // 加载项目信息和文档列表
  // 实际应用中应从API获取
  
  // 默认选择第一个文档类型
  if (docTypes.length > 0) {
    selectDocType(docTypes[0].id);
  }
});
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
