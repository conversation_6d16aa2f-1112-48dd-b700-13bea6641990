<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import DecryptConfigModal from '@/components/DecryptConfigModal.vue';
import { useProjectStore } from '@/stores/project';

const route = useRoute();
const projectId = parseInt(route.params.id as string);
const activeTab = ref('overview');
const projectStore = useProjectStore();

// 解密配置相关状态
const showDecryptModal = ref(false);
const decryptConfig = ref({
  enabled: true,
  decryptKey: '',
  algorithm: 'AES-256',
  configPath: ''
});

const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

// 打开解密配置弹窗
const openDecryptConfig = async () => {
  try {
    // 从store获取最新的解密配置
    const config = await projectStore.fetchDecryptConfig(projectId);
    decryptConfig.value = {
      ...config,
      enabled: true // 强制启用，确保复选框选中
    };
    showDecryptModal.value = true;
  } catch (error) {
    console.error('获取解密配置失败:', error);
    // 使用默认配置
    const defaultConfig = projectStore.getDecryptConfig(projectId);
    decryptConfig.value = {
      ...defaultConfig,
      enabled: true // 强制启用，确保复选框选中
    };
    showDecryptModal.value = true;
  }
};

// 关闭解密配置弹窗
const closeDecryptModal = () => {
  showDecryptModal.value = false;
};

// 保存解密配置
const saveDecryptConfig = async (config: any) => {
  try {
    await projectStore.saveDecryptConfig(projectId, config);
    decryptConfig.value = { ...config };
    console.log('解密配置保存成功:', config);
  } catch (error) {
    console.error('保存解密配置失败:', error);
    throw error;
  }
};

// 组件挂载时获取解密配置
onMounted(async () => {
  try {
    const config = await projectStore.fetchDecryptConfig(projectId);
    decryptConfig.value = {
      ...config,
      enabled: true // 强制启用，确保复选框选中
    };
  } catch (error) {
    console.error('初始化解密配置失败:', error);
    const defaultConfig = projectStore.getDecryptConfig(projectId);
    decryptConfig.value = {
      ...defaultConfig,
      enabled: true // 强制启用，确保复选框选中
    };
  }
});
</script>

<template>
  <div class="py-6">
    <div class="flex items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">项目详情</h1>
      <span class="ml-3 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
        ID: {{ projectId }}
      </span>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
      <div class="p-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h2 class="text-xl font-bold text-gray-800">示例项目 {{ projectId }}</h2>
            <p class="text-gray-500">example/repo-{{ projectId }}</p>
          </div>
          <div class="flex space-x-2">
            <button class="px-3 py-1 border border-gray-300 rounded text-gray-700 hover:bg-gray-50">
              同步
            </button>
            <button class="px-3 py-1 border border-blue-500 text-blue-500 rounded hover:bg-blue-50">
              编辑
            </button>
            <button
              @click="openDecryptConfig"
              class="px-3 py-1 border border-green-500 text-green-600 rounded hover:bg-green-50 flex items-center"
            >
              <i class="fas fa-shield-alt mr-1"></i>
              解密配置（已启用）
            </button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div class="bg-gray-50 p-4 rounded">
            <div class="text-sm text-gray-500">仓库类型</div>
            <div class="font-medium">GitHub</div>
          </div>
          <div class="bg-gray-50 p-4 rounded">
            <div class="text-sm text-gray-500">架构评分</div>
            <div class="font-medium">
              <span class="text-yellow-500">★★★★</span>☆
            </div>
          </div>
          <div class="bg-gray-50 p-4 rounded">
            <div class="text-sm text-gray-500">上次同步</div>
            <div class="font-medium">2023-06-15 13:45</div>
          </div>
          <div class="bg-gray-50 p-4 rounded">
            <div class="text-sm text-gray-500">主要语言</div>
            <div class="font-medium">Java</div>
          </div>
        </div>
      </div>

      <!-- 标签页导航 -->
      <div class="border-t border-b">
        <nav class="flex space-x-8 px-6">
          <button
            @click="setActiveTab('overview')"
            :class="[
              'py-4 px-1 font-medium text-sm border-b-2 -mb-px',
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            概览
          </button>
          <button
            @click="setActiveTab('architecture')"
            :class="[
              'py-4 px-1 font-medium text-sm border-b-2 -mb-px',
              activeTab === 'architecture'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            架构分析
          </button>
          <button
            @click="setActiveTab('components')"
            :class="[
              'py-4 px-1 font-medium text-sm border-b-2 -mb-px',
              activeTab === 'components'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            组件查看
          </button>
          <button
            @click="setActiveTab('dependencies')"
            :class="[
              'py-4 px-1 font-medium text-sm border-b-2 -mb-px',
              activeTab === 'dependencies'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            依赖分析
          </button>
          <button
            @click="setActiveTab('docs')"
            :class="[
              'py-4 px-1 font-medium text-sm border-b-2 -mb-px',
              activeTab === 'docs'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            文档生成
          </button>
        </nav>
      </div>

      <!-- 标签页内容 -->
      <div class="p-6">
        <div v-if="activeTab === 'overview'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">项目描述</h3>
            <p class="text-gray-600">
              这是一个示例项目，展示了架构鹰眼ArchScope的项目分析功能。该项目采用领域驱动设计(DDD)的六边形架构，
              分为领域层、应用层、接口层和基础设施层。
            </p>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">技术栈</h3>
            <div class="flex flex-wrap gap-2">
              <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded">Spring Boot</span>
              <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded">Vue 3</span>
              <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded">MyBatis Plus</span>
              <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded">MySQL</span>
              <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded">Redis</span>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">项目统计</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="bg-gray-50 p-4 rounded">
                <div class="text-sm text-gray-500">代码行数</div>
                <div class="font-medium">12,567</div>
              </div>
              <div class="bg-gray-50 p-4 rounded">
                <div class="text-sm text-gray-500">文件数量</div>
                <div class="font-medium">143</div>
              </div>
              <div class="bg-gray-50 p-4 rounded">
                <div class="text-sm text-gray-500">组件数量</div>
                <div class="font-medium">37</div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="activeTab === 'docs'" class="p-6">
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-lg font-medium text-gray-900 mb-4">项目文档</h3>
            <p class="text-gray-600 mb-6">查看项目的自动生成文档，包括产品简介、架构设计、接口文档等。</p>

            <div class="flex space-x-4">
              <router-link :to="`/projects/${projectId}/documents`" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                <i class="fas fa-book mr-2"></i> 查看文档
              </router-link>

              <button class="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50">
                <i class="fas fa-sync mr-2"></i> 重新生成文档
              </button>
            </div>
          </div>
        </div>

        <div v-else class="py-10 text-center text-gray-500">
          <p>{{ activeTab }} 功能正在开发中...</p>
        </div>
      </div>
    </div>

    <!-- 解密配置弹窗 -->
    <DecryptConfigModal
      :visible="showDecryptModal"
      :project-id="projectId.toString()"
      :initial-config="decryptConfig"
      @close="closeDecryptModal"
      @save="saveDecryptConfig"
    />
  </div>
</template>