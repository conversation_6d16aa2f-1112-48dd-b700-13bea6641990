<template>
  <div class="bg-gray-50 font-sans min-h-screen">
    <app-header></app-header>

    <div v-if="loading" class="flex justify-center items-center h-64">
      <div class="spinner inline-block w-8 h-8 border-4 border-t-indigo-500 border-r-transparent border-b-indigo-500 border-l-transparent rounded-full animate-spin"></div>
      <p class="ml-3 text-gray-600">加载项目信息中...</p>
    </div>

    <div v-else-if="error" class="container mx-auto p-6 text-center">
      <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">加载失败！</strong>
        <span class="block sm:inline"> {{ error }}</span>
        <div class="mt-3">
          <button
            @click="loadProject"
            class="bg-red-100 hover:bg-red-200 text-red-800 font-semibold py-2 px-4 rounded transition duration-200"
          >
            <i class="fas fa-sync-alt mr-2"></i> 重试
          </button>
        </div>
      </div>
    </div>

    <div v-else class="flex">
      <!-- 左侧导航栏 -->
      <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg w-72">
        <!-- 项目导航栏 -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
          <div class="flex items-center justify-between">
            <router-link :to="`/projects/${projectId}`" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
              <i class="fas fa-arrow-circle-left"></i>
            </router-link>
            <div class="relative inline-block text-left flex-grow mx-2">
              <select
                  v-model="selectedProject"
                  class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                  @change="onProjectChange"
              >
                <option v-if="project" :value="project.id">{{ project.name }}</option>
                <option v-for="p in otherProjects" :key="p.id" :value="p.id">{{ p.name }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="p-4 border-b border-gray-700">
          <h1 class="text-xl font-bold text-white truncate">{{ project ? project.name : '加载中...' }} 文档</h1>
        </div>

        <!-- 文档导航 -->
        <document-navigation
            :document-types="documentTypes"
            :active-doc-type="activeDocType"
            @select="setActiveDocType"
        />

        <!-- 版本选择和比较 -->
        <div class="p-4 border-t border-gray-700 mt-auto">
          <version-selector
              :versions="documentVersions"
              v-model:selected-version="selectedVersion"
              @compare="showVersionCompare = true"
          />
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto">
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
          <h1 class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">
            {{ getDocTypeLabel(activeDocType) }}
          </h1>

          <!-- 文档内容 -->
          <document-content
              :content="documentContent"
              :loading="contentLoading"
              :error="contentError"
          />
        </div>
      </div>
    </div>

    <!-- 版本比较对话框 -->
    <div v-if="showVersionCompare" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">版本比较</h3>
          <button @click="showVersionCompare = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mb-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">从版本</label>
              <select
                  v-model="compareFromVersion"
                  class="form-select w-full px-3 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
              >
                <option v-for="version in documentVersions" :key="version.id" :value="version.id">
                  {{ version.versionTag }} ({{ formatDate(version.timestamp) }})
                </option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">到版本</label>
              <select
                  v-model="compareToVersion"
                  class="form-select w-full px-3 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
              >
                <option v-for="version in documentVersions" :key="version.id" :value="version.id">
                  {{ version.versionTag }} ({{ formatDate(version.timestamp) }})
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="flex justify-end space-x-3">
          <button
              @click="showVersionCompare = false"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button
              @click="compareVersions"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            比较
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import AppHeader from '@/components/AppHeader.vue'
import VersionSelector from '@/components/VersionSelector.vue'
import DocumentContent from '@/components/DocumentContent.vue'
import DocumentNavigation, { DocumentTypeOption } from '@/components/DocumentNavigation.vue'

export default defineComponent({
  name: 'ProjectDocView',
  components: {
    AppHeader,
    VersionSelector,
    DocumentContent,
    DocumentNavigation
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectStore = useProjectStore()

    // 状态
    const projectId = ref(route.params.id as string)
    const project = ref(null)
    const loading = ref(true)
    const error = ref('')
    const documentVersions = ref([])
    const selectedVersion = ref('')
    const activeDocType = ref('PRODUCT_INTRO')
    const documentContent = ref('')
    const contentLoading = ref(false)
    const contentError = ref('')
    const showVersionCompare = ref(false)
    const compareFromVersion = ref('')
    const compareToVersion = ref('')
    const selectedProject = ref(projectId.value)
    const otherProjects = ref([])

    // 文档类型定义
    const documentTypes = ref<DocumentTypeOption[]>([
      {
        label: '产品简介',
        value: 'PRODUCT_INTRO',
        icon: 'fas fa-home',
        sections: [
          { id: '核心能力', label: '核心能力' },
          { id: '主要特性', label: '主要特性' },
          { id: '未来规划', label: '未来规划' },
          { id: '目标用户', label: '目标用户' }
        ]
      },
      { label: '架构设计', value: 'ARCHITECTURE', icon: 'fas fa-sitemap' },
      { label: '扩展能力', value: 'EXTENSION', icon: 'fas fa-puzzle-piece' },
      { label: '用户手册', value: 'USER_MANUAL', icon: 'fas fa-book-open' },
      { label: '接口文档', value: 'API', icon: 'fas fa-file-code' },
      { label: 'LLM生成的原始内容', value: 'LLMS_TXT', icon: 'fas fa-file-alt' }
    ])

    // 获取文档类型标签
    const getDocTypeLabel = (value) => {
      const docType = documentTypes.value.find(dt => dt.value === value)
      return docType ? docType.label : value
    }

    // 设置活动文档类型
    const setActiveDocType = (docType) => {
      activeDocType.value = docType
      loadDocumentContent()
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知日期'

      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    // 加载项目信息
    const loadProject = async () => {
      try {
        loading.value = true
        error.value = ''

        // 获取项目详情
        const projectData = await projectStore.fetchProjectById(Number(projectId.value))
        project.value = projectData

        // 获取其他项目列表（用于项目切换）
        await projectStore.fetchProjects()
        otherProjects.value = projectStore.projects.filter(p => p.id !== Number(projectId.value))

        // 加载文档版本
        await loadDocumentVersions()
      } catch (err) {
        console.error('加载项目失败:', err)
        error.value = err.message || '加载项目信息失败'
      } finally {
        loading.value = false
      }
    }

    // 加载文档版本列表
    const loadDocumentVersions = async () => {
      try {
        versionsLoading.value = true
        versionsError.value = ''

        // 调用API获取文档版本列表
        const versions = await projectStore.fetchDocumentVersions(Number(projectId.value))
        documentVersions.value = versions

        // 默认选择最新版本
        if (documentVersions.value.length > 0) {
          selectedVersion.value = documentVersions.value[0].id
          compareFromVersion.value = documentVersions.value.length > 1 ? documentVersions.value[1].id : documentVersions.value[0].id
          compareToVersion.value = documentVersions.value[0].id

          // 加载文档内容
          await loadDocumentContent()
        } else {
          contentError.value = '该项目暂无文档版本'
        }
      } catch (err) {
        console.error('加载文档版本失败:', err)
        versionsError.value = err.message || '加载文档版本失败'
      } finally {
        versionsLoading.value = false
      }
    }

    // 加载文档内容
    const loadDocumentContent = async () => {
      try {
        contentLoading.value = true
        contentError.value = ''

        // 这里应该调用API获取文档内容
        // 由于API尚未实现，这里使用模拟数据
        if (activeDocType.value === 'PRODUCT_INTRO') {
          documentContent.value = `
            <p class="text-lg"><code class="bg-indigo-50 text-indigo-600 px-2 py-1 rounded">架构鹰眼 ArchScope</code> 系统是一个面向开发者的架构观测和守护系统。</p>
            <p>它旨在通过自动化分析需求文档、设计文档和代码仓库，为开发者提供项目的全局视角，从而帮助开发者快速理解和治理项目，同时也方便使用方快速对接。</p>

            <h2 id="核心能力" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">核心能力</h2>
            <ul class="space-y-2 my-6">
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                <span>基于仓库地址自动生成专属项目文档网站。</span>
              </li>
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                <span>自动感知项目变更周期，定期更新文档。</span>
              </li>
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                <span>统计项目访问量，进行分级（1-5星）。</span>
              </li>
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                <span>智能任务排队处理，优化资源分配。</span>
              </li>
            </ul>

            <h3 id="主要特性" class="text-xl font-semibold text-gray-800 mt-8 mb-4">主要特性</h3>
            <p>ArchScope 提供以下主要特性：</p>
            <ul class="space-y-3 my-6">
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-md bg-blue-100 text-blue-600 h-6 w-6 mr-3"><i class="fas fa-file-alt text-xs"></i></span>
                <span><strong class="text-gray-800">自动化文档生成</strong>: 从代码和现有文档中提取信息。</span>
              </li>
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-md bg-green-100 text-green-600 h-6 w-6 mr-3"><i class="fas fa-code-branch text-xs"></i></span>
                <span><strong class="text-gray-800">版本管理与对比</strong>: 追踪文档变更，方便回溯和比较。</span>
              </li>
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-md bg-yellow-100 text-yellow-600 h-6 w-6 mr-3"><i class="fas fa-chart-line text-xs"></i></span>
                <span><strong class="text-gray-800">项目健康度评估</strong>: 基于访问量和更新频率等指标。</span>
              </li>
              <li class="flex items-center">
                <span class="inline-flex items-center justify-center rounded-md bg-purple-100 text-purple-600 h-6 w-6 mr-3"><i class="fas fa-puzzle-piece text-xs"></i></span>
                <span><strong class="text-gray-800">可扩展的解析器</strong>: 支持集成更多文档和代码类型。</span>
              </li>
            </ul>

            <h2 id="未来规划" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">未来规划</h2>
            <p class="mb-4">我们计划在未来版本中增加以下功能：</p>
            <ul class="space-y-2 my-6 pl-5 list-disc">
              <li class="text-gray-700">深度代码结构分析。</li>
              <li class="text-gray-700">与 CI/CD 流程集成。</li>
              <li class="text-gray-700">更丰富的可视化图表。</li>
            </ul>

            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 my-8">
              <h2 id="目标用户" class="text-2xl font-semibold text-gray-800 mb-4">目标用户</h2>
              <p class="mb-4">本系统主要服务于需要快速理解、维护和对接项目的开发者和使用方。</p>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white p-5 rounded-lg shadow-sm">
                  <h3 id="开发者" class="text-xl font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-code mr-2 text-indigo-500"></i>开发者
                  </h3>
                  <p class="mb-4 text-gray-600">帮助开发者快速熟悉新项目，理解现有架构，进行代码治理。</p>
                </div>

                <div class="bg-white p-5 rounded-lg shadow-sm">
                  <h3 id="使用方" class="text-xl font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-users mr-2 text-green-500"></i>使用方
                  </h3>
                  <p class="mb-4 text-gray-600">提供清晰的接口文档和用户手册，方便快速集成和使用项目。</p>
                </div>
              </div>
            </div>
          `
        } else {
          documentContent.value = `<p class="text-center py-10 text-gray-500">${getDocTypeLabel(activeDocType.value)} 文档正在生成中...</p>`
        }
      } catch (err) {
        console.error('加载文档内容失败:', err)
        contentError.value = err.message || '加载文档内容失败'
      } finally {
        contentLoading.value = false
      }
    }

    // 项目变更处理
    const onProjectChange = () => {
      router.push(`/projects/${selectedProject.value}/docs`)
    }

    // 版本比较
    const compareVersions = async () => {
      if (!compareFromVersion.value || !compareToVersion.value) {
        alert('请选择要比较的版本')
        return
      }

      try {
        const compareResult = await projectStore.compareDocumentVersions(
          Number(compareFromVersion.value),
          Number(compareToVersion.value)
        )

        // 关闭对话框
        showVersionCompare.value = false

        // 显示比较结果
        alert(compareResult || '版本比较结果：无差异')
      } catch (err) {
        console.error('版本比较失败:', err)
        alert('版本比较失败: ' + (err.message || '未知错误'))
      }
    }

    // 监听路由参数变化
    watch(() => route.params.id, (newId) => {
      if (newId && newId !== projectId.value) {
        projectId.value = newId as string
        selectedProject.value = projectId.value
        loadProject()
      }
    })

    // 组件挂载时加载数据
    onMounted(() => {
      loadProject()
    })

    return {
      projectId,
      project,
      loading,
      error,
      documentVersions,
      selectedVersion,
      activeDocType,
      documentContent,
      contentLoading,
      contentError,
      versionsLoading,
      versionsError,
      contentLoading,
      contentError,
      documentTypes,
      showVersionCompare,
      compareFromVersion,
      compareToVersion,
      selectedProject,
      otherProjects,
      getDocTypeLabel,
      setActiveDocType,
      formatDate,
      onProjectChange,
      compareVersions
    }
  }
})
</script>

<style scoped>
.sidebar {
  width: 18rem;
  background-color: #1E293B; /* Slate-800 */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 自定义表单样式 */
.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1F2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #4F46E5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}
</style>
