<template>
  <div class="bg-gray-100 font-sans min-h-screen">
    <nav class="bg-gray-800 p-4">
      <div class="container mx-auto flex justify-between items-center">
        <router-link to="/" class="text-white flex items-center">
          <div
            class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-2"
          >
            <img src="@/assets/logo.png" alt="ArchScope" class="h-8 w-8" />
          </div>
          <span class="text-2xl font-bold">ArchScope</span>
        </router-link>
        <div class="flex space-x-4">
          <router-link
            to="/projects"
            class="bg-indigo-600 text-white px-3 py-2 rounded-md text-sm font-medium"
            >项目列表</router-link
          >
          <router-link
            to="/tasks"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
            >任务队列</router-link
          >
          <router-link
            to="/projects/new"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
            >注册项目</router-link
          >
        </div>
      </div>
    </nav>

    <div class="container mx-auto p-6">
      <div class="flex items-center justify-between mb-6 animate-fade-in">
        <h1 class="text-3xl font-bold text-gray-800">项目列表</h1>
        <router-link
          to="/projects/new"
          class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center animate-button"
        >
          <i class="fas fa-plus-circle mr-2"></i> 注册新项目
        </router-link>
      </div>

      <!-- 项目列表 -->
      <div v-if="loading" class="text-center py-10">
        <div
          class="spinner inline-block w-8 h-8 border-4 border-t-indigo-500 border-r-transparent border-b-indigo-500 border-l-transparent rounded-full animate-spin"
        ></div>
        <p class="mt-2 text-gray-600">加载项目中...</p>
      </div>

      <div
        v-else-if="projects.length === 0"
        class="bg-white rounded-lg shadow-md p-8 text-center"
      >
        <div class="text-gray-400 mb-4">
          <i class="fas fa-project-diagram text-6xl"></i>
        </div>
        <h2 class="text-2xl font-semibold text-gray-700 mb-2">暂无项目</h2>
        <p class="text-gray-600 mb-6">
          您当前没有任何项目，点击下方按钮创建您的第一个项目
        </p>
        <router-link
          to="/projects/new"
          class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md inline-flex items-center"
        >
          <i class="fas fa-plus-circle mr-2"></i> 注册新项目
        </router-link>
      </div>

      <div
        v-else
        class="content-card bg-white shadow-md rounded-lg overflow-hidden mb-6 animate-slide-up"
      >
        <div
          class="p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between"
        >
          <h2 class="text-lg font-semibold text-gray-700">所有项目</h2>
          <div class="flex items-center">
            <div class="input-with-icon mr-4">
              <i class="fas fa-search input-icon"></i>
              <input
                type="text"
                placeholder="搜索项目..."
                class="form-input border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white"
                v-model="searchQuery"
              />
            </div>
            <select class="form-select" v-model="sortOption">
              <option value="name">按名称排序</option>
              <option value="updated">最近更新</option>
              <option value="created">创建时间</option>
            </select>
          </div>
        </div>
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="table-header">
            <tr>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                项目名称
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                仓库地址
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                星级
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                最后更新
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">操作</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="project in filteredProjects"
              :key="project.id"
              class="table-row animate-scale"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div
                    class="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center"
                  >
                    <i class="fas fa-project-diagram text-indigo-600"></i>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ project.name }}
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ project.description || "无项目描述" }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-500">
                  <i class="fas fa-code-branch text-gray-400 mr-2"></i>
                  <span>{{ project.repositoryUrl }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="star-rating text-sm">
                  <i
                    v-for="n in 5"
                    :key="n"
                    :class="[
                      'fa-star',
                      n <= (project.analysisCount || 0)
                        ? 'fas star-filled'
                        : 'far star-empty',
                    ]"
                  ></i>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-500">
                  <i class="far fa-clock text-gray-400 mr-2"></i>
                  <span>{{ formatDate(project.updatedAt) }}</span>
                </div>
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
              >
                <router-link
                  :to="`/projects/${project.id}`"
                  class="text-indigo-600 hover:text-indigo-900 mr-4 inline-flex items-center"
                >
                  <i class="fas fa-info-circle mr-1"></i> 详情
                </router-link>
                <router-link
                  :to="`/projects/${project.id}/documents`"
                  class="text-green-600 hover:text-green-900 inline-flex items-center"
                >
                  <i class="fas fa-external-link-alt mr-1"></i> 访问
                </router-link>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div
        v-if="projects.length > 0"
        class="flex items-center justify-between animate-fade-in"
      >
        <div class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ paginationStart }}</span> 到
          <span class="font-medium">{{ paginationEnd }}</span> 共
          <span class="font-medium">{{ projects.length }}</span> 个项目
        </div>
        <div class="flex items-center space-x-2">
          <button
            class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            上一页
          </button>
          <button
            v-for="page in totalPages"
            :key="page"
            @click="currentPage = page"
            :class="[
              'px-3 py-1 rounded-md animate-button',
              currentPage === page
                ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                : 'bg-white border border-gray-300 text-gray-500 hover:bg-gray-50',
            ]"
          >
            {{ page }}
          </button>
          <button
            class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, computed } from "vue";
import { useProjectStore } from "@/stores/project";

export default defineComponent({
  name: "ProjectListView",
  setup() {
    const projectStore = useProjectStore();
    const loading = ref(true);
    const error = ref("");
    const searchQuery = ref("");
    const sortOption = ref("updated");
    const currentPage = ref(1);
    const itemsPerPage = ref(10);

    // 获取项目列表
    const fetchProjects = async () => {
      try {
        loading.value = true;
        await projectStore.fetchProjects();
      } catch (err: any) {
        error.value = err.message || "获取项目列表失败";
      } finally {
        loading.value = false;
      }
    };

    // 格式化日期
    const formatDate = (dateString: string) => {
      if (!dateString) return "未知日期";

      const date = new Date(dateString);
      return date.toLocaleDateString("zh-CN", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    };

    // 获取状态文本
    const getStatusText = (status: string) => {
      switch (status) {
        case "PENDING":
          return "待分析";
        case "ANALYZING":
          return "分析中";
        case "COMPLETED":
          return "分析完成";
        case "FAILED":
          return "分析失败";
        default:
          return "未知状态";
      }
    };

    // 获取状态样式
    const getStatusClass = (status: string) => {
      switch (status) {
        case "PENDING":
          return "bg-yellow-100 text-yellow-800";
        case "ANALYZING":
          return "bg-blue-100 text-blue-800";
        case "COMPLETED":
          return "bg-green-100 text-green-800";
        case "FAILED":
          return "bg-red-100 text-red-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
    };

    // 搜索和排序后的项目列表
    const sortedProjects = computed(() => {
      const projects = [...projectStore.projects];

      // 排序
      switch (sortOption.value) {
        case "name":
          return projects.sort((a, b) => a.name.localeCompare(b.name));
        case "updated":
          return projects.sort(
            (a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
        case "created":
          return projects.sort(
            (a, b) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        default:
          return projects;
      }
    });

    // 搜索过滤
    const filteredProjects = computed(() => {
      if (!searchQuery.value.trim()) {
        // 分页
        const start = (currentPage.value - 1) * itemsPerPage.value;
        const end = start + itemsPerPage.value;
        return sortedProjects.value.slice(start, end);
      }

      const query = searchQuery.value.toLowerCase().trim();
      const filtered = sortedProjects.value.filter(
        (project) =>
          project.name.toLowerCase().includes(query) ||
          (project.description &&
            project.description.toLowerCase().includes(query)) ||
          (project.repositoryUrl &&
            project.repositoryUrl.toLowerCase().includes(query))
      );

      // 分页
      const start = (currentPage.value - 1) * itemsPerPage.value;
      const end = start + itemsPerPage.value;
      return filtered.slice(start, end);
    });

    // 分页计算
    const totalPages = computed(() => {
      const filteredCount = sortedProjects.value.filter((project) => {
        if (!searchQuery.value.trim()) return true;

        const query = searchQuery.value.toLowerCase().trim();
        return (
          project.name.toLowerCase().includes(query) ||
          (project.description &&
            project.description.toLowerCase().includes(query)) ||
          (project.repositoryUrl &&
            project.repositoryUrl.toLowerCase().includes(query))
        );
      }).length;

      return Math.max(1, Math.ceil(filteredCount / itemsPerPage.value));
    });

    // 分页起始和结束
    const paginationStart = computed(() => {
      return (currentPage.value - 1) * itemsPerPage.value + 1;
    });

    const paginationEnd = computed(() => {
      const end = currentPage.value * itemsPerPage.value;
      const filteredCount = sortedProjects.value.filter((project) => {
        if (!searchQuery.value.trim()) return true;

        const query = searchQuery.value.toLowerCase().trim();
        return (
          project.name.toLowerCase().includes(query) ||
          (project.description &&
            project.description.toLowerCase().includes(query)) ||
          (project.repositoryUrl &&
            project.repositoryUrl.toLowerCase().includes(query))
        );
      }).length;

      return Math.min(end, filteredCount);
    });

    onMounted(() => {
      fetchProjects();
    });

    return {
      projects: projectStore.projects,
      filteredProjects,
      loading,
      error,
      formatDate,
      getStatusText,
      getStatusClass,
      searchQuery,
      sortOption,
      currentPage,
      totalPages,
      paginationStart,
      paginationEnd,
    };
  },
});
</script>

<style scoped>
/* 自定义样式 */
.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 表格样式 */
.table-header {
  background-color: #f8fafc; /* Slate-50 */
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  color: #64748b; /* Slate-500 */
}

.table-row {
  transition: all 0.2s ease;
}

.table-row:hover {
  background-color: #f1f5f9; /* Slate-100 */
}

/* 星级评分样式 */
.star-rating {
  display: inline-flex;
  align-items: center;
}

.star-filled {
  color: #fbbf24; /* Amber-400 */
}

.star-empty {
  color: #e5e7eb; /* Gray-200 */
}

/* 表单元素样式 */
.form-input {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

/* 带图标的输入框 */
.input-with-icon {
  position: relative;
}

.input-with-icon .form-input {
  padding-left: 2.5rem;
}

.input-with-icon .input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
  z-index: 10;
}

/* 动画效果 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-scale {
  transition: transform 0.3s ease;
}

.animate-scale:hover {
  transform: scale(1.02);
}

.animate-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
