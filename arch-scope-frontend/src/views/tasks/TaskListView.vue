<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import MainLayout from "@/layouts/MainLayout.vue";

// 模拟任务数据，符合界面原型设计
const mockTasks = ref([
  {
    id: "task-12345",
    name: "文档更新 (UPDATE)",
    project: "Awesome Components",
    type: "doc_generation",
    status: "processing",
    startTime: "2023-10-26 10:05",
    endTime: null,
    projectColor: "bg-indigo-500",
  },
  {
    id: "task-12344",
    name: "文档更新 (UPDATE)",
    project: "Payment Gateway",
    type: "doc_generation",
    status: "success",
    startTime: "2023-10-25 15:30",
    endTime: "2023-10-25 15:45",
    projectColor: "bg-blue-500",
  },
  {
    id: "task-12346",
    name: "项目初始化 (INIT)",
    project: "New Project X",
    type: "project_init",
    status: "waiting",
    startTime: "2023-10-26 10:10",
    endTime: null,
    projectColor: "bg-green-500",
  },
]);

const searchQuery = ref("");
const statusFilter = ref("all");
const router = useRouter();

// 状态样式映射
const getStatusClass = (status: string) => {
  const statusClasses = {
    processing: "status-processing",
    success: "status-success",
    waiting: "status-waiting",
    failed: "status-failed",
  };
  return (
    statusClasses[status as keyof typeof statusClasses] || "status-waiting"
  );
};

// 状态文本映射
const getStatusText = (status: string) => {
  const statusTexts = {
    processing: "处理中",
    success: "成功",
    waiting: "等待中",
    failed: "失败",
  };
  return statusTexts[status as keyof typeof statusTexts] || "未知";
};

// 状态图标映射
const getStatusIcon = (status: string) => {
  const statusIcons = {
    processing: "fas fa-sync-alt",
    success: "fas fa-check-circle",
    waiting: "fas fa-clock",
    failed: "fas fa-times-circle",
  };
  return (
    statusIcons[status as keyof typeof statusIcons] || "fas fa-question-circle"
  );
};

// 任务类型图标映射
const getTypeIcon = (type: string) => {
  const typeIcons = {
    doc_generation: "fas fa-file-alt",
    project_init: "fas fa-project-diagram",
    code_analysis: "fas fa-code",
    repo_sync: "fas fa-sync",
  };
  return typeIcons[type as keyof typeof typeIcons] || "fas fa-tasks";
};

// 筛选后的任务列表
const filteredTasks = computed(() => {
  return mockTasks.value.filter((task) => {
    const matchesSearch =
      task.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      task.project.toLowerCase().includes(searchQuery.value.toLowerCase());
    const matchesStatus =
      statusFilter.value === "all" || task.status === statusFilter.value;
    return matchesSearch && matchesStatus;
  });
});

// 查看任务详情
const viewTaskDetails = (taskId: string) => {
  router.push(`/tasks/${taskId}`);
};

// 刷新任务列表
const refreshTasks = () => {
  console.log("刷新任务列表");
};

onMounted(() => {
  console.log("任务列表页面已加载");
});
</script>

<template>
  <MainLayout>
    <div class="container mx-auto p-6">
      <!-- 页面头部 -->
      <div class="flex items-center justify-between mb-6 animate-fade-in">
        <h1 class="text-3xl font-bold text-gray-800">任务队列</h1>
        <div class="flex items-center space-x-4">
          <button
            @click="refreshTasks"
            data-cy="refresh-button"
            class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center animate-button"
          >
            <i class="fas fa-sync mr-2"></i> 刷新任务
          </button>
        </div>
      </div>

      <!-- 主要内容卡片 -->
      <div
        class="content-card bg-white shadow-md rounded-lg overflow-hidden mb-6 animate-slide-up"
      >
        <!-- 卡片头部 -->
        <div
          class="p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between"
        >
          <h2 class="text-lg font-semibold text-gray-700">所有任务</h2>
          <div class="flex items-center">
            <!-- 搜索框 -->
            <div class="input-with-icon mr-4">
              <i class="fas fa-search input-icon"></i>
              <input
                v-model="searchQuery"
                data-cy="search-input"
                type="text"
                placeholder="搜索任务..."
                class="form-input border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white"
              />
            </div>
            <!-- 状态筛选 -->
            <select
              v-model="statusFilter"
              data-cy="status-filter"
              class="form-select"
            >
              <option value="all">所有状态</option>
              <option value="processing">处理中</option>
              <option value="success">成功</option>
              <option value="waiting">等待中</option>
              <option value="failed">失败</option>
            </select>
          </div>
        </div>

        <!-- 任务表格 -->
        <table data-cy="task-table" class="min-w-full divide-y divide-gray-200">
          <thead class="table-header">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                任务ID
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                关联项目
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                任务类型
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                状态
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                开始时间
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                结束时间
              </th>
              <th class="relative px-6 py-3">
                <span class="sr-only">详情</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="task in filteredTasks"
              :key="task.id"
              data-cy="task-row"
              class="table-row animate-scale cursor-pointer"
              @click="viewTaskDetails(task.id)"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div
                  class="flex items-center text-sm font-medium text-gray-900"
                >
                  <i class="fas fa-tasks text-gray-400 mr-2"></i>
                  <span data-cy="task-id">{{ task.id }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-900">
                  <span
                    :class="`inline-block h-2 w-2 rounded-full ${task.projectColor} mr-2`"
                  ></span>
                  <span data-cy="task-project">{{ task.project }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-900">
                  <i
                    :class="`${getTypeIcon(task.type)} text-gray-400 mr-2`"
                  ></i>
                  <span data-cy="task-type">{{ task.name }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  data-cy="task-status"
                  :class="`status-badge ${getStatusClass(
                    task.status
                  )} flex items-center`"
                >
                  <i :class="`${getStatusIcon(task.status)} mr-2`"></i>
                  {{ getStatusText(task.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-500">
                  <i class="far fa-clock text-gray-400 mr-2"></i>
                  <span data-cy="task-start-time">{{ task.startTime }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-500">
                  <template v-if="task.endTime">
                    <i class="far fa-clock text-gray-400 mr-2"></i>
                    <span>{{ task.endTime }}</span>
                  </template>
                  <span v-else>-</span>
                </div>
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
              >
                <button
                  @click.stop="viewTaskDetails(task.id)"
                  data-cy="task-detail-button"
                  class="text-indigo-600 hover:text-indigo-900 inline-flex items-center transition-colors duration-200"
                >
                  <i class="fas fa-info-circle mr-1"></i> 详情
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex items-center justify-between animate-fade-in">
        <div data-cy="pagination-info" class="text-sm text-gray-700">
          显示 <span class="font-medium">1</span> 到
          <span class="font-medium">{{ filteredTasks.length }}</span> 共
          <span class="font-medium">{{ filteredTasks.length }}</span> 个任务
        </div>
        <div class="flex items-center space-x-2">
          <button
            data-cy="pagination-prev"
            class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
            disabled
          >
            上一页
          </button>
          <button
            data-cy="pagination-current"
            class="px-3 py-1 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 animate-button"
          >
            1
          </button>
          <button
            data-cy="pagination-next"
            class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
            disabled
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<style scoped>
/* 界面原型样式 */
:root {
  --primary-color: #4f46e5; /* Indigo-600 */
  --primary-hover: #4338ca; /* Indigo-700 */
}

body {
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background-color: #f9fafb; /* Gray-50 */
}

.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom button styles */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

/* Table styles */
.table-header {
  background-color: #f8fafc; /* Slate-50 */
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  color: #64748b; /* Slate-500 */
}

.table-row {
  transition: all 0.2s ease;
}

.table-row:hover {
  background-color: #f1f5f9; /* Slate-100 */
}

/* 状态徽章样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-processing {
  background-color: #fef3c7; /* Amber-100 */
  color: #92400e; /* Amber-800 */
}

.status-success {
  background-color: #dcfce7; /* Green-100 */
  color: #166534; /* Green-800 */
}

.status-waiting {
  background-color: #dbeafe; /* Blue-100 */
  color: #1e40af; /* Blue-800 */
}

.status-failed {
  background-color: #fee2e2; /* Red-100 */
  color: #991b1b; /* Red-800 */
}

/* Custom select styles */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

/* Custom input styles */
.form-input {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

/* Input with icon styles */
.input-with-icon {
  position: relative;
}

.input-with-icon .form-input {
  padding-left: 2.5rem;
}

.input-with-icon .input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
  z-index: 10;
}

/* Animation effects */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-scale {
  transition: transform 0.3s ease;
}

.animate-scale:hover {
  transform: scale(1.02);
}

.animate-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
