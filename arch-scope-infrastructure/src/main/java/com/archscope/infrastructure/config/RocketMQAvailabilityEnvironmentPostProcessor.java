package com.archscope.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.Properties;

/**
 * 在 Spring 应用启动早期阶段检测 RocketMQ 可用性
 * 并设置相应的属性以控制 RocketMQ 消费者的启用/禁用
 */
@Slf4j
@Component
public class RocketMQAvailabilityEnvironmentPostProcessor implements EnvironmentPostProcessor {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        Properties props = new Properties();

        // 获取 RocketMQ 服务器地址
        String nameServer = environment.getProperty("rocketmq.name-server", "localhost:9876");

        // 检测 RocketMQ 服务器是否可用
        boolean rocketMQAvailable = isRocketMQServerAvailable(nameServer);

        // 设置属性
        props.setProperty("rocketmq.consumer.enabled", String.valueOf(rocketMQAvailable));

        if (!rocketMQAvailable) {
            System.out.println("⚠️ RocketMQ 服务不可用，消费者功能将被禁用: " + nameServer);
        } else {
            System.out.println("✅ RocketMQ 服务可用，消费者功能正常启用: " + nameServer);
        }

        // 添加属性源
        PropertiesPropertySource propertySource = new PropertiesPropertySource("rocketmq-availability", props);
        environment.getPropertySources().addLast(propertySource);
    }

    /**
     * 检查 RocketMQ 名称服务器是否可用
     */
    private boolean isRocketMQServerAvailable(String nameServer) {
        String[] parts = nameServer.split(":");
        if (parts.length != 2) {
            return false;
        }

        String host = parts[0];
        int port;
        try {
            port = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            return false;
        }

        // 尝试连接名称服务器
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 2000); // 2秒超时
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}