package com.archscope.infrastructure.mapper;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 领域任务Mapper接口
 */
@Mapper
public interface DomainTaskMapper extends BaseMapper<Task> {
    
    /**
     * 查找下一个待执行的任务
     * 
     * @return 任务
     */
    Task findNextPendingTask();
    
    /**
     * 查找指定类型的下一个待执行任务
     * 
     * @param taskType 任务类型
     * @return 任务
     */
    Task findNextPendingTaskByType(@Param("taskType") String taskType);
    
    /**
     * 查找所有指定状态的任务
     * 
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findAllByStatus(@Param("status") TaskStatus status);
    
    /**
     * 查找所有指定状态和类型的任务
     * 
     * @param status 任务状态
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<Task> findAllByStatusAndTaskType(@Param("status") TaskStatus status, @Param("taskType") String taskType);
    
    /**
     * 查找所有指定状态和项目ID的任务
     * 
     * @param status 任务状态
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<Task> findAllByStatusAndProjectId(@Param("status") TaskStatus status, @Param("projectId") Long projectId);
    
    /**
     * 删除指定日期之前的已完成任务
     * 
     * @param date 日期
     * @return 删除的任务数量
     */
    int deleteCompletedTasksOlderThan(@Param("date") LocalDateTime date);
}
