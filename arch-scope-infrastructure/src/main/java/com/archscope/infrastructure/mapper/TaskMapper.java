package com.archscope.infrastructure.mapper;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TaskMapper {

    void insert(Task task);

    Task findById(Long id);

    List<Task> findByStatus(@Param("status") TaskStatus status);

    void updateStatus(@Param("id") Long id, @Param("status") TaskStatus status);

    void update(Task task);
}
