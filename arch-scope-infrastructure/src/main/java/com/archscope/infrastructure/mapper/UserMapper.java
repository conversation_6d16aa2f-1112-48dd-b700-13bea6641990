package com.archscope.infrastructure.mapper;

import com.archscope.domain.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据角色查询用户列表
     * 
     * @param role 用户角色
     * @return 用户列表
     */
    List<User> selectByRole(@Param("role") String role);
    
    /**
     * 验证用户凭证
     * 
     * @param username 用户名
     * @param password 密码
     * @return 用户实体
     */
    User authenticate(@Param("username") String username, @Param("password") String password);
}
