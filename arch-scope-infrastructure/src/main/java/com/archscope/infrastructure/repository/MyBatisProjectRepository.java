package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.valueobject.ProjectType;
import com.archscope.infrastructure.mapper.ProjectMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 项目仓库MyBatis实现
 */
@Repository("myBatisProjectRepository")
public class MyBatisProjectRepository implements ProjectRepository {

    private final ProjectMapper projectMapper;

    public MyBatisProjectRepository(ProjectMapper projectMapper) {
        this.projectMapper = projectMapper;
    }

    @Override
    public Project save(Project project) {
        if (project.getId() == null) {
            projectMapper.insert(project);
        } else {
            projectMapper.updateById(project);
        }
        return project;
    }

    @Override
    public Optional<Project> findById(Long id) {
        return Optional.ofNullable(projectMapper.selectById(id));
    }

    @Override
    public Optional<Project> findByRepositoryUrl(String repositoryUrl) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getRepositoryUrl, repositoryUrl);
        return Optional.ofNullable(projectMapper.selectOne(wrapper));
    }

    @Override
    public Optional<Project> findByName(String name) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getName, name);
        return Optional.ofNullable(projectMapper.selectOne(wrapper));
    }

    @Override
    public Project update(Project project) {
        projectMapper.updateById(project);
        return project;
    }

    @Override
    public void delete(Long id) {
        projectMapper.deleteById(id);
    }

    @Override
    public List<Project> findAll() {
        return projectMapper.selectList(null);
    }

    @Override
    public List<Project> findAll(int page, int size) {
        Page<Project> projectPage = new Page<>(page + 1, size);
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        return projectMapper.selectPage(projectPage, wrapper).getRecords();
    }

    @Override
    public List<Project> findByType(ProjectType type) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getType, type);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public List<Project> findByUserId(Long userId) {
        return findByCreatorId(userId);
    }

    @Override
    public List<Project> findByCreatorId(Long creatorId) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getCreatorId, creatorId);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public List<Project> findRecentProjects(int limit) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(Project::getCreatedAt).last("LIMIT " + limit);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public List<Project> findTopRatedProjects(int limit) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(Project::getRating).last("LIMIT " + limit);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public long count() {
        return projectMapper.selectCount(null);
    }

    @Override
    public void deleteById(Long id) {
        projectMapper.deleteById(id);
    }

    @Override
    public boolean existsByRepositoryUrl(String repositoryUrl) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getRepositoryUrl, repositoryUrl);
        return projectMapper.selectCount(wrapper) > 0;
    }
}