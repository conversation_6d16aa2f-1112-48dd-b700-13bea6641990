package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.infrastructure.mapper.DomainTaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务仓库MyBatis实现
 */
@Repository
@RequiredArgsConstructor
public class MyBatisTaskRepository implements TaskRepository {

    private final DomainTaskMapper domainTaskMapper;

    @Override
    public Task save(Task task) {
        if (task.getId() == null) {
            domainTaskMapper.insert(task);
        } else {
            domainTaskMapper.updateById(task);
        }
        return task;
    }

    @Override
    public Optional<Task> findById(Long id) {
        return Optional.ofNullable(domainTaskMapper.selectById(id));
    }

    @Override
    public List<Task> findAll() {
        return domainTaskMapper.selectList(null);
    }

    @Override
    public List<Task> findAllByStatus(TaskStatus status) {
        return domainTaskMapper.findAllByStatus(status);
    }

    @Override
    public List<Task> findAllByStatusAndTaskType(TaskStatus status, String taskType) {
        return domainTaskMapper.findAllByStatusAndTaskType(status, taskType);
    }

    @Override
    public List<Task> findAllByStatusAndProjectId(TaskStatus status, Long projectId) {
        return domainTaskMapper.findAllByStatusAndProjectId(status, projectId);
    }

    @Override
    public Optional<Task> findNextPendingTask() {
        return Optional.ofNullable(domainTaskMapper.findNextPendingTask());
    }

    @Override
    public Optional<Task> findNextPendingTaskByType(String taskType) {
        return Optional.ofNullable(domainTaskMapper.findNextPendingTaskByType(taskType));
    }

    @Override
    public Task update(Task task) {
        domainTaskMapper.updateById(task);
        return task;
    }

    @Override
    public void deleteById(Long id) {
        domainTaskMapper.deleteById(id);
    }

    @Override
    public int deleteCompletedTasksOlderThan(LocalDateTime date) {
        return domainTaskMapper.deleteCompletedTasksOlderThan(date);
    }

    @Override
    public List<Task> findByProjectId(Long projectId) {
        LambdaQueryWrapper<Task> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Task::getProjectId, projectId);
        return domainTaskMapper.selectList(wrapper);
    }
}
