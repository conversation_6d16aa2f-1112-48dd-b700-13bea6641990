-- 创建文档版本表
CREATE TABLE IF NOT EXISTS document_version (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    commit_id VARCHAR(255) NOT NULL,
    content_path VARCHAR(512) NOT NULL,
    timestamp DATETIME NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    version_tag VARCHAR(50) NOT NULL,
    compare_metadata JSON,
    title VARCHAR(255),
    description TEXT,
    author VA<PERSON>HAR(255),
    last_modified DATETIME NOT NULL,
    is_published BOOLEAN DEFAULT FALSE,
    status VARCHAR(50) DEFAULT 'DRAFT',
    
    -- 索引
    INDEX idx_project_id (project_id),
    INDEX idx_commit_id (commit_id),
    INDEX idx_version_tag (version_tag),
    INDEX idx_timestamp (timestamp),

    -- 唯一约束
    UNIQUE INDEX u_idx_project_version_type (project_id, version_tag, doc_type)
);
