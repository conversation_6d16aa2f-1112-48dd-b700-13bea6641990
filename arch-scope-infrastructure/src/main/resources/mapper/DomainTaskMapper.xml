<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.DomainTaskMapper">
    <resultMap id="BaseResultMap" type="com.archscope.domain.entity.Task">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="task_type" property="taskType" />
        <result column="status" property="status" />
        <result column="priority" property="priority" />
        <result column="progress" property="progress" />
        <result column="created_at" property="createdAt" />
        <result column="started_at" property="startedAt" />
        <result column="completed_at" property="completedAt" />
        <result column="error_message" property="errorMessage" />
        <result column="result_summary" property="resultSummary" />
        <result column="parameters" property="parameters" typeHandler="com.archscope.infrastructure.config.typehandler.MapTypeHandler" />
        <result column="project_id" property="projectId" />
        <result column="created_by" property="createdBy" />
        <result column="assigned_to" property="assignedTo" />
    </resultMap>

    <select id="findNextPendingTask" resultMap="BaseResultMap">
        SELECT * FROM tasks
        WHERE status = 'PENDING'
        ORDER BY priority DESC, created_at ASC
        LIMIT 1
    </select>

    <select id="findNextPendingTaskByType" resultMap="BaseResultMap">
        SELECT * FROM tasks
        WHERE status = 'PENDING' AND task_type = #{taskType}
        ORDER BY priority DESC, created_at ASC
        LIMIT 1
    </select>

    <select id="findAllByStatus" resultMap="BaseResultMap">
        SELECT * FROM tasks
        WHERE status = #{status}
        ORDER BY created_at DESC
    </select>

    <select id="findAllByStatusAndTaskType" resultMap="BaseResultMap">
        SELECT * FROM tasks
        WHERE status = #{status} AND task_type = #{taskType}
        ORDER BY created_at DESC
    </select>

    <select id="findAllByStatusAndProjectId" resultMap="BaseResultMap">
        SELECT * FROM tasks
        WHERE status = #{status} AND project_id = #{projectId}
        ORDER BY created_at DESC
    </select>

    <delete id="deleteCompletedTasksOlderThan">
        DELETE FROM tasks
        WHERE status = 'COMPLETED' AND completed_at &lt; #{date}
    </delete>
</mapper>
