<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.ProjectMapper">
    <resultMap id="BaseResultMap" type="com.archscope.domain.entity.Project">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="repository_url" property="repositoryUrl" />
        <result column="branch" property="branch" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="last_analyzed_at" property="lastAnalyzedAt" />
        <result column="creator_id" property="creatorId" />
        <result column="status" property="status" />
        <result column="active" property="active" />
        <result column="documentation_path" property="documentationPath" />
        <result column="analysis_count" property="analysisCount" />
        <result column="documentation_version" property="documentationVersion" />
        <result column="rating" property="rating" />
        <result column="lines_of_code" property="linesOfCode" />
        <result column="file_count" property="fileCount" />
        <result column="contributor_count" property="contributorCount" />
        <result column="icon" property="icon" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, name, description, repository_url, branch, created_at, updated_at, last_analyzed_at,
        creator_id, status, active, documentation_path, analysis_count, documentation_version,
        rating, lines_of_code, file_count, contributor_count, icon, type
    </sql>

    <!-- 插入项目 -->
    <insert id="insert" parameterType="com.archscope.domain.entity.Project" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO project (
            name, description, repository_url, branch, created_at, updated_at, last_analyzed_at,
            creator_id, status, active, documentation_path, analysis_count, documentation_version,
            rating, lines_of_code, file_count, contributor_count, icon, type
        ) VALUES (
            #{name}, #{description}, #{repositoryUrl}, #{branch}, #{createdAt}, #{updatedAt}, #{lastAnalyzedAt},
            #{creatorId}, #{status}, #{active}, #{documentationPath}, #{analysisCount}, #{documentationVersion},
            #{rating}, #{linesOfCode}, #{fileCount}, #{contributorCount}, #{icon}, #{type}
        )
    </insert>

    <!-- 更新项目 -->
    <update id="updateById" parameterType="com.archscope.domain.entity.Project">
        UPDATE project
        SET
            name = #{name},
            description = #{description},
            repository_url = #{repositoryUrl},
            branch = #{branch},
            updated_at = #{updatedAt},
            last_analyzed_at = #{lastAnalyzedAt},
            creator_id = #{creatorId},
            status = #{status},
            active = #{active},
            documentation_path = #{documentationPath},
            analysis_count = #{analysisCount},
            documentation_version = #{documentationVersion},
            rating = #{rating},
            lines_of_code = #{linesOfCode},
            file_count = #{fileCount},
            contributor_count = #{contributorCount},
            icon = #{icon},
            type = #{type}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询项目 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project
        WHERE id = #{id}
    </select>

    <!-- 根据ID删除项目 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM project WHERE id = #{id}
    </delete>
</mapper>
