<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.TaskMapper">

    <resultMap id="TaskResultMap" type="com.archscope.domain.entity.Task">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="repositoryUrl" column="repository_url"/>
        <result property="branch" column="branch"/>
        <result property="commitHash" column="commit_hash"/>
        <result property="taskType" column="task_type"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="startedAt" column="started_at"/>
        <result property="completedAt" column="completed_at"/>
        <result property="errorMessage" column="error_message"/>
        <result property="resultDetails" column="result_details"/>
    </resultMap>

    <insert id="insert" parameterType="com.archscope.domain.entity.Task" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tasks (project_id, repository_url, branch, commit_hash, task_type, status, created_at)
        VALUES (#{projectId}, #{repositoryUrl}, #{branch}, #{commitHash}, #{taskType}, #{status}, #{createdAt})
    </insert>

    <select id="findById" parameterType="Long" resultMap="TaskResultMap">
        SELECT id, project_id, repository_url, branch, commit_hash, task_type, status, created_at, started_at, completed_at, error_message, result_details
        FROM tasks
        WHERE id = #{id}
    </select>

    <select id="findByStatus" parameterType="com.archscope.domain.valueobject.TaskStatus" resultMap="TaskResultMap">
        SELECT id, project_id, repository_url, branch, commit_hash, task_type, status, created_at, started_at, completed_at, error_message, result_details
        FROM tasks
        WHERE status = #{status}
    </select>

    <update id="updateStatus">
        UPDATE tasks
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <update id="update" parameterType="com.archscope.domain.entity.Task">
        UPDATE tasks
        SET
            project_id = #{projectId},
            repository_url = #{repositoryUrl},
            branch = #{branch},
            commit_hash = #{commitHash},
            task_type = #{taskType},
            status = #{status},
            created_at = #{createdAt},
            started_at = #{startedAt},
            completed_at = #{completedAt},
            error_message = #{errorMessage},
            result_details = #{resultDetails}
        WHERE id = #{id}
    </update>

</mapper>
