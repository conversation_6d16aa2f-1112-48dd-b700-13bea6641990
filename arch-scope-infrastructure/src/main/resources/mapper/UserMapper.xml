<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.archscope.domain.entity.User">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="email" property="email" />
        <result column="full_name" property="fullName" />
        <result column="role" property="role" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="active" property="active" />
        <result column="avatar" property="avatar" />
        <result column="phone" property="phone" />
        <result column="department" property="department" />
        <result column="position" property="position" />
        <result column="last_login_at" property="lastLoginAt" />
        <result column="last_login_ip" property="lastLoginIp" />
    </resultMap>
    
    <select id="selectByRole" resultMap="BaseResultMap">
        SELECT * FROM users WHERE role = #{role}
    </select>
    
    <select id="authenticate" resultMap="BaseResultMap">
        SELECT * FROM users WHERE username = #{username} AND password = #{password} AND active = 1
    </select>
</mapper>
