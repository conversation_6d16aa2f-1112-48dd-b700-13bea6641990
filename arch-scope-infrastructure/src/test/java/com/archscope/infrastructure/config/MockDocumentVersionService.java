package com.archscope.infrastructure.config;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.domain.valueobject.DocumentType;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 文档版本服务的Mock实现，用于测试环境
 */
@Service
@Primary
@Profile("test")
public class MockDocumentVersionService implements DocumentVersionService {

    private final Map<Long, DocumentVersion> documentVersions = new HashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);

    @Override
    public DocumentVersion createDocumentVersion(DocumentVersion documentVersion) {
        if (documentVersion.getId() == null) {
            documentVersion.setId(idGenerator.getAndIncrement());
        }
        
        // 设置创建时间
        if (documentVersion.getTimestamp() == null) {
            documentVersion.setTimestamp(LocalDateTime.now());
        }

        // 设置最后修改时间
        if (documentVersion.getLastModified() == null) {
            documentVersion.setLastModified(LocalDateTime.now());
        }

        // 如果没有版本标签，生成一个
        if (documentVersion.getVersionTag() == null || documentVersion.getVersionTag().isEmpty()) {
            documentVersion.setVersionTag(generateVersionTag(documentVersion.getProjectId(), documentVersion.getDocType()));
        }

        // 默认未发布
        if (documentVersion.getIsPublished() == null) {
            documentVersion.setIsPublished(false);
        }

        // 默认状态为草稿
        if (documentVersion.getStatus() == null || documentVersion.getStatus().isEmpty()) {
            documentVersion.setStatus("DRAFT");
        }
        
        documentVersions.put(documentVersion.getId(), documentVersion);
        return documentVersion;
    }

    @Override
    public Optional<DocumentVersion> getDocumentVersionById(Long id) {
        return Optional.ofNullable(documentVersions.get(id));
    }

    @Override
    public List<DocumentVersion> findByIds(List<Long> ids) {
        return ids.stream()
                .map(documentVersions::get)
                .filter(java.util.Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<DocumentVersion> getDocumentVersionsByProjectId(Long projectId) {
        return documentVersions.values().stream()
                .filter(dv -> dv.getProjectId().equals(projectId))
                .collect(Collectors.toList());
    }

    @Override
    public List<DocumentVersion> findPublishedByProjectId(Long projectId) {
        return documentVersions.values().stream()
                .filter(dv -> dv.getProjectId().equals(projectId) && Boolean.TRUE.equals(dv.getIsPublished()))
                .collect(Collectors.toList());
    }

    @Override
    public List<DocumentVersion> getDocumentVersionsByProjectIdAndType(Long projectId, DocumentType docType) {
        return documentVersions.values().stream()
                .filter(dv -> dv.getProjectId().equals(projectId) && dv.getDocType().equals(docType))
                .collect(Collectors.toList());
    }

    @Override
    public List<DocumentVersion> getDocumentVersionsByProjectIdAndCommitId(Long projectId, String commitId) {
        return documentVersions.values().stream()
                .filter(dv -> dv.getProjectId().equals(projectId) && dv.getCommitId().equals(commitId))
                .collect(Collectors.toList());
    }

    @Override
    public Optional<DocumentVersion> getDocumentVersionByProjectIdAndVersionTag(Long projectId, String versionTag) {
        return documentVersions.values().stream()
                .filter(dv -> dv.getProjectId().equals(projectId) && dv.getVersionTag().equals(versionTag))
                .findFirst();
    }

    @Override
    public Optional<DocumentVersion> getLatestDocumentVersionByProjectIdAndType(Long projectId, DocumentType docType) {
        return documentVersions.values().stream()
                .filter(dv -> dv.getProjectId().equals(projectId) && dv.getDocType().equals(docType))
                .max(java.util.Comparator.comparing(DocumentVersion::getTimestamp));
    }

    @Override
    public DocumentVersion updateDocumentVersion(DocumentVersion documentVersion) {
        documentVersion.setLastModified(LocalDateTime.now());
        documentVersions.put(documentVersion.getId(), documentVersion);
        return documentVersion;
    }

    @Override
    public void deleteDocumentVersion(Long id) {
        documentVersions.remove(id);
    }

    @Override
    public int deleteDocumentVersionsByProjectId(Long projectId) {
        List<Long> idsToRemove = documentVersions.values().stream()
                .filter(dv -> dv.getProjectId().equals(projectId))
                .map(DocumentVersion::getId)
                .collect(Collectors.toList());
        
        idsToRemove.forEach(documentVersions::remove);
        return idsToRemove.size();
    }

    @Override
    public String compareDocumentVersions(Long versionId1, Long versionId2) {
        DocumentVersion version1 = documentVersions.get(versionId1);
        DocumentVersion version2 = documentVersions.get(versionId2);
        
        if (version1 == null || version2 == null) {
            throw new IllegalArgumentException("文档版本不存在");
        }
        
        return "文档比较结果：版本 " + version1.getVersionTag() + " 与版本 " + version2.getVersionTag() + " 的差异";
    }

    @Override
    public DocumentVersion publishDocumentVersion(Long id) {
        DocumentVersion version = documentVersions.get(id);
        if (version == null) {
            throw new IllegalArgumentException("文档版本不存在");
        }
        
        version.setIsPublished(true);
        version.setStatus("PUBLISHED");
        version.setLastModified(LocalDateTime.now());
        
        return version;
    }

    @Override
    public DocumentVersion unpublishDocumentVersion(Long id) {
        DocumentVersion version = documentVersions.get(id);
        if (version == null) {
            throw new IllegalArgumentException("文档版本不存在");
        }
        
        version.setIsPublished(false);
        version.setStatus("DRAFT");
        version.setLastModified(LocalDateTime.now());
        
        return version;
    }

    @Override
    public String generateVersionTag(Long projectId, DocumentType docType) {
        Optional<DocumentVersion> latestVersionOpt = getLatestDocumentVersionByProjectIdAndType(projectId, docType);
        
        if (latestVersionOpt.isEmpty()) {
            return "v1.0.0";
        }
        
        String latestVersionTag = latestVersionOpt.get().getVersionTag();
        
        // 解析版本号
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("v(\\d+)\\.(\\d+)\\.(\\d+)");
        java.util.regex.Matcher matcher = pattern.matcher(latestVersionTag);
        
        if (matcher.find()) {
            int major = Integer.parseInt(matcher.group(1));
            int minor = Integer.parseInt(matcher.group(2));
            int patch = Integer.parseInt(matcher.group(3));
            
            // 增加补丁版本号
            patch++;
            
            // 如果补丁版本号超过9，增加次要版本号
            if (patch > 9) {
                patch = 0;
                minor++;
            }
            
            // 如果次要版本号超过9，增加主要版本号
            if (minor > 9) {
                minor = 0;
                major++;
            }
            
            return String.format("v%d.%d.%d", major, minor, patch);
        } else {
            return "v1.0.0";
        }
    }
}
