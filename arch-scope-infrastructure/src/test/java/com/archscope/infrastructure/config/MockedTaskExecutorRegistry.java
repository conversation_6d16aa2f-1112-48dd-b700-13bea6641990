package com.archscope.infrastructure.config;

import com.archscope.domain.task.TaskExecutor;
import com.archscope.domain.task.TaskExecutorRegistry;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * 任务执行器注册表的模拟配置
 * 用于测试环境，提供一个空的任务执行器注册表
 */
@TestConfiguration
public class MockedTaskExecutorRegistry {

    @Bean
    @Primary
    public TaskExecutorRegistry taskExecutorRegistry() {
        TaskExecutorRegistry registry = new TaskExecutorRegistry();
        
        // 注册模拟的任务执行器
        registry.registerExecutor(new MockedCodeParseTaskExecutor());
        registry.registerExecutor(new MockedDocGenerateTaskExecutor());
        
        return registry;
    }
    
    /**
     * 模拟的代码解析任务执行器
     */
    private static class MockedCodeParseTaskExecutor implements TaskExecutor {
        
        @Override
        public String getTaskType() {
            return "CODE_PARSE";
        }
        
        @Override
        public void execute(com.archscope.domain.entity.Task task) {
            // 模拟执行，不做任何实际操作
        }
        
        @Override
        public boolean cancel(Long taskId) {
            // 模拟取消，始终返回成功
            return true;
        }
    }
    
    /**
     * 模拟的文档生成任务执行器
     */
    private static class MockedDocGenerateTaskExecutor implements TaskExecutor {
        
        @Override
        public String getTaskType() {
            return "DOC_GENERATE";
        }
        
        @Override
        public void execute(com.archscope.domain.entity.Task task) {
            // 模拟执行，不做任何实际操作
        }
        
        @Override
        public boolean cancel(Long taskId) {
            // 模拟取消，始终返回成功
            return true;
        }
    }
}
