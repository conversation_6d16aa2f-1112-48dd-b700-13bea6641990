package com.archscope.infrastructure.messaging;

import com.archscope.domain.message.TaskMessage;
import com.archscope.domain.valueobject.TaskType;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.messaging.Message;

import java.time.LocalDateTime;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class RocketMQMessageServiceTest {
    @Mock
    private RocketMQTemplate rocketMQTemplate;

    @InjectMocks
    private RocketMQMessageService rocketMQMessageService;

    private TaskMessage buildTaskMessage(TaskType type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("key", "value");

        return TaskMessage.builder()
                .taskId(1L)
                .projectId(100L)
                .taskType(type)
                .priority(1)
                .createdAt(LocalDateTime.now())
                .parameters(params)
                .build();
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSendTaskMessage_success() {
        doNothing().when(rocketMQTemplate).convertAndSend(anyString(), any(TaskMessage.class));
        boolean result = rocketMQMessageService.sendTaskMessage("test-topic", buildTaskMessage(TaskType.CODE_PARSE));
        assertTrue(result);
        verify(rocketMQTemplate, times(1)).convertAndSend(eq("test-topic"), any(TaskMessage.class));
    }

    @Test
    void testSendTaskMessage_failure() {
        doThrow(new RuntimeException("send error")).when(rocketMQTemplate).convertAndSend(anyString(), any(TaskMessage.class));
        boolean result = rocketMQMessageService.sendTaskMessage("test-topic", buildTaskMessage(TaskType.DOC_GENERATE));
        assertFalse(result);
        verify(rocketMQTemplate, times(1)).convertAndSend(eq("test-topic"), any(TaskMessage.class));
    }

    @Test
    void testSendTaskMessageAsync_success() {
        doAnswer(invocation -> {
            SendCallback callback = invocation.getArgument(2);
            callback.onSuccess(mock(SendResult.class));
            return null;
        }).when(rocketMQTemplate).asyncSend(anyString(), any(TaskMessage.class), any(SendCallback.class));
        boolean result = rocketMQMessageService.sendTaskMessageAsync("test-topic", buildTaskMessage(TaskType.CODE_PARSE));
        assertTrue(result);
        verify(rocketMQTemplate, times(1)).asyncSend(eq("test-topic"), any(TaskMessage.class), any(SendCallback.class));
    }

    @Test
    void testSendTaskMessageAsync_failure() {
        doThrow(new RuntimeException("async error")).when(rocketMQTemplate).asyncSend(anyString(), any(TaskMessage.class), any(SendCallback.class));
        boolean result = rocketMQMessageService.sendTaskMessageAsync("test-topic", buildTaskMessage(TaskType.DOC_GENERATE));
        assertFalse(result);
        verify(rocketMQTemplate, times(1)).asyncSend(eq("test-topic"), any(TaskMessage.class), any(SendCallback.class));
    }

    @Test
    void testSendTaskMessageDelay_success() {
        // 模拟 getProducer() 方法
        org.apache.rocketmq.client.producer.DefaultMQProducer producer = mock(org.apache.rocketmq.client.producer.DefaultMQProducer.class);
        when(rocketMQTemplate.getProducer()).thenReturn(producer);
        when(producer.getSendMsgTimeout()).thenReturn(3000);

        // 使用 when().thenReturn() 而不是 doNothing()
        when(rocketMQTemplate.syncSend(anyString(), any(Message.class), anyLong(), anyInt())).thenReturn(null);
        boolean result = rocketMQMessageService.sendTaskMessageDelay("test-topic", buildTaskMessage(TaskType.CODE_PARSE), 3);
        assertTrue(result);
        verify(rocketMQTemplate, times(1)).syncSend(eq("test-topic"), any(Message.class), eq(3000L), eq(3));
    }

    @Test
    void testSendTaskMessageDelay_failure() {
        // 模拟 getProducer() 方法
        org.apache.rocketmq.client.producer.DefaultMQProducer producer = mock(org.apache.rocketmq.client.producer.DefaultMQProducer.class);
        when(rocketMQTemplate.getProducer()).thenReturn(producer);
        when(producer.getSendMsgTimeout()).thenReturn(3000);

        doThrow(new RuntimeException("delay error")).when(rocketMQTemplate).syncSend(anyString(), any(Message.class), anyLong(), anyInt());
        boolean result = rocketMQMessageService.sendTaskMessageDelay("test-topic", buildTaskMessage(TaskType.DOC_GENERATE), 2);
        assertFalse(result);
        verify(rocketMQTemplate, times(1)).syncSend(eq("test-topic"), any(Message.class), eq(3000L), eq(2));
    }
}