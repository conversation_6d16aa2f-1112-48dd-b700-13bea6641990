package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.Project;
import com.archscope.infrastructure.TestApplication;
import com.archscope.infrastructure.config.TestAutoConfiguration;
import com.archscope.infrastructure.cache.ProjectCacheService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = TestApplication.class)
@Import(TestAutoConfiguration.class)
@ActiveProfiles("test")
public class CachedProjectRepositoryTest {

    @Mock
    private MyBatisProjectRepository delegate;

    @Mock
    private ProjectCacheService cacheService;

    @InjectMocks
    private CachedProjectRepository repository;

    @Test
    public void testFindById_CacheHit() {
        // 准备测试数据
        Long projectId = 1L;
        Project project = createTestProject(projectId);
        
        // 配置缓存命中
        when(cacheService.getProjectById(projectId)).thenReturn(Optional.of(project));
        
        // 执行测试
        Optional<Project> result = repository.findById(projectId);
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(projectId, result.get().getId());
        assertEquals("Test Project", result.get().getName());
        
        // 验证缓存被使用，委托未被调用
        verify(cacheService).getProjectById(projectId);
        verify(delegate, never()).findById(projectId);
    }
    
    @Test
    public void testFindById_CacheMiss() {
        // 准备测试数据
        Long projectId = 1L;
        Project project = createTestProject(projectId);
        
        // 配置缓存未命中，委托返回数据
        when(cacheService.getProjectById(projectId)).thenReturn(Optional.empty());
        when(delegate.findById(projectId)).thenReturn(Optional.of(project));
        
        // 执行测试
        Optional<Project> result = repository.findById(projectId);
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(projectId, result.get().getId());
        
        // 验证缓存被检查，委托被调用，缓存被更新
        verify(cacheService).getProjectById(projectId);
        verify(delegate).findById(projectId);
        verify(cacheService).cacheProject(project);
    }
    
    @Test
    public void testFindAll_CacheHit() {
        // 准备测试数据
        List<Project> projects = Arrays.asList(
                createTestProject(1L),
                createTestProject(2L)
        );
        
        // 配置缓存命中
        when(cacheService.getAllProjects()).thenReturn(Optional.of(projects));
        
        // 执行测试
        List<Project> result = repository.findAll();
        
        // 验证结果
        assertEquals(2, result.size());
        
        // 验证缓存被使用，委托未被调用
        verify(cacheService).getAllProjects();
        verify(delegate, never()).findAll();
    }
    
    @Test
    public void testFindAll_CacheMiss() {
        // 准备测试数据
        List<Project> projects = Arrays.asList(
                createTestProject(1L),
                createTestProject(2L)
        );
        
        // 配置缓存未命中，委托返回数据
        when(cacheService.getAllProjects()).thenReturn(Optional.empty());
        when(delegate.findAll()).thenReturn(projects);
        
        // 执行测试
        List<Project> result = repository.findAll();
        
        // 验证结果
        assertEquals(2, result.size());
        
        // 验证缓存被检查，委托被调用，缓存被更新
        verify(cacheService).getAllProjects();
        verify(delegate).findAll();
        verify(cacheService).cacheProjectList(projects);
    }
    
    @Test
    public void testSave() {
        // 准备测试数据
        Project project = createTestProject(1L);
        
        // 配置委托返回数据
        when(delegate.save(project)).thenReturn(project);
        
        // 执行测试
        Project result = repository.save(project);
        
        // 验证结果
        assertEquals(project.getId(), result.getId());
        
        // 验证委托被调用，缓存被更新
        verify(delegate).save(project);
        verify(cacheService).cacheProject(project);
        verify(cacheService).removeProjectCache(project.getId());
    }
    
    @Test
    public void testDeleteById() {
        // 准备测试数据
        Long projectId = 1L;
        
        // 执行测试
        repository.deleteById(projectId);
        
        // 验证委托被调用，缓存被清除
        verify(delegate).deleteById(projectId);
        verify(cacheService).removeProjectCache(projectId);
    }
    
    /**
     * 创建测试项目对象
     */
    private Project createTestProject(Long id) {
        return Project.builder()
                .id(id)
                .name("Test Project")
                .description("Test Description")
                .repositoryUrl("https://github.com/test/project" + id + ".git")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
} 