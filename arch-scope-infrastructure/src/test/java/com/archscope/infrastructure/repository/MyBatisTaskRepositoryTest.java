package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.infrastructure.mapper.DomainTaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MyBatisTaskRepositoryTest {

    @Mock
    private DomainTaskMapper domainTaskMapper;

    @InjectMocks
    private MyBatisTaskRepository taskRepository;

    @Captor
    private ArgumentCaptor<LambdaQueryWrapper<Task>> queryWrapperCaptor;

    private Task testTask;

    @BeforeEach
    void setUp() {
        testTask = Task.builder()
                .id(1L)
                .name("Test Task")
                .description("Test task description")
                .taskType("CODE_PARSE")
                .status(TaskStatus.PENDING)
                .priority(5)
                .progress(0)
                .createdAt(LocalDateTime.now())
                .parameters(new HashMap<String, Object>())
                .projectId(101L)
                .build();
    }

    @Test
    void save_insert() {
        Task newTask = Task.builder()
                .name("New Task")
                .description("New task description")
                .taskType("DOC_GENERATE")
                .status(TaskStatus.PENDING)
                .priority(3)
                .projectId(102L)
                .build();

        when(domainTaskMapper.insert(any(Task.class))).thenReturn(1);

        Task result = taskRepository.save(newTask);

        assertEquals("New Task", result.getName());
        verify(domainTaskMapper, times(1)).insert(any(Task.class));
    }

    @Test
    void save_update() {
        when(domainTaskMapper.updateById(any(Task.class))).thenReturn(1);

        Task result = taskRepository.save(testTask);

        assertEquals("Test Task", result.getName());
        verify(domainTaskMapper, times(1)).updateById(any(Task.class));
    }

    @Test
    void findById() {
        when(domainTaskMapper.selectById(1L)).thenReturn(testTask);

        Optional<Task> result = taskRepository.findById(1L);

        assertTrue(result.isPresent());
        assertEquals("Test Task", result.get().getName());
        verify(domainTaskMapper, times(1)).selectById(1L);
    }

    @Test
    void findAll() {
        List<Task> tasks = Arrays.asList(
                testTask,
                Task.builder().id(2L).name("Task 2").build()
        );
        when(domainTaskMapper.selectList(null)).thenReturn(tasks);

        List<Task> result = taskRepository.findAll();

        assertEquals(2, result.size());
        verify(domainTaskMapper, times(1)).selectList(null);
    }

    @Test
    void findByStatus() {
        List<Task> tasks = Arrays.asList(
                testTask,
                Task.builder().id(2L).name("Task 2").status(TaskStatus.PENDING).build()
        );
        when(domainTaskMapper.findAllByStatus(TaskStatus.PENDING)).thenReturn(tasks);

        List<Task> result = taskRepository.findAllByStatus(TaskStatus.PENDING);

        assertEquals(2, result.size());
        verify(domainTaskMapper, times(1)).findAllByStatus(TaskStatus.PENDING);
    }

    @Test
    void findAllByStatusAndTaskType() {
        List<Task> tasks = Arrays.asList(
                testTask,
                Task.builder().id(2L).name("Task 2").status(TaskStatus.PENDING).taskType("CODE_PARSE").build()
        );
        when(domainTaskMapper.findAllByStatusAndTaskType(TaskStatus.PENDING, "CODE_PARSE")).thenReturn(tasks);

        List<Task> result = taskRepository.findAllByStatusAndTaskType(TaskStatus.PENDING, "CODE_PARSE");

        assertEquals(2, result.size());
        verify(domainTaskMapper, times(1)).findAllByStatusAndTaskType(TaskStatus.PENDING, "CODE_PARSE");
    }

    @Test
    void findAllByStatusAndProjectId() {
        List<Task> tasks = Arrays.asList(
                testTask,
                Task.builder().id(2L).name("Task 2").status(TaskStatus.PENDING).projectId(101L).build()
        );
        when(domainTaskMapper.findAllByStatusAndProjectId(TaskStatus.PENDING, 101L)).thenReturn(tasks);

        List<Task> result = taskRepository.findAllByStatusAndProjectId(TaskStatus.PENDING, 101L);

        assertEquals(2, result.size());
        verify(domainTaskMapper, times(1)).findAllByStatusAndProjectId(TaskStatus.PENDING, 101L);
    }

    @Test
    void findNextPendingTask() {
        when(domainTaskMapper.findNextPendingTask()).thenReturn(testTask);

        Optional<Task> result = taskRepository.findNextPendingTask();

        assertTrue(result.isPresent());
        assertEquals("Test Task", result.get().getName());
        verify(domainTaskMapper, times(1)).findNextPendingTask();
    }

    @Test
    void findNextPendingTaskByType() {
        when(domainTaskMapper.findNextPendingTaskByType("CODE_PARSE")).thenReturn(testTask);

        Optional<Task> result = taskRepository.findNextPendingTaskByType("CODE_PARSE");

        assertTrue(result.isPresent());
        assertEquals("Test Task", result.get().getName());
        verify(domainTaskMapper, times(1)).findNextPendingTaskByType("CODE_PARSE");
    }

    @Test
    void update() {
        when(domainTaskMapper.updateById(any(Task.class))).thenReturn(1);

        Task result = taskRepository.update(testTask);

        assertEquals("Test Task", result.getName());
        verify(domainTaskMapper, times(1)).updateById(any(Task.class));
    }

    @Test
    void deleteById() {
        doNothing().when(domainTaskMapper).deleteById(1L);

        taskRepository.deleteById(1L);

        verify(domainTaskMapper, times(1)).deleteById(1L);
    }

    @Test
    void deleteCompletedTasksOlderThan() {
        LocalDateTime date = LocalDateTime.now().minusDays(30);
        when(domainTaskMapper.deleteCompletedTasksOlderThan(date)).thenReturn(5);

        int result = taskRepository.deleteCompletedTasksOlderThan(date);

        assertEquals(5, result);
        verify(domainTaskMapper, times(1)).deleteCompletedTasksOlderThan(date);
    }

    @Test
    void findByProjectId() {
        List<Task> tasks = Arrays.asList(
                testTask,
                Task.builder().id(2L).name("Task 2").projectId(101L).build()
        );
        when(domainTaskMapper.selectList(any())).thenReturn(tasks);

        List<Task> result = taskRepository.findByProjectId(101L);

        assertEquals(2, result.size());
        verify(domainTaskMapper, times(1)).selectList(any());
    }
}
