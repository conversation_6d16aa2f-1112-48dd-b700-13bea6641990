package com.archscope.config;

import com.archscope.domain.service.DocumentGenerationService;
import com.archscope.domain.service.IncrementalParseService;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.task.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * 任务系统配置
 */
@Slf4j
@Configuration
public class TaskConfig {

    private TaskScheduler taskScheduler;
    private TaskMonitor taskMonitor;

    /**
     * 创建任务执行器注册表
     *
     * @return 任务执行器注册表
     */
    @Bean
    public TaskExecutorRegistry taskExecutorRegistry() {
        return new TaskExecutorRegistry();
    }

    /**
     * 创建代码解析任务执行器
     *
     * @param incrementalParseService 增量解析服务
     * @param taskQueueService        任务队列服务
     * @return 代码解析任务执行器
     */
    @Bean
    public CodeParseTaskExecutor codeParseTaskExecutor(
            IncrementalParseService incrementalParseService,
            TaskQueueService taskQueueService) {
        return new CodeParseTaskExecutor(incrementalParseService, taskQueueService);
    }

    /**
     * 创建文档生成任务执行器
     *
     * @param documentGenerationService 文档生成服务
     * @param taskQueueService          任务队列服务
     * @return 文档生成任务执行器
     */
    @Bean
    public DocGenerateTaskExecutor docGenerateTaskExecutor(
            DocumentGenerationService documentGenerationService,
            TaskQueueService taskQueueService) {
        return new DocGenerateTaskExecutor(documentGenerationService, taskQueueService);
    }

    /**
     * 创建任务调度器
     *
     * @param taskQueueService     任务队列服务
     * @param taskExecutorRegistry 任务执行器注册表
     * @return 任务调度器
     */
    @Bean
    public TaskScheduler taskScheduler(
            TaskQueueService taskQueueService,
            TaskExecutorRegistry taskExecutorRegistry) {
        try {
            taskScheduler = new TaskScheduler(taskQueueService, taskExecutorRegistry);
            taskScheduler.start(60, 300);
            return taskScheduler;
        } catch (Exception e) {
            log.error("启动任务调度器失败: {}", e.getMessage(), e);
            // 返回一个空的调度器，避免应用启动失败
            return new TaskScheduler(taskQueueService, taskExecutorRegistry);
        }
    }

    /**
     * 创建任务监控器
     *
     * @param taskQueueService 任务队列服务
     * @return 任务监控器
     */
    @Bean
    public TaskMonitor taskMonitor(TaskQueueService taskQueueService) {
        try {
            taskMonitor = new TaskMonitor(taskQueueService);
            taskMonitor.start(30, 150);
            return taskMonitor;
        } catch (Exception e) {
            log.error("启动任务监控器失败: {}", e.getMessage(), e);
            // 返回一个空的监控器，避免应用启动失败
            return new TaskMonitor(taskQueueService);
        }
    }

    /**
     * 关闭任务系统
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭任务系统");

        // 停止任务调度器
        if (taskScheduler != null) {
            try {
                taskScheduler.stop();
            } catch (Exception e) {
                log.error("停止任务调度器失败: {}", e.getMessage(), e);
            }
        }

        // 停止任务监控器
        if (taskMonitor != null) {
            try {
                taskMonitor.stop();
            } catch (Exception e) {
                log.error("停止任务监控器失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 注册任务执行器
     *
     * @param taskExecutorRegistry    任务执行器注册表
     * @param codeParseTaskExecutor   代码解析任务执行器
     * @param docGenerateTaskExecutor 文档生成任务执行器
     */
    @Bean
    public TaskExecutorRegistry registerTaskExecutors(
            TaskExecutorRegistry taskExecutorRegistry,
            CodeParseTaskExecutor codeParseTaskExecutor,
            DocGenerateTaskExecutor docGenerateTaskExecutor) {
        try {
            log.info("注册任务执行器");

            // 注册代码解析任务执行器
            taskExecutorRegistry.registerExecutor(codeParseTaskExecutor);

            // 注册文档生成任务执行器
            taskExecutorRegistry.registerExecutor(docGenerateTaskExecutor);
        } catch (Exception e) {
            log.error("注册任务执行器失败: {}", e.getMessage(), e);
        }

        return taskExecutorRegistry;
    }
}
