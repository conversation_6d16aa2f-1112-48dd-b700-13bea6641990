server:
  port: 8080

spring:
  application:
    name: arch-scope
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: root
    password: root
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  redis:
    host: localhost
    port: 6379
    password: 'test'
    database: 0
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
  cache:
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: true
      use-key-prefix: true
      key-prefix: "arch-scope:"

# Redisson配置
redisson:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 传输模式
  transportMode: NIO
  # 单节点配置
  singleServerConfig:
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 连接超时，单位：毫秒
    connectTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 命令失败重试次数
    retryAttempts: 3
    # 命令重试发送时间间隔，单位：毫秒
    retryInterval: 1500
    # 发布和订阅连接的最小空闲连接数
    subscriptionConnectionMinimumIdleSize: 1
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 数据库编号
    database: 0
    # DNS监测时间间隔，单位：毫秒
    dnsMonitoringInterval: 5000

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.archscope.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto

rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: ${ROCKETMQ_PRODUCER_GROUP:arch-scope-producer}
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    compress-message-body-threshold: 4096
    retry-next-server: true
    max-message-size: 4194304
  # 容错模式 - 当 RocketMQ 不可用时允许应用正常启动
  fault-tolerant: true
  # 消费者相关配置
  consumer:
    # 是否启用消费者
    # 由应用启动时自动设置，也可以手动配置
    enabled: true
    # 是否在启动时立即注册消费者
    pull-timeout: 1000
    # 启动失败时的等待时间(毫秒)
    max-reconnect-wait: 5000


logging:
  level:
    root: INFO
    com.archscope: DEBUG

prompts:
  directory: prompts

openai:
  api:
    key: ${OPENAI_API_KEY:your-api-key}
    model: gpt-4

app:
  jwt:
    secret: defaultSecretKeyForJwtInMainEnvironment
    expirationMs: 3600000
    refreshExpirationMs: 86400000
