### ADR-003: MVP阶段认证与授权策略

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope系统需要一个用户认证和授权机制来保护其功能和数据。根据最新需求澄清，系统将区分公开功能（任何人可访问）和受保护的管理功能（需要管理员身份）。企业级的单点登录 (SSO) 集成将在MVP阶段之后实现，但管理员角色的认证最终将依赖SSO。客户将提供一个单独的拦截器在SSO集成后处理权限，ArchScope只需使用此权限上下文。我们需要为MVP阶段定义一个可行的、临时的（如果需要）或部分开放的认证与授权策略，并为未来平滑过渡到完整SSO集成做好规划。

**决策驱动因素 (Decision Drivers):**
* **FR (综合):** 支持匿名用户提交项目和查看公开文档。支持管理员（通过SSO认证后）管理项目（如设置可见性/状态）。
* **项目进度:** SSO集成被允许放在最后实现。
* **MVP目标:** 快速验证核心功能流，特别是匿名贡献和公开文档查看。
* **安全性:** 即使是MVP，也需要考虑对潜在的管理接口进行适当的保护，避免数据被随意修改或删除。
* **用户体验:** 匿名用户的访问应尽可能 frictionless。
* **运维:** MVP阶段的认证方案不应过于复杂以致难以部署和测试。

**考虑的方案 (Considered Options):**

* **方案1: MVP阶段完全无认证**
    * 所有API端点（包括添加项目、触发解析、设置项目可见性/状态）均对公网开放，不进行任何认证或授权检查。
    * **优点:** 实现最简单，开发速度最快。
    * **缺点:** 安全性极低，任何人都可执行管理操作，不适用于任何有真实数据或部署在非完全受控环境的场景。不符合“只有管理员才能修改项目信息”的需求。

* **方案2: MVP阶段引入临时本地用户认证 (用户名/密码)**
    * 在ArchScope内部创建一个简单的用户表，存储用户名、哈希密码和基本角色（如ADMIN, USER）。
    * `facade`实现一个基础的登录API，颁发JWT或Session。
    * 管理类API需要此本地认证。公开API保持匿名。
    * **优点:** 提供了用户身份区分和基本的角色管理，为后续SSO用户映射提供基础。安全性高于方案1。
    * **缺点:** 增加了MVP的开发工作量（用户管理、登录API、密码存储安全）。与最终SSO方案存在差异，后续需要数据迁移或账户映射。

* **方案3: MVP阶段对管理API使用全局API Key保护**
    * 为所有需要保护的管理类API设置一个全局的、配置在后端的API Key。调用这些API时，请求头必须携带此Key。
    * 公开API保持匿名。
    * **优点:** 实现相对简单，提供了一层基础保护，阻止了完全匿名的管理操作。不需要用户管理和登录界面。
    * **缺点:** 无法区分具体管理员用户，所有管理员共享一个Key，不利于审计。API Key的保管和轮换需要管理。

* **方案4: MVP阶段区分API：公开API匿名访问，管理API依赖未来SSO (MVP暂不实现或硬编码管理员逻辑)**
    * **公开API (MVP实现):**
        * `POST /projects` (匿名提交项目，项目初始状态为`PENDING_REVIEW`, `visibility=INTERNAL`)。
        * `GET /projects/{id}/documentation/**` (查看文档，后端检查`project.visibility == 'PUBLIC'` 和 `project.status == 'AVAILABLE'`)。
        * `GET /system/error-codes`。
    * **受保护的管理API (MVP暂不提供完整功能，或提供极简内部接口):**
        * 如设置项目为`PUBLIC`和`AVAILABLE`、触发指定项目解析等。这些API在`api-spec.yaml`中标记为需要SSO认证和管理员角色。
        * **MVP实现方式：**
            * **选项4a (无管理功能):** MVP阶段完全不提供这些管理API的外部访问，项目状态和可见性通过数据库脚本或其他内部工具手动修改，仅用于测试和演示公开文档流程。
            * **选项4b (硬编码/配置管理员标识):** MVP阶段的管理API（如果必须有，如手动触发解析）可能依赖一个硬编码在配置中的“管理员标识符”或一个简单的内部Token，不暴露给普通用户。
            * **选项4c (依赖SSO但推迟):** 明确这些API依赖SSO，如果SSO未集成，则这些API不可用或返回特定错误。
    * **优点:**
        * 严格分离了公开功能和管理功能。
        * 确保了管理功能的安全性最终依赖于SSO，符合长期目标。
        * MVP可以聚焦于核心的匿名提交流程和公开文档查看流程。
    * **缺点:**
        * 如果MVP需要一些基础的管理操作（如将项目设为公开），选项4a会导致流程不完整。选项4b的硬编码方案不安全。选项4c意味着SSO不集成则部分功能缺失。

**决策 (Decision):**

我们决定采用 **方案4的组合策略，并明确SSO集成为MVP的一部分，但其权限处理依赖外部拦截器**：

1.  **公开API (MVP阶段实现并可用):**
    * `POST /projects`: 任何人可匿名提交项目。项目初始状态为 `PENDING_REVIEW` (或 `INTERNAL`) 和 `PENDING_ANALYSIS` (或 `UNAVAILABLE`)。
    * `GET /projects/{projectId}/documentation/latest/**`: 任何人可查看。后端逻辑必须校验 `project.visibility == 'PUBLIC'` 且 `project.status == 'AVAILABLE'`。
    * `GET /system/error-codes`: 任何人可查看。
    * *这些API不进行SSO认证检查。*

2.  **受保护的管理API (MVP阶段实现，并依赖SSO及外部权限拦截器):**
    * `GET /admin/projects`, `GET /admin/projects/{projectId}`, `PUT /admin/projects/{projectId}/visibility`, `PUT /admin/projects/{projectId}/status`, `POST /admin/projects/{projectId}/trigger-analysis`, `GET /admin/tasks`, `GET /admin/tasks/{taskId}` 等。
    * 这些API**必须通过SSO认证**，并且需要用户具有“管理员”角色。
    * **SSO集成是MVP的一部分。** ArchScope后端（`archscope-main`和`archscope-infrastructure`的`SSOAdapter`）将配置为与企业SSO IdP（OIDC/SAML）集成，处理认证回调并建立安全上下文。
    * **权限处理依赖外部拦截器：** 客户将提供一个单独的拦截器，该拦截器在SSO认证成功后运行，负责从SSO断言中解析用户角色/组，并将相应的权限信息（如用户是否为`ARCHSCOPE_ADMIN`）注入到请求上下文中（如Spring `SecurityContextHolder`）。
    * ArchScope的API控制器 (`archscope-facade`) 和应用服务 (`archscope-app`) 将信任并使用这个上下文中的角色信息进行授权决策（例如，使用 `@PreAuthorize("hasRole('ARCHSCOPE_ADMIN')")`）。
    * *这意味着，虽然SSO对接本身可能不费事，但它是MVP中管理员功能能够运作的前提。*

理由:
1.  **明确功能边界:** 清晰区分了匿名用户可访问的公共服务和管理员专属的管理服务。
2.  **安全性保障:** 所有管理和修改操作都置于SSO的保护之下，符合安全最佳实践。
3.  **MVP完整性:** 包含管理员将匿名提交的项目审核并设为公开的流程，使得从匿名提交到公开可看的整个核心用户旅程在MVP中是闭环的。
4.  **符合客户技术栈:** 利用客户提供的SSO和权限拦截器，减少ArchScope自身的权限管理复杂性。
5.  **长期一致性:** MVP阶段的认证授权机制与最终方案保持一致，避免了临时方案的迁移成本。

**后果 (Consequences):**

* **积极的:**
    * MVP阶段就拥有了相对完善和安全的认证授权体系。
    * 核心业务流程（包括管理员审核）可以在MVP中得到完整验证。
    * 为后续所有需要权限控制的功能奠定了基础。
* **消极的/风险/需要注意的:**
    * **SSO集成工作量:** 虽然客户认为对接不费事，但SSO集成（OIDC/SAML客户端配置、回调处理、属性映射、会话管理、错误处理、登出）仍然是MVP阶段的一项明确的开发任务，需要投入时间和进行充分测试。
    * **外部拦截器依赖:** ArchScope的授权逻辑依赖于外部拦截器正确注入角色信息。需要与客户方明确该拦截器的接口和行为。
    * **本地开发/测试环境SSO模拟:** 可能需要配置一个本地的模拟SSO IdP (如Keycloak Docker实例) 或开发模式下的Bypass机制，以便在没有连接到企业SSO环境时进行开发和测试。
* **需要进一步的工作/决策:**
    * 详细设计`archscope-main`中的Spring Security配置，以集成企业SSO并与外部权限拦截器协作。
    * 明确从外部拦截器获取角色信息的具体方式（例如，是通过`HttpServletRequest.isUserInRole()`还是从`SecurityContext`中获取`GrantedAuthority`）。
    * 为本地开发和测试环境制定SSO模拟或Bypass策略。

**其他相关信息 (Optional):**
* 参考 `architecture.md` 中关于SSO集成和公开/受保护API的论述。
* 参考 `prd.md` 中FR-AUTH部分的更新。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队 (客户反馈整合)