## API 详细设计

**基础约定:**

* **根路径 (Base Path)**: `/api/v1`
* **数据格式 (Data Format)**: 所有请求体均为 `application/json` (UTF-8编码)。
* **认证 (Authentication)**: MVP阶段，面向最终用户的API端点无需认证。
* **统一响应格式**:
    * **成功响应**:
        ```json
        {
          "success": true,
          "code": "000000", // 统一成功码，或可根据业务场景定义更细致的成功码
          "message": "操作成功", // 具体的操作成功信息
          "data": {
            // 实际业务数据 DTO
          }
        }
        ```
    * **失败响应**:
        ```json
        {
          "success": false,
          "code": "XXXXXX", // 具体的业务/系统错误码 (如 400001, 404001, 500001)
          "message": "具体的错误描述信息",
          "data": null // 或包含少量辅助调试信息的对象，但通常为null
        }
        ```
      *HTTP状态码依然会正确设置 (如 200, 201, 400, 404, 500等)，响应体遵循此结构。*

* **分页 (Pagination)**: 对于列表查询接口，其分页结果对象 (`PagedResult<T>`) 将被置于成功响应的 `data` 字段下。
    * `PagedResult<T>` 结构:
        ```json
        {
          "content": [ /* T类型的列表数据 */ ],
          "pageNumber": 0, // 当前页码 (从0开始)
          "pageSize": 20,  // 每页数量
          "totalElements": 100, // 总记录数
          "totalPages": 5 // 总页数
        }
        ```

**预定义业务响应码 (示例，需后续细化):**
* `000000`: 操作成功
* `A00001`: 用户端错误 (大类，如参数校验失败)
    * `A00101`: 请求参数无效
    * `A00102`: 必要参数缺失
* `A00201`: 资源不存在 (例如，项目未找到)
* `A00301`: 操作冲突 (例如，项目已存在)
* `B00001`: 系统执行超时
* `C00001`: 第三方服务错误 (例如，调用OpenRouter API失败)
* `Z00001`: 未知系统异常

---
### 1. 项目管理 (Projects)

#### 1.1 注册新项目
* **功能点**: FR-PM-001
* **端点**: `POST /projects`
* **描述**: 注册一个新的代码仓库项目。
* **请求体 (`ProjectCreationRequestDTO`)**:
    ```json
    {
      "projectName": "string (必填)",
      "repoUrl": "string (必填, Git仓库地址)",
      "repoType": "string (必填, 'GITLAB', 'GITHUB', 'BITBUCKET', 'OTHER')",
      "defaultBranch": "string (必填)",
      "description": "string (可选)"
    }
    ```
* **成功响应 (HTTP `201 Created`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "项目注册成功",
      "data": { // ProjectDTO
        "projectId": "string",
        "projectName": "string",
        "repoUrl": "string",
        "repoType": "string",
        "defaultBranch": "string",
        "description": "string",
        "createdAt": "string (ISO 8601 UTC)",
        "lastAnalyzedAt": "string (ISO 8601 UTC, 可选)"
      }
    }
    ```
* **失败响应 (例如 HTTP `400 Bad Request` 或 `409 Conflict`)**:
    ```json
    { // 参数错误示例
      "success": false,
      "code": "A00101",
      "message": "项目名称不能为空",
      "data": null
    }
    ```
    ```json
    { // 仓库已存在示例
      "success": false,
      "code": "A00301",
      "message": "具有相同仓库地址的项目已存在",
      "data": null
    }
    ```

#### 1.2 获取项目列表
* **功能点**: FR-PM-002
* **端点**: `GET /projects`
* **描述**: 获取已注册的项目列表，支持分页和排序。
* **查询参数**: `pageNumber` (int, 从0开始), `pageSize` (int), `sortBy` (string), `sortOrder` (string: "asc" | "desc")。
* **成功响应 (HTTP `200 OK`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "获取项目列表成功",
      "data": { // PagedResult<ProjectDTO>
        "content": [
          // ProjectDTO 列表
        ],
        "pageNumber": 0,
        "pageSize": 20,
        "totalElements": 1,
        "totalPages": 1
      }
    }
    ```

#### 1.3 获取特定项目详情
* **功能点**: 支持 `project_detail.html`
* **端点**: `GET /projects/{projectId}`
* **路径参数**: `projectId` (string)
* **成功响应 (HTTP `200 OK`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "获取项目详情成功",
      "data": { // ProjectDetailDTO
        "projectId": "string",
        "projectName": "string",
        // ... 其他 ProjectDTO 字段 ...
        "recentTasks": [ // TaskSummaryDTO 列表
          {
            "taskId": "string",
            "status": "string",
            "createdAt": "string",
            "isSuccess": true
          }
        ],
        "latestDocVersionId": "string (可选)"
      }
    }
    ```
* **失败响应 (例如 HTTP `404 Not Found`)**:
    ```json
    {
      "success": false,
      "code": "A00201",
      "message": "项目未找到",
      "data": null
    }
    ```

#### 1.4 手动触发项目分析
* **功能点**: FR-PM-007
* **端点**: `POST /projects/{projectId}/analyze`
* **路径参数**: `projectId` (string)
* **请求体 (`AnalysisTriggerDTO`)**:
    ```json
    {
      "branch": "string (可选, 默认为项目默认分支)",
      "commitId": "string (可选, 特定commit SHA)"
    }
    ```
* **成功响应 (HTTP `202 Accepted`)**:
    ```json
    {
      "success": true,
      "code": "000000", // 或特定任务已接受码
      "message": "项目分析任务已成功触发",
      "data": { // TaskDTO
        "taskId": "string",
        "projectId": "string",
        "status": "PENDING", // 初始状态
        "currentStage": null,
        "createdAt": "string",
        // ... 其他 TaskDTO 字段 ...
        "triggeredBy": "MANUAL",
        "targetBranch": "string",
        "targetCommitId": "string (可选)"
      }
    }
    ```

---
### 2. 任务管理 (Tasks)

#### 2.1 获取特定任务详情
* **功能点**: FR-TASK-004
* **端点**: `GET /tasks/{taskId}`
* **路径参数**: `taskId` (string)
* **成功响应 (HTTP `200 OK`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "获取任务详情成功",
      "data": { // TaskDetailDTO
        "taskId": "string",
        "projectId": "string",
        "projectName": "string",
        "status": "string", // 如 'PROCESSING', 'COMPLETED', 'FAILED'
        "currentStage": "string", // 如 'LLM_ANALYZING'
        // ... 其他 TaskDTO 字段 ...
        "errorMessage": "string (可选)",
        "errorDetails": "string (可选)",
        "logSummary": [
          { "timestamp": "string", "level": "INFO", "message": "任务阶段日志..." }
        ],
        "generatedDocVersionId": "string (可选)"
      }
    }
    ```

#### 2.2 获取项目相关的任务列表
* **功能点**: 支持 `project_detail.html` 和 `task_queue.html`
* **端点**: `GET /projects/{projectId}/tasks`
* **路径参数**: `projectId` (string)
* **查询参数**: `pageNumber`, `pageSize`, `status` (string), `sortBy`, `sortOrder`
* **成功响应 (HTTP `200 OK`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "获取项目任务列表成功",
      "data": { // PagedResult<TaskSummaryDTO>
        "content": [
          // TaskSummaryDTO 列表
        ],
        // ... 分页信息 ...
      }
    }
    ```

---
### 3. 文档查阅 (Documentation Access)

#### 3.1 获取项目的文档版本列表
* **功能点**: FR-DOCSITE-003
* **端点**: `GET /projects/{projectId}/doc-versions`
* **路径参数**: `projectId` (string)
* **成功响应 (HTTP `200 OK`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "获取文档版本列表成功",
      "data": { // DocVersionListDTO (或直接为 DocVersionDTO[]，取决于是否需要额外元数据)
        "versions": [ // DocVersionDTO 列表
          {
            "versionId": "string (Commit SHA)",
            "displayName": "string",
            "createdAt": "string",
            "isLatest": true,
            "basePath": "string (此版本文档的静态访问基路径)"
          }
        ]
      }
    }
    ```

#### 3.2 获取特定文档版本的导航树
* **功能点**: FR-DOCSITE-002
* **端点**: `GET /projects/{projectId}/doc-versions/{versionId}/navigation-tree`
* **路径参数**: `projectId` (string), `versionId` (string)
* **成功响应 (HTTP `200 OK`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "获取导航树成功",
      "data": [ // DocTreeNodeDTO[] (直接是数组作为data)
        {
          "id": "string",
          "title": "string",
          "path": "string (相对HTML路径)",
          "children": [ /* ... */ ]
        }
      ]
    }
    ```

#### 3.3 获取文档内容对比信息
* **功能点**: FR-DOCSITE-004 (MVP核心特性)
* **端点**: `GET /projects/{projectId}/doc-versions/comparison`
* **路径参数**: `projectId` (string)
* **查询参数**: `filePath` (string), `versionId1` (string), `versionId2` (string)
* **成功响应 (HTTP `200 OK`)**:
    ```json
    {
      "success": true,
      "code": "000000",
      "message": "获取文档对比信息成功",
      "data": { // DocComparisonDTO
        "filePath": "string",
        "version1Id": "string",
        "version2Id": "string",
        "diffOutputType": "UNIFIED_DIFF", // 或其他结构化类型
        "diffContent": "string (例如 unified diff 文本)"
      }
    }
    ```
