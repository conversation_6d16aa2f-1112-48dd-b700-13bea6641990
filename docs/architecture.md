# ArchScope 架构设计文档

## 1. 引言与目标读者

### 1.1 文档目的
本文档旨在详细阐述ArchScope系统的软件架构设计。它将作为开发团队构建、部署和维护系统的核心技术蓝图。本文档定义了系统的整体结构、核心组件、模块职责、技术选型、数据模型、关键业务流程的实现方式，以及如何满足已定义的非功能性需求。

### 1.2 目标读者
* **开发工程师 (后端、前端):** 理解系统结构、模块职责、接口定义、技术栈，指导日常开发工作。
* **架构师:** 审阅架构设计的合理性、可扩展性、可维护性，确保与整体技术战略一致。
* **测试工程师:** 理解系统功能和组件交互，设计测试策略和测试用例。
* **运维工程师:** 了解系统部署架构、依赖组件、监控和运维需求。
* **产品经理/项目经理:** 了解技术实现方案如何满足业务需求，评估技术可行性和复杂度。

### 1.3 项目背景与范围回顾
ArchScope是一个架构观测和守护系统，旨在通过分析代码仓库（MVP阶段为Java项目），自动生成项目文档网站，并提供（未来）项目健康度评估等功能。关键需求包括：
* 任何人可匿名提交项目进行分析。
* 系统判断项目新旧：新项目触发异步解析；旧项目且状态为`AVAILABLE`、可见性为`PUBLIC`时，引导至文档页；否则提示项目已存在但当前不可见。
* 管理员通过SSO登录后可管理项目（如设置可见性为`PUBLIC`、状态为`AVAILABLE`）。
* 查看状态为`AVAILABLE`且可见性为`PUBLIC`的项目文档无需登录。
* MVP阶段重点支持Java语言，后续扩展。
* 部署在客户的私有云Kubernetes环境中，数据库和消息队列采用自建方案。
* LLM服务集成首选OpenRouter。
* SSO集成后，权限处理将由客户提供的单独拦截器负责，ArchScope需能获取并使用这些权限上下文（如用户角色）。

详细需求请参见 `prd.md` (版本1.1或更高)。

## 2. 架构概述

### 2.1 架构选型：模块化单体 (DDD六边形)
ArchScope将采用**模块化单体 (Modular Monolith)** 架构。此选择基于当前系统规模较小、团队协作紧密以及快速迭代交付MVP的需求。同时，为了保证系统的长期可维护性和未来向微服务演进的可能性，我们将严格遵循**领域驱动设计 (DDD)** 的原则，并采用**六边形架构（端口与适配器）**的思想来组织代码。

后端将划分为以下核心Maven模块：
* `arch-scope-main`: 应用启动入口和全局配置。
* `arch-scope-facade`: 接口层 (Facade)，约定与外部系统交互的接口、DTO。
* `arch-scope-app`: 应用服务层，编排领域对象完成业务用例，处理事务，适配外部HTTP请求，DTO转换。
* `arch-scope-domain`: 领域模型层，包含实体、值对象、领域服务接口、仓储接口，封装核心业务逻辑和规则。
* `arch-scope-infrastructure`: 基础设施层，实现仓储接口（对接MySQL, Neo4j, Redis），集成外部服务（Git, OpenRouter, RocketMQ），以及SSO认证信息的解析与使用。

### 2.2 关键架构原则
* **领域驱动设计 (DDD):** 以业务领域为核心进行建模和设计。
* **六边形架构 (Ports and Adapters):** 应用核心（`domain`和`app`）与外部依赖（`facade`和`infrastructure`）通过端口（接口）解耦。
* **依赖倒置原则:** 高层模块不依赖底层模块，两者都依赖于抽象；抽象不依赖细节，细节依赖抽象。`app`和`infrastructure`都依赖`domain`层定义的接口。
* **高内聚、低耦合:** 模块内部功能高度相关，模块之间依赖尽可能少且通过稳定接口交互。
* **清晰的职责边界:** 每个模块和组件都有明确定义的职责。
* **可测试性:** 核心业务逻辑（`domain`, `app`）应易于进行单元测试和集成测试，与具体基础设施实现解耦。
* **可演进性:** 清晰的模块化为未来可能的性能优化、功能扩展或向微服务迁移提供基础。

## 3. C4模型

### 3.1 Level 1: 系统上下文图 (System Context Diagram)

```mermaid
C4Context
  title System Context Diagram for ArchScope

  Person(AnonymousUser, "匿名用户", "通过Web浏览器与系统交互")
  Person(AdminUser, "管理员", "通过SSO认证后，使用Web浏览器管理系统")

  System_Ext(GitRepo, "Git代码仓库", "GitHub, GitLab等，通过API交互")
  System_Ext(LLMService, "LLM服务 (OpenRouter)", "OpenRouter API，用于代码分析辅助 (MVP后)")
  System_Ext(SSOIdP, "企业SSO IdP", "OIDC/SAML，用于管理员认证")

  System(ArchScope, "ArchScope", "架构观测与守护系统")

  Rel(AnonymousUser, ArchScope, "提交项目URL, 查看公开文档", "HTTPS")
  Rel(AdminUser, ArchScope, "访问管理界面/API", "HTTPS")

  Rel(ArchScope, GitRepo, "克隆/拉取代码", "HTTPS, PAT/OAuth")
  Rel(ArchScope, LLMService, "代码片段分析 (MVP后)", "HTTPS, API Key")
  Rel_Back_Neighbor(ArchScope, SSOIdP, "认证用户, 获取角色信息", "HTTPS, OIDC/SAML")

  UpdateElementStyle(AnonymousUser, $bgColor="lightblue", $fontColor="black", $borderColor="blue")
  UpdateElementStyle(AdminUser, $bgColor="lightcoral", $fontColor="black", $borderColor="red")
  UpdateElementStyle(ArchScope, $bgColor="lightgreen", $fontColor="black", $borderColor="green")
```

**交互说明:**
1.  **匿名用户** 通过浏览器向ArchScope提交项目URL，或查看已标记为公开且状态为可用的项目文档网站。
2.  **管理员** 通过企业SSO IdP认证后，访问ArchScope的管理界面或受保护的API，进行项目管理（如设置可见性/状态、触发解析等）。
3.  **ArchScope系统** 与外部**Git代码仓库**交互，使用PAT或OAuth方式克隆/拉取代码。
4.  **ArchScope系统** (MVP后) 可能与外部**LLM服务 (OpenRouter)** 交互，发送代码片段进行分析，并接收分析结果。
5.  **ArchScope系统** 与**企业SSO IdP**交互，完成管理员用户的认证流程，并获取用户的身份和角色信息。客户提供的拦截器将处理权限，ArchScope使用此上下文。

### 3.2 Level 2: 容器图 (Container Diagram)

部署环境为客户的**私有云Kubernetes (K8s) 集群**。核心数据存储和消息队列采用**自建**方案，并与K8s统一的集群管理、备份、监控和日志收集平台集成。

```mermaid
C4Container
  title Container Diagram for ArchScope on Private Cloud Kubernetes

  Person(User, "用户", "通过Web浏览器访问系统 (匿名或管理员)")

  System_Boundary(K8sCluster, "ArchScope Namespace on Kubernetes") {
    Container(FrontendSPA, "Frontend SPA", "Vue.js 3, TypeScript", "用户界面，运行于用户浏览器，静态资源由K8s Ingress/Nginx提供服务")
    Container(BackendApp, "ArchScope Backend", "Java Spring Boot Modular Monolith", "核心业务逻辑, REST API, 异步任务处理, 文档生成 (archscope-main)")
    
    ContainerDb(MySQL, "MySQL Database", "Self-managed on K8s (StatefulSet)", "存储项目元数据, 任务状态, 配置信息")
    ContainerDb(Neo4j, "Neo4j Database", "Self-managed on K8s (StatefulSet)", "存储代码知识图谱")
    ContainerDb(Redis, "Redis Cache", "Self-managed on K8s (StatefulSet/Deployment)", "缓存常用数据")
    
    ContainerQueue(RocketMQ, "Apache RocketMQ", "Self-managed on K8s (StatefulSet/Deployment)", "异步任务队列")

    Container(StaticSiteNginx, "Static Doc Site Server", "Nginx on K8s", "托管生成的静态HTML文档网站")
  }

  System_Ext(GitRepoAPI, "Git Provider APIs", "GitHub, GitLab, etc.")
  System_Ext(OpenRouterAPI, "OpenRouter LLM API", "LLM Gateway (MVP后)")
  System_Ext(SSOProvider, "Enterprise SSO IdP", "OIDC/SAML Provider")
  
  System_Ext(K8sMonitoring, "Unified K8s Monitoring", "Prometheus, Grafana")
  System_Ext(K8sLogging, "Unified K8s Logging", "EFK, Loki")
  System_Ext(K8sBackup, "Unified K8s Backup Service")

  Rel(User, FrontendSPA, "访问系统界面", "HTTPS")
  Rel(FrontendSPA, BackendApp, "调用REST API", "HTTPS, JSON")

  Rel(BackendApp, MySQL, "读写数据", "JDBC")
  Rel(BackendApp, Neo4j, "读写图数据", "Bolt/HTTP")
  Rel(BackendApp, Redis, "读写缓存", "Redis Protocol")
  Rel(BackendApp, RocketMQ, "发送/接收任务消息", "RocketMQ Protocol")

  Rel(BackendApp, GitRepoAPI, "访问仓库代码/元数据", "HTTPS, API Key/OAuth")
  Rel(BackendApp, OpenRouterAPI, "请求LLM分析 (MVP后)", "HTTPS, API Key")
  Rel_Back_Neighbor(BackendApp, SSOProvider, "处理SSO认证流程", "OIDC/SAML")

  Rel(BackendApp, StaticSiteNginx, "部署/更新静态站点内容", "Shared Volume / API")
  Rel(User, StaticSiteNginx, "查看文档网站", "HTTPS")
  
  BiRel(BackendApp, K8sMonitoring, "暴露Metrics / 查询集群监控", "HTTP")
  Rel(BackendApp, K8sLogging, "输出日志", "stdout/stderr -> Log Agent")
  BiRel(MySQL, K8sBackup, "数据备份/恢复")
  BiRel(Neo4j, K8sBackup, "数据备份/恢复")
  BiRel(Redis, K8sBackup, "数据备份/恢复 (if persistent)")
  BiRel(RocketMQ, K8sBackup, "消息持久化数据备份/恢复")
```

**容器说明:**
* **Frontend SPA:** 用户界面。
* **ArchScope Backend Application:** 模块化单体应用，部署为K8s Deployment。
* **MySQL, Neo4j, Redis, RocketMQ:** 自建组件，部署在K8s内部 (StatefulSets优先，确保数据持久性和稳定性)，通过PV/PVC对接私有云的持久化存储。
* **Static Doc Site Server:** 使用Nginx Pod（或类似方案）托管生成的静态文档网站，与BackendApp解耦，方便独立扩展和管理。BackendApp负责将生成的站点文件推送到Nginx可访问的共享存储卷。
* **外部依赖:** Git API, OpenRouter API, SSO IdP。
* **K8s平台服务:** 统一监控、日志、备份。

### 3.3 Level 3: 组件图 (Component Diagram - ArchScope Backend Application Internals)

```mermaid
C4Component
  title Component Diagram for ArchScope Backend Application

  Container_Boundary(BackendAppBoundary, "ArchScope Backend Application (Modular Monolith)") {
    Component(Main, "archscope-main", "Java, Spring Boot", "应用启动, 全局配置 (Security, Web, DB, MQ), Bean组装")
    Component(InterfacesAPI, "archscope-facade", "Java, Spring MVC/WebFlux", "REST API端点, DTO定义与转换, 输入验证. 受SSO保护的端点依赖外部拦截器提供的权限上下文.")
    Component(AppServices, "archscope-app", "Java", "应用服务, 业务用例编排, 事务管理. 基于权限上下文执行授权逻辑.")
    Component(DomainCore, "archscope-domain", "Java", "核心领域模型 (实体, 值对象, 领域服务接口, 仓储接口), 业务规则.")
    
    Container_Boundary(InfrastructureBoundary, "archscope-infrastructure (Adapters & Implementations)") {
      Component(PersistenceAdapter, "Persistence Adapters", "Java, Spring Data JPA/Neo4j/Redis, MyBatisPlus", "实现仓储接口 (MySQL, Neo4j, Redis)")
      Component(MessagingAdapter, "Messaging Adapters", "Java, RocketMQ Client", "消息生产者/消费者 (RocketMQ)")
      Component(GitAdapter, "Git Client Adapter", "Java, JGit/CLI wrapper", "与Git仓库API和操作交互 (PAT/OAuth)")
      Component(LLMAdapter, "LLM Client Adapter", "Java, HTTP Client", "与OpenRouter API交互 (MVP后)")
      Component(SSOAdapter, "SSO Integration Support", "Java, Spring Security SAML/OAuth2 Client", "处理SSO认证回调, 解析用户信息和角色声明 (供外部拦截器使用或自身进行校验)")
      Component(DocGenAdapter, "Documentation Generation Adapters", "Java, Thymeleaf, Markdown Libs", "Markdown内容生成, 静态站点文件打包")
    }
  }

  System_Ext(MySQL_DB, "MySQL Database")
  System_Ext(Neo4j_DB, "Neo4j Database")
  System_Ext(Redis_Cache, "Redis Cache")
  System_Ext(RocketMQ_MQ, "RocketMQ")
  System_Ext(Git_API, "Git Provider APIs")
  System_Ext(OpenRouter_LLM, "OpenRouter LLM API (MVP后)")
  System_Ext(SSO_IdP, "Enterprise SSO IdP")
  System_Ext(AuthN_Interceptor, "Customer's AuthN/AuthZ Interceptor", "Handles SSO and injects roles")


  Rel(Main, InterfacesAPI, "组装并暴露")
  Rel(Main, AppServices, "组装")
  Rel(Main, DomainCore, "组装 (使其可被注入)")
  Rel(Main, PersistenceAdapter, "组装")
  Rel(Main, MessagingAdapter, "组装")
  Rel(Main, GitAdapter, "组装")
  Rel(Main, LLMAdapter, "组装")
  Rel(Main, SSOAdapter, "组装 (SSO客户端配置)")
  Rel(Main, DocGenAdapter, "组装")

  Rel(InterfacesAPI, AppServices, "调用应用服务")
  Rel_Neighbor(InterfacesAPI, AuthN_Interceptor, "SSO认证和权限上下文由外部拦截器提供")

  Rel(AppServices, DomainCore, "使用领域模型和规则, 调用领域服务接口")
  Rel(AppServices, PersistenceAdapter, "通过仓储接口进行数据持久化")
  Rel(AppServices, MessagingAdapter, "发送任务消息")
  Rel(AppServices, GitAdapter, "请求Git操作")
  Rel(AppServices, LLMAdapter, "请求LLM分析 (MVP后)")
  Rel(AppServices, DocGenAdapter, "请求文档生成")

  Rel(PersistenceAdapter, DomainCore, "实现领域定义的仓储接口")
  Rel(PersistenceAdapter, MySQL_DB, "读写")
  Rel(PersistenceAdapter, Neo4j_DB, "读写")
  Rel(PersistenceAdapter, Redis_Cache, "读写")

  Rel(MessagingAdapter, RocketMQ_MQ, "发送/接收消息")
  Rel_Back(MessagingAdapter, AppServices, "消息驱动调用应用服务 (消费者)")

  Rel(GitAdapter, Git_API, "API调用, Git操作")
  Rel(LLMAdapter, OpenRouter_LLM, "API调用 (MVP后)")
  Rel(SSOAdapter, SSO_IdP, "协议交互 (如OIDC Discovery, Token Validation Support)")
  
  UpdateElementStyle(DomainCore, $bgColor="lightcoral")
  UpdateElementStyle(AppServices, $bgColor="lightyellow")
  UpdateElementStyle(InterfacesAPI, $bgColor="lightblue")
  UpdateElementStyle(InfrastructureBoundary, $bgColor="lightgrey")
```
**组件职责与依赖关系:** (已在2.1节详述，此图更细化了基础设施层内部的适配器，并明确了外部SSO拦截器的角色)

### 3.4 (可选) Level 4: 代码图 (Code Diagram)
(将在具体模块详细设计时按需提供，例如展示`Project`聚合根的状态转换方法，或`ProjectApplicationService.submitNewProject`用例的关键类协作。)

## 4. 模块职责与设计 (详细)

### 4.1 `archscope-domain`
* **核心实体:** `Project`, `Task`, `DocumentSet`, `CodeFileAnalysisResult` (MVP阶段简化，主要关注Project和Task)。
    * `Project`:
        * 属性: `projectId` (UUID), `name` (String), `repositoryInfo` (VO: url, type, credentialsId), `language` (Enum: JAVA), `defaultBranch` (String), **`status` (Enum: PENDING_ANALYSIS, ANALYSIS_IN_PROGRESS, ANALYSIS_FAILED, DOC_GEN_IN_PROGRESS, DOC_GEN_FAILED, UNAVAILABLE, AVAILABLE)**, **`visibility` (Enum: PENDING_REVIEW, INTERNAL, PUBLIC)**, `createdAt` (DateTime), `updatedAt` (DateTime), `normalizedRepoUrl` (String, Unique), `latestAnalyzedCommitId` (String, Nullable), `latestPublicCommitId` (String, Nullable), `publicDocSitePath` (String, Nullable), `adminNotes` (String, Nullable).
        * 行为 (部分):
            * `static Project submitAnonymous(RepositoryInfo repoInfo, String name, String defaultBranch)`: 创建一个匿名提交的项目，初始状态 PENDING_REVIEW, PENDING_ANALYSIS。
            * `updateStatus(Status newStatus, AdminUser admin)`: 管理员更新项目状态。
            * `updateVisibility(Visibility newVisibility, AdminUser admin)`: 管理员更新项目可见性。
            * `recordSuccessfulAnalysis(String commitId)`: 记录一次成功的分析。
            * `publishDocumentation(String commitId, String sitePath)`: (在设为Public和Available后) 记录公开文档信息。
            * `isPubliclyAccessible()`: boolean 判断 (visibility == PUBLIC && status == AVAILABLE)。
* **值对象:** `RepositoryInfo`, `AdminUser` (从SSO上下文获取的管理员身份标识)。
* **仓储接口:**
    * `ProjectRepository`: `save(Project)`, `findById(ProjectId)`, `findByNormalizedRepoUrl(String normalizedUrl)`, `findAllForAdmin(Pageable)`, `findAllPublicAndAvailable(Pageable)`.
    * `TaskRepository`: `save(Task)`, `findById(TaskId)`, `findAllByProjectId(ProjectId, Pageable)`, `findAllAdmin(TaskQueryCriteria, Pageable)`.
* **领域服务接口 (可选):**
    * `ProjectUniquenessValidator`: `isRepoUrlUnique(String normalizedRepoUrl)` (由`ProjectApplicationService`使用)。
    * `AnalysisTriggerPolicy`: `shouldTriggerNewAnalysis(Project project, String latestCommitInRepo)` (MVP可能简化为总是触发)。

### 4.2 `archscope-app`
* **应用服务:**
    * `ProjectApplicationService`:
        * `submitNewProject(SubmitProjectCommand)`:
            1. 标准化`repoUrl`。
            2. 调用`ProjectUniquenessValidator.isRepoUrlUnique()` (通过`ProjectRepository.findByNormalizedRepoUrl()`)。
            3. 若已存在：检查其`status`和`visibility`。若`PUBLIC`且`AVAILABLE`，返回`ExistingProjectInfoDTO` (含文档链接)。否则返回“项目已存在但不可访问”的提示DTO。
            4. 若全新：调用`Project.submitAnonymous()`创建实体，`ProjectRepository.save()`，然后创建`CODE_FULL_ANALYSIS_JAVA`任务并发送到MQ。返回`NewProjectAcceptedDTO`。
        * `getPublicProjectDetails(String projectId)`: 获取项目信息，仅当`project.isPubliclyAccessible()`为true。
        * `listPublicProjects(Pageable)`: 获取所有公开且可用的项目列表。
    * `AdminProjectApplicationService` (或在`ProjectApplicationService`中通过注入的`AdminUser`上下文进行权限判断):
        * `listAllProjects(Pageable, AdminUser admin)`
        * `getProjectDetailsForAdmin(String projectId, AdminUser admin)`
        * `updateProjectVisibility(UpdateVisibilityCommand, AdminUser admin)`: 更新`Project.visibility`，保存。若设为`PUBLIC`且项目`status`为`AVAILABLE`，确保`publicDocSitePath`等信息已准备好。
        * `updateProjectStatus(UpdateStatusCommand, AdminUser admin)`: 更新`Project.status`，保存。
        * `triggerAnalysisForProject(TriggerAnalysisCommand, AdminUser admin)`: 创建`CODE_FULL_ANALYSIS_JAVA`任务并发送。
        * `deleteProject(String projectId, AdminUser admin)`: (Post-MVP)
    * `DocumentationApplicationService`:
        * `getPublicDocumentTree(String projectId)`: 获取`projectId`最新公开可用版本的文档树。先查`Project`确保可公开访问，再从`DocumentSet` (或其简化表示) 读取树结构。
        * `getPublicDocumentContent(String projectId, String filePath)`: 获取文档内容，同上先校验可访问性。
    * `TaskApplicationService`:
        * `getTaskDetails(String taskId, AuthenticatedUser userContext)`: 获取任务详情。根据`userContext`的角色判断是否有权查看（如管理员可看所有，普通用户未来或可看自己触发的）。MVP阶段可能简化为仅管理员可查。
        * `listTasksForAdmin(TaskQueryCriteria, Pageable, AdminUser admin)`
    * `SystemInfoApplicationService`:
        * `listErrorCodes()`: 返回错误码指南信息。
* **REST Controllers:** (与上一版基本一致，但Admin相关接口明确需要SSO认证并传入`AdminUser`上下文)
    * `PublicProjectController`:
        * `POST /projects` (SubmitProjectCommand -> NewProjectAcceptedDTO | ExistingProjectInfoDTO)
        * `GET /projects/{projectId}/documentation` (代理到静态站点或重定向)
        * `GET /projects/{projectId}/documentation/tree` (-> DocTreeNodeListDTO)
        * `GET /projects/{projectId}/documentation/content` (-> MarkdownDocumentDTO)
    * `AdminProjectController` (路径前缀 `/admin/projects`, 所有方法需SSO Admin角色):
        * `GET /` (listAllProjects)
        * `GET /{projectId}` (getProjectDetailsForAdmin)
        * `PUT /{projectId}/visibility` (updateProjectVisibility)
        * `PUT /{projectId}/status` (updateProjectStatus)
        * `POST /{projectId}/trigger-analysis` (triggerAnalysisForProject)
    * `AdminTaskController` (路径前缀 `/admin/tasks`, 所有方法需SSO Admin角色):
        * `GET /` (listTasksForAdmin)
        * `GET /{taskId}` (getTaskDetails)
    * `SystemController`:
        * `GET /system/error-codes` (公开 -> ErrorCodeListDTO)

### 4.3 `archscope-facade`
* **DTOs:** 与`api-spec.yaml`对应。
* **安全:** 受SSO保护的端点将依赖客户提供的外部拦截器注入认证和角色信息。ArchScope的Controller或AppService将使用此信息。公开API则不依赖此机制。

### 4.4 `archscope-infrastructure`
* **Persistence Adapters:**
    * `MySqlProjectRepositoryImpl`: 实现`ProjectRepository`，使用Spring Data JPA/MyBatisPlus与MySQL交互。
    * `MySqlTaskRepositoryImpl`: 实现`TaskRepository`。
    * `Neo4jCodeGraphAdapterImpl`: 实现代码图谱的存储和查询接口（这些接口在`archscope-domain`或`archscope-app`中定义）。使用Spring Data Neo4j或Neo4j Java Driver。
    * `RedisCacheAdapterImpl`: 实现通用缓存服务接口。
* **Messaging Adapters:**
    * `RocketMQTaskProducerImpl`: 实现发送任务消息到RocketMQ的接口。
    * `RocketMQAnalysisTaskConsumer`, `RocketMQDocGenTaskConsumer`: 作为消息监听器，从RocketMQ消费消息，反序列化payload，然后调用`archscope-app`层的相应应用服务来处理任务（例如，`codeAnalysisApplicationService.performFullJavaAnalysis(...)`）。这些Consumer本身是Spring管理的Bean。
* **External Service Clients/Adapters:**
    * `GitServiceAdapterImpl`: 实现Git操作，支持PAT和OAuth App。
    * `OpenRouterLlmClientAdapterImpl`: (MVP后) 与OpenRouter API交互。
    * `SsoUserInfoExtractorImpl` (或类似名称): (如果需要) 辅助从Spring Security上下文或SSO断言中提取标准化的`AuthenticatedUser`或`AdminUser`信息供应用层使用。
* **Documentation Generation Adapters:**
    * `ThymeleafMarkdownGeneratorImpl`: 实现Markdown生成。
    * `StaticSitePublisherImpl`: 将生成的Markdown/HTML文件发布到Nginx可访问的K8s PV/共享卷。

### 4.5 `archscope-main`
* Spring Boot `@SpringBootApplication`类。
* `SecurityConfig`:
    * 配置Spring Security与SSO IdP的集成（OIDC Client或SAML SP）。
    * 定义哪些URL路径模式是公开的 (`/projects` POST, `/projects/{id}/documentation/**` GET, `/system/error-codes` GET)。
    * 定义哪些URL路径模式需要认证 (`/admin/**`)。
    * 配置与客户提供的权限拦截器的协作（如果需要特定的Filter顺序或上下文共享）。
* 其他配置类: `WebMvcConfig`, `DataSourceConfig` (MySQL, Neo4j), `RedisConfig`, `RocketMQConfig`, `AsyncConfig` (for @Async task executors if not fully relying on MQ consumers for all async work)。
* Bean扫描和依赖注入配置。

## 5. 核心业务流程实现 (详细序列图)
*(与上一版Mermaid图一致，确保语法正确可渲染。流程逻辑已根据最新反馈调整，如匿名提交、管理员审核公开等)*

### 5.1 流程1: 匿名用户提交新Java项目并触发首次解析
```mermaid
sequenceDiagram
    participant FES as Frontend SPA (Anonymous)
    participant API as archscope-facade (PublicProjectController)
    participant APP as archscope-app (ProjectApplicationService)
    participant DOM_PRJ as archscope-domain (Project Entity/Factory)
    participant REPO_PRJ as archscope-infrastructure (ProjectRepository)
    participant INFRA_MSG as archscope-infrastructure (TaskMessageProducer)
    participant MQ as RocketMQ
    participant TASK_CONSUMER as archscope-infrastructure (CodeAnalysisTaskConsumer)
    participant APP_ANLS as archscope-app (CodeAnalysisApplicationService) %% Or a dedicated AnalysisAppService
    participant INFRA_GIT as archscope-infrastructure (GitServiceAdapter)
    participant INFRA_AST as archscope-infrastructure (JavaAstParserAdapter)
    participant INFRA_GRAPH as archscope-infrastructure (Neo4jCodeGraphAdapter)
    participant REPO_TSK as archscope-infrastructure (TaskRepository)

    FES->>+API: POST /projects (repoUrl, repoType='GIT', language='JAVA', defaultBranch='main')
    API->>+APP: submitNewProject(command)
    APP->>+REPO_PRJ: findByNormalizedRepoUrl(normalizedRepoUrl)
    REPO_PRJ-->>-APP: null (project is new)
    APP->>+DOM_PRJ: Project.submitAnonymous(repoInfo, name, defaultBranch)
    DOM_PRJ-->>-APP: newProjectEntity (visibility=PENDING_REVIEW, status=PENDING_ANALYSIS)
    APP->>+REPO_PRJ: save(newProjectEntity)
    REPO_PRJ-->>-APP: savedProjectEntity (with projectId)
    APP->>APP: createTaskPayload(taskId=uuid(), type=CODE_FULL_ANALYSIS_JAVA, projectId, defaultBranch)
    APP->>+REPO_TSK: save(newTaskEntity with status QUEUED)
    REPO_TSK-->>-APP: savedTaskEntity
    APP->>+INFRA_MSG: sendAnalysisTask(savedTaskEntity.payload with taskId)
    INFRA_MSG->>+MQ: Publish(CodeAnalysisTopic, message with taskId)
    MQ-->>-INFRA_MSG: Ack
    APP-->>-API: NewProjectAcceptedDTO (projectId, taskInfo(taskId, QUEUED))
    API-->>-FES: HTTP 202 Accepted (NewProjectAcceptedDTO)

    %% Async Analysis Part
    MQ->>+TASK_CONSUMER: Deliver(message with taskId and payload)
    TASK_CONSUMER->>+APP_ANLS: processCodeAnalysisTask(taskId, payload)
    APP_ANLS->>+REPO_TSK: updateTaskStatus(taskId, RUNNING)
    REPO_TSK-->>-APP_ANLS: Ack
    APP_ANLS->>+INFRA_GIT: cloneOrFetch(repoInfo, commitId)
    INFRA_GIT-->>-APP_ANLS: localCodePath
    APP_ANLS->>+INFRA_AST: parseJavaProject(localCodePath)
    INFRA_AST-->>-APP_ANLS: astParseResults
    APP_ANLS->>+INFRA_GRAPH: storeCodeGraph(projectId, commitId, astParseResults)
    INFRA_GRAPH-->>-APP_ANLS: GraphStorageReceipt
    APP_ANLS->>+REPO_PRJ: updateProjectAfterAnalysis(projectId, commitId, status=UNAVAILABLE) %% Analysis done, awaiting review/docgen
    REPO_PRJ-->>-APP_ANLS: Ack
    APP_ANLS->>+REPO_TSK: updateTaskStatus(taskId, SUCCESS, resultSummary="AST Parsed")
    REPO_TSK-->>-APP_ANLS: Ack
    
    %% Trigger Doc Gen Task
    APP_ANLS->>APP_ANLS: createTaskPayloadForDocGen(taskId=uuid(), type=DOC_SITE_GENERATION_JAVA, projectId, commitId)
    APP_ANLS->>+REPO_TSK: save(newDocGenTaskEntity with status QUEUED)
    REPO_TSK-->>-APP_ANLS: savedDocGenTaskEntity
    APP_ANLS->>+INFRA_MSG: sendDocGenTask(savedDocGenTaskEntity.payload with taskId)
    INFRA_MSG->>+MQ: Publish(DocumentationTopic, docGenMessage with taskId)
    MQ-->>-INFRA_MSG: Ack
    TASK_CONSUMER-->>MQ: AckMessageConsumed
```

### 5.2 流程2: 匿名用户提交已存在的公开可用项目
```mermaid
sequenceDiagram
    participant FES as Frontend SPA (Anonymous)
    participant API as archscope-facade (PublicProjectController)
    participant APP as archscope-app (ProjectApplicationService)
    participant REPO_PRJ as archscope-infrastructure (ProjectRepository)

    FES->>+API: POST /projects (repoUrl, ...)
    API->>+APP: submitNewProject(command)
    APP->>+REPO_PRJ: findByNormalizedRepoUrl(normalizedUrl)
    REPO_PRJ-->>-APP: existingProjectEntity (status='AVAILABLE', visibility='PUBLIC', publicDocSitePath='/docs/proj123')
    APP-->>-API: ExistingProjectInfoDTO (projectId, publicDocSitePath, message="Project already exists and is public.")
    API-->>-FES: HTTP 200 OK (ExistingProjectInfoDTO)
```

### 5.3 流程3: 管理员通过SSO登录后，审核项目并设为公开可用
```mermaid
sequenceDiagram
    participant ADMIN_FES as Frontend SPA (Admin)
    participant EXT_INTERCEPTOR as Customer's SSO AuthN/AuthZ Interceptor
    participant API as archscope-facade (AdminProjectController)
    participant APP as archscope-app (AdminProjectApplicationService)
    participant DOM_PRJ as archscope-domain (Project Entity)
    participant REPO_PRJ as archscope-infrastructure (ProjectRepository)
    participant INFRA_MSG as archscope-infrastructure (TaskMessageProducer)
    participant MQ as RocketMQ
    participant REPO_TSK as archscope-infrastructure (TaskRepository)


    ADMIN_FES->>API: GET /admin/projects?visibility=PENDING_REVIEW (HTTP request with SSO session/token)
    Note over API: EXT_INTERCEPTOR validates SSO token, injects Admin role into SecurityContext.
    API->>+APP: listAllProjects(criteria, adminUserContext)
    APP->>+REPO_PRJ: findByCriteria(criteria)
    REPO_PRJ-->>-APP: pendingProjectsList
    APP-->>-API: PagedResult_ProjectSummaryDTO
    API-->>-ADMIN_FES: List of pending projects

    ADMIN_FES->>+API: PUT /admin/projects/{projectId}/visibility (body: {visibility: 'PUBLIC'})
    Note over API: EXT_INTERCEPTOR validates SSO token & Admin role.
    API->>+APP: updateProjectVisibility(projectId, 'PUBLIC', adminUserContext)
    APP->>+REPO_PRJ: findById(projectId)
    REPO_PRJ-->>-APP: projectToUpdate (current visibility=PENDING_REVIEW)
    APP->>+DOM_PRJ: projectToUpdate.markAsPublic(adminUserContext)
    DOM_PRJ-->>-APP: (State changed)
    APP->>+REPO_PRJ: save(projectToUpdate)
    REPO_PRJ-->>-APP: updatedProjectEntity (visibility=PUBLIC)
    APP-->>-API: UpdatedProjectDetailDTO
    API-->>-ADMIN_FES: HTTP 200 OK

    ADMIN_FES->>+API: PUT /admin/projects/{projectId}/status (body: {status: 'AVAILABLE'})
    Note over API: EXT_INTERCEPTOR validates.
    API->>+APP: updateProjectStatus(projectId, 'AVAILABLE', adminUserContext)
    APP->>+REPO_PRJ: findById(projectId)
    REPO_PRJ-->>-APP: projectToUpdate (current status, e.g. UNAVAILABLE after analysis)
    APP->>+DOM_PRJ: projectToUpdate.markAsAvailable(adminUserContext)
    DOM_PRJ-->>-APP: (State changed)
    APP->>+REPO_PRJ: save(projectToUpdate)
    REPO_PRJ-->>-APP: updatedProjectEntity (status=AVAILABLE)
    
    opt If Doc Site needs generation/update for public path after approval
        APP->>APP: createTaskPayloadForDocSiteUpdate(taskId=uuid(), type=DOC_SITE_GENERATION_JAVA, projectId, projectToUpdate.latestAnalyzedCommitId)
        APP->>+REPO_TSK: save(newDocSiteTaskEntity)
        REPO_TSK-->>-APP: savedDocSiteTaskEntity
        APP->>+INFRA_MSG: sendDocSiteUpdateTask(savedDocSiteTaskEntity.payload with taskId)
        INFRA_MSG->>+MQ: Publish(DocumentationTopic, docSiteUpdateMessage with taskId)
        MQ-->>-INFRA_MSG: Ack
    end
    
    APP-->>-API: UpdatedProjectDetailDTO
    API-->>-ADMIN_FES: HTTP 200 OK
```

### 5.4 流程4: 任何用户查看公开项目文档
```mermaid
sequenceDiagram
    participant USER_FES as Frontend SPA (Anonymous or Authenticated)
    participant API as archscope-facade (PublicProjectController)
    participant APP as archscope-app (DocumentationApplicationService)
    participant REPO_PRJ as archscope-infrastructure (ProjectRepository)
    participant STATIC_NGINX as Static Doc Site Server (Nginx)

    USER_FES->>+API: GET /projects/{projectId}/documentation %% Requests root of doc site
    API->>+APP: getPublicProjectDocumentationRoot(projectId)
    APP->>+REPO_PRJ: findById(projectId)
    REPO_PRJ-->>-APP: projectEntity
    alt projectEntity.visibility == 'PUBLIC' AND projectEntity.status == 'AVAILABLE'
        APP-->>-API: RedirectInfoDTO (urlToRedirect = projectEntity.publicDocSitePath + "/index.html")
        API-->>-USER_FES: HTTP 302 Found, Location: <nginx_base_url>/<projectEntity.publicDocSitePath>/index.html
        USER_FES->>STATIC_NGINX: GET /<projectEntity.publicDocSitePath>/index.html
        STATIC_NGINX-->>USER_FES: HTML Page
    else Project Not Public or Not Available
        APP-->>-API: Error (e.g., ProjectNotPubliclyAvailableException)
        API-->>-USER_FES: HTTP 403 Forbidden or 404 Not Found
    end

    %% For specific doc file or tree - API calls DocumentationApplicationService which then might fetch from a manifest or directly from static site storage metadata
    USER_FES->>+API: GET /projects/{projectId}/documentation/tree %% API to get tree structure for SPA navigation
    API->>+APP: getPublicDocumentTree(projectId)
    APP->>+REPO_PRJ: findById(projectId)
    REPO_PRJ-->>-APP: projectEntity
    alt projectEntity.visibility == 'PUBLIC' AND projectEntity.status == 'AVAILABLE'
        APP->>APP: constructTreeFromManifest(projectEntity.publicDocSitePath) %% Logic to read manifest or list files
        APP-->>-API: DocTreeNode_List DTO
        API-->>-USER_FES: HTTP 200 OK (Doc Tree for SPA rendering)
    else
        APP-->>-API: Error
        API-->>-USER_FES: HTTP 403/404
    end
```

### 5.5 流程5: 任务状态轮询 (管理员)
```mermaid
sequenceDiagram
    participant ADMIN_FES as Frontend SPA (Admin)
    participant EXT_INTERCEPTOR as Customer's SSO AuthN/AuthZ Interceptor
    participant API as archscope-facade (AdminTaskController)
    participant APP as archscope-app (TaskApplicationService)
    participant REPO_TSK as archscope-infrastructure (TaskRepository)

    ADMIN_FES->>API: (After an admin action triggered an async task, receives taskId)
    loop Until Task is SUCCESS or FAILED
        ADMIN_FES->>+API: GET /admin/tasks/{taskId} (HTTP request with SSO session/token)
        Note over API: EXT_INTERCEPTOR validates SSO token, injects Admin role.
        API->>+APP: getTaskDetails(taskId, adminUserContext)
        APP->>+REPO_TSK: findById(taskId)
        REPO_TSK-->>-APP: taskEntity
        alt taskEntity found
            APP-->>-API: TaskDetailDTO (with current status, progress, etc.)
            API-->>-ADMIN_FES: HTTP 200 OK (TaskDetailDTO)
        else Task Not Found
            APP-->>-API: Error (TaskNotFoundException)
            API-->>-ADMIN_FES: HTTP 404 Not Found
        end
        ADMIN_FES->>ADMIN_FES: (Wait for polling interval)
    end
```

## 6. 数据模型设计 (详细)

### 6.1 MySQL关系模型 (ERD - Mermaid)
```mermaid
erDiagram
    Project {
        varchar_36 projectId PK "UUID"
        varchar_255 name
        text description
        varchar_512 repoUrlNormalized PK "Normalized Git Repo URL (Unique)"
        varchar_50 repoType "Enum: GITHUB, GITLAB, BITBUCKET, OTHER_GIT"
        varchar_50 language "Enum: JAVA (MVP)"
        varchar_100 defaultBranch
        varchar_50 status "Enum: PENDING_ANALYSIS, ANALYSIS_IN_PROGRESS, ANALYSIS_FAILED, DOC_GEN_IN_PROGRESS, DOC_GEN_FAILED, UNAVAILABLE, AVAILABLE"
        varchar_50 visibility "Enum: PENDING_REVIEW, INTERNAL, PUBLIC"
        datetime createdAt
        datetime updatedAt
        varchar_40 latestAnalyzedCommitId "SHA of last successfully analyzed commit for internal use"
        varchar_40 latestPublicCommitId "SHA of commit for currently public docs"
        varchar_255 publicDocSitePath "Base path/identifier for Nginx/Static Site server"
        text adminNotes "For admin review process (e.g., why a project is PENDING_REVIEW)"
    }

    Task {
        varchar_36 taskId PK "UUID"
        varchar_36 projectId FK "FK to Project.projectId, Nullable"
        varchar_100 taskType "Enum: CODE_FULL_ANALYSIS_JAVA, DOC_SITE_GENERATION_JAVA"
        varchar_50 status "Enum: QUEUED, RUNNING, SUCCESS, FAILED"
        int priority "App-level priority (MVP simplified)"
        text payload "JSON payload for task parameters"
        datetime createdAt
        datetime queuedAt
        datetime startedAt
        datetime finishedAt
        int retryCount
        text lastErrorMessage
        text resultSummary "JSON summary of results or pointers"
        varchar_100 triggeredByType "Enum: WEB_SUBMISSION, ADMIN_SSO_TRIGGER, SYSTEM_SCHEDULED_TRIGGER, CHAINED_TASK_TRIGGER"
        varchar_255 triggeredById "Identifier of trigger source (e.g., Admin SSO User ID, previous taskId)"
    }

    Project ||--o{ Task : "can have many"
```
*(Note: User-specific tables are minimal due to SSO. If ArchScope needs to store user preferences mapped to SSO User ID, a simple `UserPreferences` table could be added later.)*

### 6.2 Neo4j图模型 (Conceptual Schema for Java MVP)
* **Nodes:**
    * `:JavaProject {projectId: string, name: string, repoUrlNormalized: string}` (Root node for a project's graph data)
    * `:GitCommit {commitId: string, timestamp: datetime, message: string}` (Represents a specific version analyzed)
    * `:JavaFile {path: string, name: string}` (Relative path within the commit)
    * `:JavaPackage {fqn: string, name: string}` (Fully qualified package name)
    * `:JavaClass {fqn: string, name: string, visibility: string, isAbstract: boolean, isFinal: boolean, loc: integer, comment: string}`
    * `:JavaInterface {fqn: string, name: string, visibility: string, loc: integer, comment: string}`
    * `:JavaMethod {signature: string, name: string, visibility: string, returnType: string, parameters: list<string>, isStatic: boolean, isAbstract: boolean, cyclomaticComplexity: integer, loc: integer, comment: string}`
    * `:JavaField {name: string, type: string, visibility: string, isStatic: boolean, isFinal: boolean, comment: string}`
* **Relationships:**
    * `(:JavaProject)-[:ANALYZED_COMMIT]->(:GitCommit)`
    * `(:GitCommit)-[:CONTAINS_FILE]->(:JavaFile)`
    * `(:JavaFile)-[:DEFINES_TYPE]->(:JavaClass | :JavaInterface)`
    * `(:JavaPackage)-[:CONTAINS_TYPE]->(:JavaClass | :JavaInterface)` (Can be derived or explicit)
    * `(:JavaClass)-[:DECLARES_METHOD]->(:JavaMethod)`
    * `(:JavaClass)-[:DECLARES_FIELD]->(:JavaField)`
    * `(:JavaInterface)-[:DECLARES_METHOD]->(:JavaMethod)`
    * `(:JavaClass)-[:EXTENDS]->(:JavaClass)`
    * `(:JavaClass)-[:IMPLEMENTS]->(:JavaInterface)`
    * `(:JavaInterface)-[:EXTENDS_INTERFACE]->(:JavaInterface)`
    * `(:JavaMethod)-[:CALLS_METHOD]->(:JavaMethod)` (MVP: This can be complex; might start with class-level `USES_TYPE` or method signature references)
    * `(:JavaMethod)-[:ACCESSES_FIELD]->(:JavaField)`
    * `(:JavaMethod)-[:RETURNS_TYPE]->(:JavaClass | :JavaInterface | {name:'primitive_type'})`
    * `(:JavaMethod)-[:HAS_PARAMETER_TYPE]->(:JavaClass | :JavaInterface | {name:'primitive_type'})`
    * `(:JavaField)-[:HAS_TYPE]->(:JavaClass | :JavaInterface | {name:'primitive_type'})`
    * `(:JavaPackage)-[:HAS_PARENT_PACKAGE]->(:JavaPackage)` (For package hierarchy)

### 6.3 Redis 使用场景 (MVP)
* **Caching (Optional for MVP, but good to plan for):**
    * Frequently accessed public project summaries (`ProjectSummaryDTO`).
    * Generated document tree structures (`DocTreeNodeDTO` list) for popular public projects/versions.
    * Potentially, rendered Markdown content (`MarkdownDocumentDTO.markdownContent`) for very popular pages.
    * Cache TTLs need to be managed carefully, especially for content that changes with new commits.
* **Distributed Lock (Post-MVP):** If needed to prevent concurrent processing સમસ્યાઓ (e.g., multiple workers trying to analyze the exact same commit for the same project simultaneously, although task de-duplication at submission might be better).
* **Rate Limiting Counters (Post-MVP):** If `POST /projects` needs rate limiting in the future.
* **Session Management (If chosen over stateless JWTs after SSO):** Store ArchScope-specific session data linked to SSO session/token. (MVP will likely use JWTs passed from the SSO interceptor or a simple session cookie derived from it).

## 7. 技术栈选型与版本 (推荐)
* **Java:** JDK 17 (LTS)
* **Spring Boot:** 3.2.x (Latest stable supporting Java 17 at time of design)
* **Vue.js:** 3.x (Latest stable)
* **TypeScript:** Latest stable
* **Tailwind CSS:** Latest stable
* **MySQL:** 8.0.x (Self-managed on K8s)
* **Neo4j:** 5.x Community Edition (Self-managed on K8s for MVP). *Consider Neo4j AuraDB (cloud) or Enterprise Edition for production HA/scaling needs if budget allows and private cloud constraints permit external PaaS.*
* **Redis:** 7.x (Self-managed on K8s).
* **Apache RocketMQ:** 5.x (Self-managed on K8s).
* **Maven:** 3.9.x
* **Node.js:** LTS version (e.g., 20.x) for frontend build
* **Docker:** Latest stable for building images.
* **Kubernetes:** Version supported by the private cloud (e.g., 1.26+). Deployed workloads should target a stable K8s API version.
* **OpenRouter:** Via its standard REST API. An API key will be required.
* **SSO Integration:** Standard OIDC Connect (preferred) or SAML 2.0, compatible with the enterprise's Identity Provider.
* **JavaParser:** Latest stable version for Java AST parsing.
* **Thymeleaf:** Latest stable for Markdown template processing.
* **CommonMark-Java (or similar):** For Markdown to HTML rendering if server-side rendering is chosen for initial site generation.

## 9. 跨领域关注点设计

### 9.1 认证与授权 (SSO & External Interceptor)
* **Authentication:** Primarily handled by the enterprise SSO IdP. `archscope-main`'s `SecurityConfig` configures Spring Security to act as an OIDC Relying Party or SAML Service Provider. It redirects unauthenticated users (accessing protected admin paths like `/admin/**`) to the SSO IdP and processes the callback to establish an ArchScope security context.
* **Session Management:** After successful SSO authentication, Spring Security will manage a session (typically cookie-based). Alternatively, if the SSO IdP issues JWTs that can be validated by ArchScope on each request, a stateless approach might be used by the API gateway/interceptor. For simplicity, a stateful session managed by Spring Security after initial SSO is assumed for MVP.
* **Authorization:**
    * **External Interceptor's Role:** The customer-provided interceptor is expected to run *before* ArchScope's `DispatcherServlet` (or as a very early Spring Security filter). It validates the SSO session/token and injects standardized role/permission information into the HTTP request (e.g., as headers like `X-User-Roles: ROLE_ARCHSCOPE_ADMIN,ROLE_TEAM_LEAD` or into `SecurityContextHolder`).
    * **ArchScope's Role:**
        * `archscope-facade` (Controllers): Can use Spring Security's method security (`@PreAuthorize("hasRole('ARCHSCOPE_ADMIN')")` or checking `HttpServletRequest.isUserInRole()`) based on the roles injected by the external interceptor.
        * `archscope-app` (Application Services): Can receive the `AuthenticatedUser` (or just their roles) as a parameter or from `SecurityContextHolder` and perform business-level authorization checks (e.g., "is this admin user allowed to change visibility of *this specific* project?").
* **Public APIs:** Paths like `POST /projects`, `GET /projects/{id}/documentation/**`, `GET /system/error-codes` are configured in `SecurityConfig` to be `permitAll()`.

### 9.2 错误处理
* **API Layer (`archscope-facade`):**
    * Global exception handling using `@ControllerAdvice` and `@ExceptionHandler`.
    * Custom domain-specific exceptions (e.g., `ProjectNotFoundException` from `archscope-domain` or `app`) are caught and mapped to appropriate HTTP status codes (e.g., 404) and a standardized `ErrorResponse` DTO.
    * Bean Validation exceptions (`MethodArgumentNotValidException`) are mapped to HTTP 400 with detailed field errors in the `ErrorResponse`.
    * Unexpected/unhandled exceptions are mapped to HTTP 500 with a generic error message for the client, while logging the full stack traceサーバー側で.
* **Error Codes (`FR-DOCSITE-008`):** A defined set of error codes (e.g., `PRJ_001_REPO_URL_INVALID`, `TASK_005_ANALYSIS_TIMEOUT`) will be included in the `ErrorResponse.code` field. The `GET /system/error-codes` API will provide a list of these codes with descriptions and troubleshooting advice, which will be displayed on a dedicated help page.

### 9.3 日志
* **Framework:** SLF4J as the facade, Logback (default with Spring Boot) or Log4j2 as the implementation.
* **Format:** Structured logging in JSON format for easy parsing by log management systems. Each log entry should include:
    * Timestamp (ISO 8601)
    * Log Level (ERROR, WARN, INFO, DEBUG, TRACE)
    * Thread Name
    * Logger Name (typically class FQN)
    * Message
    * Stack Trace (for errors)
    * MDC (Mapped Diagnostic Context) for:
        * `traceId` / `correlationId` (for distributed tracing, even in monolith useful for async tasks)
        * `userId` (if authenticated, from SSO context)
        * `projectId` (if applicable to the log entry)
        * `taskId` (if applicable)
* **Output:** Logs written to `stdout`/`stderr` to be collected by K8s log agents (e.g., Fluentd, Filebeat) and shipped to a central logging platform (e.g., Elasticsearch/Loki).
* **Audit Logging:** Specific critical actions (e.g., admin changes project visibility/status, project deletion (Post-MVP)) should generate distinct audit log entries, possibly to a separate log stream or database table for security auditing.

### 9.4 事务管理
* **Primary Scope:** `archscope-app` layer (Application Services).
* **Mechanism:** Spring's declarative transaction management using `@Transactional` annotation on public methods of application service beans.
* **Propagation:** Default propagation (`REQUIRED`) is generally suitable. Use `REQUIRES_NEW` asesinato 특정 작업이 부모 트랜잭션과 독립적으로 커밋/롤백되어야 하는 경우에 신중하게 사용합니다.
* **Database:** Applies to MySQL operations. Neo4j operations via Spring Data Neo4j can also participate in Spring-managed transactions.
* **Distributed Transactions (Post-MVP):** For operations spanning MQ message sending and DB updates requiring strict atomicity, consider:
    * **Transactional Outbox Pattern:** Write an event/message to a DB table within the main transaction. A separate process polls this table and reliably sends messages to MQ.
    * **RocketMQ Transactional Messages:** If atomicity between DB operation and MQ message send is critical.
    * For MVP, focus on "at-least-once" delivery for MQ and idempotent consumers, which is simpler.

### 9.5 配置管理
* **Spring Boot Profiles:** Use `application-{profile}.yml` (or `.properties`) for environment-specific configurations (e.g., `dev`, `test`, `staging`, `prod`).
* **K8s ConfigMaps:** Store non-sensitive application configurations (e.g., API endpoints for external services, default settings, feature flags). These are mounted as files or exposed as environment variables to Pods.
* **K8s Secrets:** Store sensitive configurations (e.g., database credentials, Git PATs, OpenRouter API Key, SSO client secret). These are mounted as files or exposed as environment variables with appropriate K8s RBAC protection.
* **Spring Cloud Kubernetes Config (Optional, Post-MVP):** For more dynamic configuration management, allowing apps to reload configuration from ConfigMaps/Secrets without restarting Pods.

## 10. 未来演进方向
* **微服务拆分:**
    * **候选模块:** `archscope-infrastructure-analysis` (代码解析，计算密集型，资源需求可能与其他模块不同), `archscope-infrastructure-docgen` (文档生成，I/O和模板处理密集型), `archscope-infrastructure-llm` (LLM交互，可能需要独立扩展和管理API Key/quota)。
    * **触发条件:** 当特定模块的性能瓶颈显著影响整体系统、其资源需求与其他模块差异过大、需要独立的技术栈演进或团队独立负责时。
    * **通信方式:** 拆分后，服务间通信可采用同步REST/gRPC或异步事件/消息（通过RocketMQ）。API Gateway (like Spring Cloud Gateway or K8s Ingress controller with advanced routing) would be essential.
* **多语言支持:** 逐步扩展 `archscope-infrastructure-analysis` 以支持Python, JavaScript/TypeScript等更多语言的AST解析器和针对性的LLM Prompt工程。
* **高级LLM应用:**
    * RAG (Retrieval Augmented Generation) 结合项目特有代码和文档，提升代码理解和问答的准确性。
    * 自动化代码摘要、技术债识别、架构合规性检查。
    * 更精准的设计模式识别和反模式检测。
* **更完善的健康度评估与架构守护:**
    * 可视化、可配置的规则引擎定义项目健康度指标和阈值。
    * 健康度趋势分析和预测。
    * 自动告警和通知，当项目健康度下降或出现严重架构问题时。
    * 与CI/CD流程深度集成，实现架构变更的自动卡点和评审。
* **IDE插件:** 开发VS Code, IntelliJ IDEA等主流IDE的插件，方便开发者在编码时直接查看ArchScope分析结果、依赖关系、文档链接等。
* **社区与生态:**
    * 开放部分核心API，鼓励社区开发者贡献：
        * 针对特定语言或框架的解析器插件。
        * 定制的文档模板。
        * 健康度评估规则集。
    * 建立开发者社区，分享最佳实践和使用案例。
* **增强的安全性:**
    * 更细粒度的权限控制（如果客户提供的拦截器不足以满足业务场景）。
    * 定期的第三方安全审计和渗透测试。
    * 引入SAST/DAST工具到CI/CD流程。
