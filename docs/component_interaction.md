# 架构鹰眼 ArchScope 组件交互设计

## 1. 组件交互概述

本文档详细描述了架构鹰眼 ArchScope 系统中各组件之间的交互关系，包括数据流、事件流和控制流，以便开发团队理解系统的运行机制和组件之间的依赖关系。

## 2. 核心组件关系图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  前端应用      +----->+  API网关       +----->+  认证服务      |
|                |      |                |      |                |
+-------+--------+      +--------+-------+      +----------------+
        |                        |
        |                        |
        v                        v
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  项目管理服务  +<---->+  任务调度服务  +<---->+  文档生成服务  |
|                |      |                |      |                |
+-------+--------+      +--------+-------+      +-------+--------+
        |                        |                      |
        |                        |                      |
        v                        v                      v
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  代码解析服务  |      |  版本监控服务  |      |  健康度评估服务|
|                |      |                |      |                |
+-------+--------+      +----------------+      +----------------+
        |
        |
        v
+----------------+
|                |
|  LLM服务       |
|                |
+----------------+
```

## 3. 主要交互流程

### 3.1 项目注册流程

```sequence
用户->前端应用: 提交项目地址
前端应用->API网关: 发送项目注册请求
API网关->认证服务: 验证用户权限
认证服务->API网关: 返回验证结果
API网关->项目管理服务: 转发项目注册请求
项目管理服务->项目管理服务: 验证项目地址
项目管理服务->数据库: 存储项目信息
数据库->项目管理服务: 返回存储结果
项目管理服务->任务调度服务: 创建初始解析任务
任务调度服务->项目管理服务: 返回任务创建结果
项目管理服务->API网关: 返回项目注册结果
API网关->前端应用: 返回注册结果
前端应用->用户: 显示注册结果
```

### 3.2 文档生成流程

```sequence
任务调度服务->代码解析服务: 分配代码解析任务
代码解析服务->代码仓库: 克隆/拉取代码
代码仓库->代码解析服务: 返回代码内容
代码解析服务->LLM服务: 发送代码解析请求
LLM服务->代码解析服务: 返回解析结果
代码解析服务->文档生成服务: 发送文档生成请求
文档生成服务->文档生成服务: 生成Markdown文档
文档生成服务->文件存储: 存储生成的文档
文件存储->文档生成服务: 返回存储结果
文档生成服务->数据库: 更新文档元数据
数据库->文档生成服务: 返回更新结果
文档生成服务->任务调度服务: 返回任务完成状态
任务调度服务->项目管理服务: 通知文档生成完成
项目管理服务->通知服务: 发送用户通知
通知服务->用户: 发送邮件/飞书通知
```

### 3.3 版本变更监控流程

```sequence
代码仓库->版本监控服务: 发送Webhook通知
版本监控服务->版本监控服务: 验证变更内容
版本监控服务->任务调度服务: 创建文档更新任务
任务调度服务->代码解析服务: 分配增量解析任务
代码解析服务->代码仓库: 获取变更内容
代码仓库->代码解析服务: 返回变更内容
代码解析服务->LLM服务: 发送增量解析请求
LLM服务->代码解析服务: 返回解析结果
代码解析服务->文档生成服务: 发送文档更新请求
文档生成服务->文件存储: 更新文档内容
文件存储->文档生成服务: 返回更新结果
文档生成服务->数据库: 更新文档版本信息
数据库->文档生成服务: 返回更新结果
文档生成服务->任务调度服务: 返回任务完成状态
任务调度服务->通知服务: 发送版本更新通知
通知服务->用户: 发送邮件/飞书通知
```

## 4. 组件详细交互

### 4.1 前端与后端交互

前端应用通过RESTful API与后端服务进行交互，主要包括：

1. **用户认证**：
   - 前端发送登录请求到认证服务
   - 认证服务验证用户身份并返回JWT令牌
   - 前端存储令牌并在后续请求中使用

2. **项目管理**：
   - 前端发送项目相关请求到项目管理服务
   - 项目管理服务处理请求并返回结果
   - 前端展示项目信息和状态

3. **文档访问**：
   - 前端请求文档内容
   - 后端返回Markdown文档或渲染后的HTML
   - 前端展示文档内容

### 4.2 后端服务间交互

后端服务之间通过同步API调用和异步消息队列进行交互：

1. **同步API调用**：
   - 服务间直接通过HTTP/RPC调用
   - 适用于需要立即响应的场景

2. **异步消息队列**：
   - 使用RocketMQ实现服务间异步通信
   - 适用于长时间运行的任务和解耦服务

### 4.3 与外部系统交互

系统与外部系统的交互主要包括：

1. **代码仓库集成**：
   - 通过GitHub/GitLab API获取代码内容
   - 接收Webhook通知获取代码变更

2. **LLM服务集成**：
   - 通过API调用LLM服务进行代码解析
   - 使用提示词工程技术提高解析质量

3. **通知服务集成**：
   - 通过邮件/飞书API发送通知
   - 支持自定义通知模板和触发条件

## 5. 数据流向

### 5.1 项目数据流

```
用户 -> 项目管理服务 -> 数据库 -> 代码解析服务 -> 文档生成服务 -> 文件存储 -> 前端展示
```

### 5.2 任务数据流

```
触发事件 -> 任务调度服务 -> 任务队列 -> 执行服务 -> 结果存储 -> 通知服务
```

### 5.3 统计数据流

```
用户访问 -> 访问日志 -> 统计分析服务 -> 统计数据存储 -> 报表生成 -> 前端展示
```

## 6. 异常处理流程

### 6.1 任务执行异常

```sequence
执行服务->执行服务: 检测到任务执行异常
执行服务->任务调度服务: 报告执行异常
任务调度服务->任务调度服务: 记录异常信息
任务调度服务->任务调度服务: 判断是否需要重试
任务调度服务->执行服务: 重新分配任务(如需要)
任务调度服务->通知服务: 发送异常通知(如需要)
通知服务->管理员: 发送异常告警
```

### 6.2 系统服务异常

```sequence
监控系统->监控系统: 检测到服务异常
监控系统->告警服务: 触发告警
告警服务->管理员: 发送告警通知
监控系统->自动恢复服务: 尝试自动恢复
自动恢复服务->服务实例: 重启或替换实例
自动恢复服务->监控系统: 报告恢复结果
```

## 7. 安全交互

### 7.1 认证流程

```sequence
用户->前端应用: 提供登录凭证
前端应用->认证服务: 发送认证请求
认证服务->认证服务: 验证凭证
认证服务->前端应用: 返回JWT令牌
前端应用->前端应用: 存储令牌
前端应用->API网关: 请求资源(带令牌)
API网关->认证服务: 验证令牌
认证服务->API网关: 返回验证结果
API网关->后端服务: 转发请求(如验证通过)
后端服务->API网关: 返回资源
API网关->前端应用: 返回资源
前端应用->用户: 展示资源
```

### 7.2 授权流程

```sequence
API网关->认证服务: 请求用户权限
认证服务->数据库: 查询用户角色
数据库->认证服务: 返回用户角色
认证服务->认证服务: 检查资源访问权限
认证服务->API网关: 返回授权结果
API网关->后端服务: 转发请求(如授权通过)
```

## 8. 扩展点

系统设计中预留了以下扩展点，以支持未来功能的扩展：

1. **插件系统**：
   - 支持自定义代码解析器
   - 支持自定义文档模板
   - 支持自定义健康度评估规则

2. **集成接口**：
   - 提供标准化的API接口
   - 支持第三方系统集成
   - 支持自定义通知渠道

3. **多语言支持**：
   - 界面国际化框架
   - 多语言文档生成
   - 跨语言代码解析