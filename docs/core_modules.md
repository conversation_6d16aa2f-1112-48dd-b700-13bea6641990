# 核心功能模块分析

从用户可见的功能角度，`架构鹰眼 ArchScope` 系统的核心功能可以分解为以下模块：

## 1. 项目管理模块 (Project Management Module)

**职责**:
- 接收并存储项目信息，包括仓库地址（支持 GitLab, GitHub 等）。
- 管理项目的元数据，如名称、描述、所有者、访问权限等。
- 提供项目列表、详情、编辑和删除功能。
- 与其他模块交互，触发文档生成和更新任务。

**子模块/关键功能**:
- 项目注册 (Project Registration)
- 项目信息存储 (Project Information Storage)
- 项目元数据管理 (Project Metadata Management)
- 访问控制 (Access Control)

## 2. 文档生成与解析模块 (Document Generation & Parsing Module)

**职责**:
- 根据项目仓库内容（代码、文档等）自动解析和生成结构化的文档内容（Markdown 格式）。
- 支持解析不同类型的源文件（如 Markdown, Java, YAML, etc.）。
- 提取关键信息，如架构设计、接口定义、依赖关系等。
- 生成符合预定结构的 Markdown 文件。

**子模块/关键功能**:
- 仓库克隆与同步 (Repository Cloning & Sync)
- 文件解析器 (File Parsers) - Markdown Parser, Code Parser, etc.
- 结构化文档生成 (Structured Document Generation)
- 元数据提取 (Metadata Extraction)

## 3. 变更感知模块 (Change Sensing Module)

**职责**:
- 监控已注册项目仓库的变更。
- 支持多种触发机制，如 Webhook, 定时轮询等。
- 在检测到变更时，通知任务管理模块创建文档更新任务。

**子模块/关键功能**:
- Webhook 接收器 (Webhook Receiver)
- 定时扫描器 (Scheduled Scanner)
- 变更检测逻辑 (Change Detection Logic)
- 事件通知 (Event Notification)

## 4. 任务管理与调度模块 (Task Management & Scheduling Module)

**职责**:
- 接收来自变更感知模块或手动触发的文档生成/更新任务。
- 维护任务队列，管理任务的生命周期（排队、进行中、完成、失败）。
- 根据项目星级和任务类型实现智能任务调度和优先级分配。
- 分配任务给可用的处理进程。

**子模块/关键功能**:
- 任务队列 (Task Queue) - 基于 RocketMQ
- 任务调度器 (Task Scheduler)
- 优先级管理 (Priority Management)
- 任务状态跟踪 (Task Status Tracking)
- 工作进程管理 (Worker Process Management)

## 5. 网站托管与渲染模块 (Website Hosting & Rendering Module)

**职责**:
- 存储生成的 Markdown 文档文件。
- 提供 Web 服务，将 Markdown 文件渲染成静态 HTML 网站对外展示。
- 支持版本选择和版本对比功能。

**子模块/关键功能**:
- 文件存储 (File Storage) - 本地文件系统或对象存储
- Markdown 渲染引擎 (Markdown Rendering Engine)
- 静态文件服务 (Static File Serving)
- 版本切换与对比接口 (Version Switching & Comparison Interface)

## 6. 统计与分级模块 (Statistics & Ranking Module)

**职责**:
- 统计各项目文档网站的访问量。
- 根据访问量数据对项目进行分级（1-5星）。
- 提供访问量和分级数据的展示接口。
- 与任务管理模块共享项目分级信息用于任务优先级。

**子模块/关键功能**:
- 访问日志记录 (Access Log Recording)
- 访问量统计 (Visit Count Aggregation)
- 项目分级算法 (Project Ranking Algorithm)
- 统计数据存储 (Statistics Data Storage) - 基于 Redis 或数据库

## 7. 安全与权限集成模块 (Security & Permission Integration Module)

**职责**:
- 集成统一用户管理和权限控制体系。
- 控制用户对项目和系统功能的访问权限。
- 确保系统安全性。

**子模块/关键功能**:
- 统一认证集成 (Unified Authentication Integration)
- 角色与权限模型 (Role & Permission Model)
- 资源访问控制 (Resource Access Control)
- 安全审计 (Security Audit)