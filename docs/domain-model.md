# 领域模型文档: ArchScope - 架构鹰眼 (MVP阶段)

## 1. 引言

本文档旨在定义和描述 ArchScope 系统在最小可行产品（MVP）阶段的核心领域概念、实体、值对象、聚合及其它们之间的关系。领域模型是理解系统业务逻辑、指导后续技术设计和实现的关键。本文档基于已确认的PRD文档进行编写。

## 2. 核心领域概念 (Core Domain Concepts)

以下是ArchScope MVP阶段的核心领域概念：

### 2.1 项目 (Project)

* **描述 (Description):** 代表一个在ArchScope系统中注册的、待分析和生成文档的软件项目。
* **主要属性 (Key Attributes):**
  * `projectId`: String (UUID, 唯一标识)
  * `name`: String (项目名称, 可从仓库URL自动初始化或用户手动维护) [user clarification]
  * `description`: String (项目描述, 可选, 可从仓库URL自动初始化) [user clarification]
  * `repositoryUrl`: String (项目Git仓库的URL, 用户注册时提供) [user clarification]
  * `defaultBranch`: String (默认分析分支, 可从仓库URL自动初始化) [user clarification]
  * `detectedLanguage`: Language (枚举: JAVA - MVP阶段仅支持Java)
  * `branches`: List<String> (分支列表, 可从仓库URL自动初始化) [user clarification]
  * `lastAnalysisTime`: DateTime (最近一次成功分析完成的时间)
  * `registeredAt`: DateTime (项目注册时间)
* **角色 (Role):** 聚合根 (Aggregate Root)

### 2.2 分析任务 (AnalysisTask)

* **描述 (Description):** 代表一次对特定项目特定代码版本（Commit ID）的分析和文档生成请求。
* **主要属性 (Key Attributes):**
  * `taskId`: String (UUID, 唯一标识)
  * `projectId`: String (关联的Project ID)
  * `commitIdToAnalyze`: String (本次任务分析的代码Commit ID)
  * `taskType`: TaskType (枚举: FULL_ANALYSIS - MVP主要类型)
  * `status`: TaskStatus (枚举: PENDING, RUNNING, SUCCESS, FAILED, CANCELED)
  * `failureReason`: String (任务失败原因, 可选)
  * `logSummary`: String (任务执行日志摘要, 可选)
  * `createdAt`: DateTime (任务创建时间)
  * `startedAt`: DateTime (任务开始执行时间, 可选)
  * `endedAt`: DateTime (任务结束执行时间, 可选)
* **角色 (Role):** 聚合根 (Aggregate Root)

### 2.3 文档版本 (DocumentVersion)

* **描述 (Description):** 代表某个项目在特定代码版本（Commit ID）下生成的一整套文档的集合。
* **主要属性 (Key Attributes):**
  * `documentVersionId`: String (UUID, 唯一标识)
  * `projectId`: String (关联的Project ID)
  * `commitId`: String (关联的代码Commit ID)
  * `generatedAt`: DateTime (此版本文档集生成的时间)
  * `documents`: List<GeneratedDocument> (包含的已生成文档实体列表)
* **角色 (Role):** 聚合根 (Aggregate Root)

### 2.4 已生成文档 (GeneratedDocument)

* **描述 (Description):** 代表一篇具体的、由系统（主要由LLM）为特定项目版本生成的Markdown文档。
* **主要属性 (Key Attributes):**
  * `documentId`: String (UUID, 唯一标识)
  * `documentName`: String (文档名称, 例如 "architecture.md", "data_model.md", "c4_component_diagram.md")
  * `markdownContent`: String (完整的Markdown内容, 包括Mermaid图表代码。MVP采用全量存储) [user clarification, prd3.md]
  * `documentType`: DocumentType (枚举: ARCHITECTURE_DESIGN, DATA_MODEL, COMPONENT_DIAGRAM_MERMAID, ER_DIAGRAM_MERMAID, GENERAL_MARKDOWN 等)
* **角色 (Role):** 实体 (Entity, 属于 `DocumentVersion` 聚合)

### 2.5 代码分析结果引用 (CodeAnalysisResultRef)

* **描述 (Description):** 代表一次代码分析任务成功后产出的结构化代码知识的引用或摘要。实际的详细图谱数据存储在图数据库（如Neo4j）中。该引用主要用于关联分析任务和后续的文档生成过程。
* **主要属性 (Key Attributes):**
  * `analysisTaskId`: String (关联的AnalysisTask ID)
  * `graphStorageReference`: String (指向图数据库中具体图数据的引用标识，可选)
  * `summary`: String (代码知识图谱的摘要信息或关键指标，可选)
  * `extractedAt`: DateTime (知识提取完成时间)
* **角色 (Role):** 值对象 (Value Object) 或与 `AnalysisTask` 关联的实体，取决于其复杂度和生命周期管理。

### 2.6 错误码指南条目 (ErrorGuideItem)

* **描述 (Description):** 代表错误码指南中的一个条目，由ArchScope团队预设和维护。系统通过分析用户项目代码，将识别出的错误模式链接到这些条目。
* **主要属性 (Key Attributes):**
  * `errorCode`: String (唯一错误码)
  * `title`: String (错误标题/模式描述)
  * `description`: String (详细描述)
  * `solutionSteps`: String (推荐的解决方案或排查步骤)
  * `tags`: List<String> (用于错误模式匹配的标签或关键词，可选)
* **角色 (Role):** 引用数据实体 (由系统读取和引用，非系统动态创建和管理的核心业务实体)

## 3. 聚合关系 (Aggregates)

聚合是领域驱动设计中的核心概念，用于组织和管理一组相关的领域对象，确保数据的一致性和业务规则的正确执行。

* **项目聚合 (Project Aggregate):**
  * **聚合根 (Root):** `Project`
  * **包含 (Contains):** `GitRepositoryInfo` (值对象, 描述仓库具体信息如URL、类型等，实际访问凭证由基础设施层安全管理)
  * **不变性约束 (Invariants - Conceptual):**
    * `projectId` 必须全局唯一。
    * `repositoryUrl` 对于已注册项目应保持稳定（或有明确的变更流程）。

* **分析任务聚合 (AnalysisTask Aggregate):**
  * **聚合根 (Root):** `AnalysisTask`
  * **不变性约束 (Invariants - Conceptual):**
    * `taskId` 必须全局唯一。
    * 任务状态转换必须遵循预定义的生命周期 (e.g., PENDING -> RUNNING -> SUCCESS/FAILED)。

* **文档版本聚合 (DocumentVersion Aggregate):**
  * **聚合根 (Root):** `DocumentVersion`
  * **包含 (Contains):** `List<GeneratedDocument>` (实体列表)
  * **不变性约束 (Invariants - Conceptual):**
    * `documentVersionId` 必须全局唯一。
    * 一个 `DocumentVersion` 唯一对应一个 `projectId` 和一个 `commitId`。
    * `documents`列表中的 `documentName` 在同一 `DocumentVersion` 内应唯一。

## 4. 值对象 (Value Objects)

值对象是没有唯一标识、通过其属性值来定义的不可变对象。

* `GitRepositoryInfo`: (如2.1节 `Project` 中描述) 包含 `url`, `type` (GITHUB, GITLAB)。
* `CommitID`: (String) 代表Git提交的SHA哈希值。
* `TaskStatus`: (Enum) 分析任务的状态，如 `PENDING`, `RUNNING`, `SUCCESS`, `FAILED`。
* `Language`: (Enum) 项目的主要编程语言，MVP阶段仅 `JAVA`。
* `DocumentType`: (Enum) 已生成文档的类型，如 `ARCHITECTURE_C4_MODEL`, `DATA_MODEL_ERD`, `COMPONENT_DIAGRAM_MERMAID`。
* `MermaidCode`: (String) 存储Mermaid语法的文本内容，用于图表生成。
* `NavigationNode`: (DTO/Value Object for UI) 包含 `title`, `path`, `children` (List<NavigationNode>)，用于构建文档网站导航树。

## 5. 领域服务 (Domain Services - Conceptual for MVP)

在MVP阶段，许多领域逻辑可能通过应用服务直接协调聚合根来完成。以下是一些概念上可能存在的领域服务，它们封装了不适合放在特定聚合根或实体中的核心业务规则：

* **ProjectInitializationService (项目初始化服务):** 负责从Git仓库URL提取项目元数据（名称、描述、分支等）的复杂逻辑 [user clarification]。
* **CodeStructureAnalyzer (代码结构分析服务):** 封装AST解析和提取代码结构形成 `CodeAnalysisResultRef` 的核心逻辑。
* **DocumentationGenerationOrchestrator (文档生成编排服务):** 协调LLM（通过其适配器）、`LLMPrompt`（从配置加载）、以及 `CodeAnalysisResultRef` 来创建 `GeneratedDocument` 集合。
* **DocumentComparisonEngine (文档比较引擎):** 负责比较两个 `GeneratedDocument` 的 `markdownContent` 并生成diff结果 [user clarification]。
* **ProjectErrorLinker (项目错误链接服务):** 负责分析代码（可能借助LLM的输出或AST分析结果）识别错误模式，并将其与预设的 `ErrorGuideItem` 进行关联 [user clarification]。

## 6. 关系图 (Conceptual Relationships Diagram)

以下是对核心领域概念之间关系的文本描述。这些关系可以用Mermaid等图表工具进行可视化。

* 一个 **Project** 可以拥有多个 **AnalysisTask**。
  * `Project "1" -- "0..*" AnalysisTask`
* 一个 **Project** 可以拥有多个 **DocumentVersion** (对应不同Commit的文档)。
  * `Project "1" -- "0..*" DocumentVersion`
* 一个 **AnalysisTask** 总是关联到一个 **Project**。
  * `AnalysisTask "1" -- "1" Project`
* 一个 **AnalysisTask** (成功后) 产出一个 **CodeAnalysisResultRef**。
  * `AnalysisTask "1" -- "0..1" CodeAnalysisResultRef` (0..1表示任务可能失败)
* 一个 **AnalysisTask** 的结果（通过`CodeAnalysisResultRef`）用于生成一个特定 **CommitID** 的 **DocumentVersion**。
* 一个 **DocumentVersion** 总是关联到一个 **Project** 和一个唯一的 **CommitID**。
  * `DocumentVersion "1" -- "1" Project`
* 一个 **DocumentVersion** 包含多篇 **GeneratedDocument**。
  * `DocumentVersion "1" -- "1..*" GeneratedDocument`

**Mermaid 示例 (概念性):**
```mermaid
erDiagram
    Project ||--o{ AnalysisTask : "has"
    Project ||--o{ DocumentVersion : "has"
    AnalysisTask{
        string taskId PK
        string projectId FK
        string commitIdToAnalyze
        TaskStatus status
    }
    Project {
        string projectId PK
        string name
        string repositoryUrl
    }
    DocumentVersion {
        string documentVersionId PK
        string projectId FK
        string commitId
    }
    DocumentVersion ||--|{ GeneratedDocument : "contains"
    GeneratedDocument {
        string documentId PK
        string documentName
        DocumentType documentType
        string markdownContent
    }
    AnalysisTask ||--o| CodeAnalysisResultRef : "produces"

    %% ErrorGuideItem is reference data, not directly linked in this ERD style typically
    %% LLMPrompt is configuration input, not a managed entity in the same way