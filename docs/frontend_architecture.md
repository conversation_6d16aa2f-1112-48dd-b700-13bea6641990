# 前端架构设计 (TypeScript/Vue 3.x/Tailwind CSS)

根据需求，前端将采用 TypeScript、Vue 3.x、Tailwind CSS、FontAwesome 6 以及第三方组件库。项目将采用模块化、组件化的架构。

## 项目结构

前端项目可以采用标准的 Vue CLI 或 Vite 项目结构，并根据功能模块进行组织：

```
arch-scope-frontend/
├── public/             # 静态资源
├── src/
│   ├── assets/         # 静态文件 (图片、字体等)
│   ├── components/     # 可复用组件
│   ├── layouts/        # 页面布局组件
│   ├── router/         # Vue Router 配置
│   ├── stores/         # Vuex 或 Pinia 状态管理 (如果需要)
│   ├── views/          # 页面组件 (对应不同的路由)
│   │   ├── projects/   # 项目相关页面 (列表、详情)
│   │   ├── tasks/      # 任务相关页面 (队列)
│   │   └── ...
│   ├── App.vue         # 根组件
│   ├── main.ts         # 入口文件
│   └── styles/         # 全局样式 (Tailwind 配置等)
├── .gitignore
├── package.json
├── tailwind.config.js  # Tailwind CSS 配置文件
├── tsconfig.json       # TypeScript 配置文件
└── vite.config.ts      # Vite 配置文件 (或 vue.config.js for Vue CLI)
```

## 技术选型与应用

*   **Vue 3.x**: 作为核心的JavaScript框架，利用其 Composition API、Teleport、Fragments 等新特性提高开发效率和性能。
*   **TypeScript**: 提供静态类型检查，增强代码的可维护性和可读性，减少运行时错误。
*   **Tailwind CSS**: 原子化 CSS 框架，通过组合 class 来快速构建 UI 界面，提高开发效率。在 `tailwind.config.js` 中进行定制。
*   **FontAwesome 6**: 图标库，用于提供丰富的矢量图标，增强界面美观性。
*   **Vue Router**: 用于实现前端路由，管理页面之间的导航。
*   **状态管理**: 根据项目复杂度，可以选择 Pinia 进行状态管理。
*   **第三方组件库**: 可以根据需要引入成熟的 Vue 3 组件库，如 Element Plus, Ant Design Vue 等，用于提供更丰富的 UI 组件（表格、表单、模态框等），加速开发。
*   **构建工具**: 推荐使用 Vite，提供更快的开发服务器启动和热更新速度。

## 组件设计原则

*   **单一职责**: 每个组件只负责一个功能或 UI 部分。
*   **可复用性**: 设计通用组件（如按钮、输入框、表格行等），提高代码复用率。
*   **Props Down, Events Up**: 数据通过 props 从父组件传递到子组件，子组件通过触发事件与父组件通信。
*   **智能组件 vs 木偶组件**: 区分负责业务逻辑和状态管理的智能组件（通常是页面组件或容器组件）和只负责 UI 渲染的木偶组件。

## 界面原型实现思路

之前设计的 HTML 原型将作为基础，使用 Vue 3 组件和 Tailwind CSS 来实现：

*   **`project_list.html`**: 对应 `views/projects/ProjectList.vue` 页面组件。表格数据通过调用后端 API 获取，使用 Vue 的 `v-for` 指令渲染列表。星级显示可以使用计算属性或方法根据星级数据生成 FontAwesome 图标。
*   **`project_detail.html`**: 对应 `views/projects/ProjectDetail.vue` 页面组件。通过路由参数获取项目ID，调用后端 API 获取项目详情和统计数据。文档链接和统计信息根据后端返回的数据渲染。
*   **`task_queue.html`**: 对应 `views/tasks/TaskQueue.vue` 页面组件。表格数据通过调用后端 API 获取任务列表。任务状态可以使用计算属性或方法根据状态值显示不同的样式和图标。

## 与后端交互

*   使用 Axios 或浏览器内置的 Fetch API 进行异步请求与后端 API (`arch-scope-facade` 模块) 进行数据交互。
*   可以封装 API 请求服务，统一处理请求、响应、错误处理等。

## 开发规范与实践

*   遵循 Vue 3 官方风格指南和 TypeScript 编码规范。
*   使用 ESLint 和 Prettier 保证代码风格一致性。
*   编写组件的单元测试（如使用 Vue Test Utils 和 Jest/Vitest）。
*   利用 Vue Devtools 进行调试。
*   所有文本内容考虑国际化（如果需要）。

## 项目文档版本管理前端实现思路

*   **版本选择**: 在项目文档页面 (对应 `project_doc_home.html`, `project_doc_architecture.html` 等原型)，需要添加一个版本选择器（如下拉框）。前端通过调用后端 API 获取项目的所有文档版本列表，并在选择器中展示。
*   **版本切换**: 当用户在版本选择器中选择不同的版本时，前端需要更新页面内容，显示对应版本的文档。这可以通过在路由中包含版本号参数，或者通过调用后端 API 获取特定版本的文档内容来实现。
*   **版本对比**:
    *   **UI**: 在项目文档页面添加“版本对比”按钮或入口。点击后，可能弹出一个模态框或跳转到一个新的页面，允许用户选择两个要对比的版本。
    *   **交互**: 用户选择两个版本后，前端调用后端提供的版本对比 API，将两个版本号发送给后端。
    *   **结果展示**: 前端接收后端返回的 Diff 结果，使用一个 Diff 视图组件（可以寻找现有的 Vue Diff 组件库）来可视化展示两个版本之间的增删改差异。
*   **路由设计**: 项目文档相关的路由需要支持版本号参数，例如 `/projects/:projectId/docs/:version/:documentPath`，其中 `:version` 是可选的，默认为最新版本。
*   **状态管理**: 可以使用状态管理库 (Vuex/Pinia) 来存储当前查看的项目文档版本信息，方便在不同组件之间共享。