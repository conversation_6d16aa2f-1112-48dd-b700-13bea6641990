## 8. 风险与缓解措施 (Risks and Mitigations)

### 8.1 技术挑战 (Technical Challenges)
* **风险1:** LLM解析的准确性、一致性、成本控制以及在复杂、多语言代码库中的表现。
    * **缓解:**
        * 采用“AST为主，LLM为辅”策略，AST提供结构化基础，减少对LLM的绝对依赖。
        * MVP阶段简化LLM应用，聚焦AST。
        * 持续优化Prompt Engineering，使用RAG提升特定领域代码理解。
        * 加强对增量解析中LLM上下文丢失问题的处理。
        * 提供并推广本地部署LLM选项，降低对外部服务依赖和成本，保障数据隐私。
        * 对LLM分析结果设置置信度，允许用户反馈和校正。
        * 广泛测试不同类型和规模的项目。
* **风险2:** 高效处理超大型代码仓库（解析时间、内存消耗、图数据库规模）。
    * **缓解:**
        * 尽早实现并持续优化增量解析逻辑。
        * 优化AST和图数据构建算法，使用高效数据结构。
        * 代码分块解析策略，维持上下文连贯性。
        * 探索后台任务的分布式处理。
        * 图数据库选择时考虑其分布式扩展能力和性能。
        * 对超大型仓库设置合理的预期，或提供分阶段、分模块解析选项。
* **风险3:** 维护多种编程语言解析器的复杂性和成本（MVP后）。
    * **缓解:**
        * 优先支持核心语言，逐步扩展。
        * 尽可能使用Tree-sitter等通用解析器生成工具，或成熟的第三方库。
        * 设计可插拔的解析器架构，方便添加新语言支持。
        * 鼓励社区贡献特定语言的解析器插件。
* **风险4 (新增):** SSO集成复杂度。
    * **描述:** 不同SSO方案 (SAML, OIDC等) 的集成细节、属性映射、会话管理、错误处理可能存在差异，对接和调试可能比预期耗时。
    * **缓解:**
        * 在项目早期阶段，尽快确定企业目标SSO提供商及其支持的标准协议。
        * 获取详细的SSO集成文档和测试环境。
        * 优先选择并实现一种广泛使用的标准协议 (如OIDC Connect) 作为基础。
        * 充分进行集成测试，覆盖各种登录、登出、会话超时、错误场景。
        * 清晰定义需要从SSO获取的用户属性和角色/组信息。
* **风险5 (新增):** Java AST解析的深度和广度。
    * **描述:** 虽然JavaParser等库相对成熟，但覆盖所有Java版本特性、大型复杂项目中的所有边界情况、以及不同构建工具（Maven, Gradle）和项目结构，仍可能遇到未预期的解析问题。
    * **缓解:**
        * MVP阶段聚焦于主流Java版本 (如Java 8/11/17) 和标准的Maven/Gradle项目结构。
        * 持续收集多样化的Java开源项目和内部项目作为测试用例。
        * 对解析错误进行详细记录和归类，迭代优化解析逻辑。
        * 允许用户在项目配置中指定源码路径、JDK版本等辅助信息以提高解析准确率。

### 8.2 MVP范围定义 (MVP Scope Definition)
* **风险:** MVP范围蔓延，初期试图包含过多功能，导致交付延迟和核心价值模糊。
    * **缓解:**
        * 严格遵循已确认的MVP范围（见7.1节）。
        * 核心工作流 (SSO登录 -> 注册Java项目 -> 手动解析 -> 生成基础文档网站 -> 查看文档) 优先。
        * 尽早获取用户对核心价值的反馈，快速迭代。

### 8.3 资源约束 (Resource Constraints)
* **风险:** 开发时间/成本被低估，特别是LLM集成（即便MVP简化）、图数据库的熟练应用、SSO集成。
    * **缓解:**
        * 采用分阶段的路线图，增量交付价值。
        * 优先保障核心功能实现。
        * 对LLM的选择和集成保持灵活性。
        * 清晰定义每个阶段“足够好”的标准。
        * 透明化项目进展和潜在风险。

### 8.4 外部服务依赖 (Dependency on External Services)
* **风险:** 外部LLM API的变更、成本波动、可用性问题、服务限制。Git提供商API的限制。SSO提供商的可用性和API稳定性。
    * **缓解:**
        * 抽象外部服务接口，方便替换或适配不同提供商。
        * 监控API使用量和成本，设置预算告警。
        * 设计优雅降级策略。
        * 实现对外部API调用的速率限制、缓存和重试机制。
        * 积极开发和推广本地部署LLM的选项。
        * 对于SSO依赖，确保有明确的错误处理和用户指引（如SSO服务临时不可用时）。
        * 向用户清晰透明地展示数据处理流程，特别是涉及外部服务时的数据流向和隐私政策。

### 8.5 数据安全与隐私 (Data Security and Privacy)
* **风险:** 用户代码（尤其是私有仓库）和LLM交互数据的安全与隐私泄露。SSO集成过程中的敏感信息（如断言、令牌）处理不当。
    * **缓解:**
        * 严格遵循NFR-SEC中的安全措施。
        * 对于代码访问，使用用户授权的、有时效性的、最小权限的Token。
        * 敏感凭证加密存储。
        * LLM交互时进行数据脱敏，并优先本地模型。
        * SSO集成遵循安全最佳实践，如验证签名、加密断言（如果适用）、安全重定向。
        * 定期的安全审计和渗透测试。