# 用户故事文档: ArchScope - 架构鹰眼 (MVP阶段)

## 1. 引言

本文档包含ArchScope系统在最小可行产品（MVP）阶段的用户故事。用户故事从特定用户视角描述了对软件功能的需求，旨在帮助开发团队更好地理解用户期望和功能价值。每个故事通常遵循以下格式：“作为一个 [用户类型]，我想要 [执行某个动作]，以便 [达成某个目的/获得某种价值]。”

每个用户故事都附有详细的验收标准（Acceptance Criteria），这些标准将作为后续功能测试和用户验收的主要依据。

本文档中的用户故事基于已确认的PRD文档（`prd.md` - 由您确认的上一版本）进行编写。

## 2. 用户画像 (User Personas)

为更好地理解用户需求，我们定义了以下核心用户画像：

* **技术团队管理者 (Tech Lead / Engineering Manager - TLM)**
* **开发人员 (Developer - DEV)**
* **架构师 (Architect - ARCH)**
* **新成员 (New Joiner - NJ)**
* **外部协作者/使用方 (External Collaborator / Consumer - EXT)**

## 3. 用户故事 (User Stories)

---

### Epic 1: 项目注册与管理

**US-001: 通过仓库URL注册新项目**
* 作为一个 **开发人员 (DEV)**，我想要通过提供项目的Git仓库URL来注册一个新的Java项目，以便ArchScope能够开始分析该项目并生成相关文档。
* **验收标准:**
    1.  **Given** 我在“注册新项目”页面 (`register_project.html`原型所示)。
    2.  **When** 我在指定的输入框中输入一个有效的Java项目的Git仓库URL（例如GitHub或GitLab的HTTPS URL）。
    3.  **And** 我点击“提交注册”按钮。
    4.  **Then** 系统应在后台创建一个新的项目记录。
    5.  **And** 系统应尝试从提供的URL对应的仓库自动初始化项目名称、项目描述、分支列表等信息。
    6.  **And** 系统应为该项目（通常是默认分支）自动触发一个后台代码分析任务（状态为“排队中”）。
    7.  **And** 我应被重定向到项目列表页面或新创建项目的详情页面，并看到一个表示项目注册成功的提示信息。
    8.  **Given** 我在“注册新项目”页面。
    9.  **When** 我输入一个格式无效的Git仓库URL（例如，非URL字符串）。
    10. **And** 我点击“提交注册”按钮。
    11. **Then** 我应在当前页面看到一个清晰的错误提示，说明“仓库URL格式不正确”。
    12. **And** 系统不应创建任何新的项目记录或任务。
    13. **Given** 我在“注册新项目”页面。
    14. **When** 我输入一个系统无法访问的Git仓库URL（例如，私有仓库但系统无权访问，或URL不存在）。
    15. **And** 我点击“提交注册”按钮。
    16. **Then** 我应在当前页面看到一个清晰的错误提示，说明“无法访问指定的仓库URL，请检查URL或仓库权限”。
    17. **And** 系统不应创建任何新的项目记录或任务。
    18. **Given** 我成功注册了一个项目，但系统未能从仓库URL自动初始化所有预期的项目信息（例如，项目描述为空）。
    19. **Then** 项目记录仍应被创建，并且在项目详情页面，我应能看到已提取的信息，对于未能自动初始化的信息（如描述），系统应允许我稍后手动编辑和补充。

**US-002: 查看已注册的项目列表**
* 作为一个 **开发人员 (DEV)**，我想要查看一个包含所有已注册项目的列表，列表中包含项目名称和最后分析时间等摘要信息，以便我能快速找到并访问我关心的项目。
* **验收标准:**
    1.  **Given** 系统中已注册了至少一个项目。
    2.  **When** 我访问项目列表页面 (`project_list.html`原型所示)。
    3.  **Then** 我应能看到一个列表或卡片式的项目展示区域。
    4.  **And** 每个项目条目应至少显示项目名称、仓库URL（或其一部分）、以及最近一次成功分析的时间戳（如果分析过）。
    5.  **And** 项目列表应按某种默认顺序列出（例如，按注册时间倒序或项目名称升序）。
    6.  **And** 我可以点击任一项目条目以导航到该项目的详情页面。
    7.  **Given** 系统中没有任何已注册的项目。
    8.  **When** 我访问项目列表页面。
    9.  **Then** 我应看到一个提示信息，说明“当前没有已注册的项目”，并可能有一个引导我去“注册新项目”页面的链接或按钮。

**US-003: 查看特定项目的详细信息**
* 作为一个 **架构师 (ARCH)**，我想要查看特定项目的详细信息页面，该页面应展示项目元数据、指向其生成文档的入口链接以及最近的分析任务状态，以便我能拥有一个获取项目特定架构信息的中心位置。
* **验收标准:**
    1.  **Given** 我已成功注册一个项目并且至少完成了一次分析。
    2.  **When** 我从项目列表页面点击进入该项目的详情页面 (`project_detail.html`原型所示)。
    3.  **Then** 我应能看到该项目的核心元数据，如项目名称、仓库URL、默认分支、项目描述（如果存在）。
    4.  **And** 我应能看到一个明确的区域或链接，用以访问该项目最新生成的文档网站。
    5.  **And** 我应能看到该项目最近执行的分析任务列表及其状态（例如，最近5条任务的状态：成功、失败、运行中）。
    6.  **And** 页面应提供返回项目列表的导航链接。
    7.  **Given** 一个项目刚刚被注册，但尚未执行任何分析任务。
    8.  **When** 我访问该项目的详情页面。
    9.  **Then** “最近分析任务”区域应显示“尚无分析任务”或类似提示。
    10. **And** “访问文档网站”的链接可能暂时不可用或提示文档尚未生成。

**US-004: 手动触发代码分析任务**
* 作为一个 **开发人员 (DEV)**，我想要在一个项目的详情页面手动触发一次对该项目的全量代码分析（针对默认分支或选定分支），以便在代码发生重要变更后立即更新其架构文档。
* **验收标准:**
    1.  **Given** 我在某个已注册项目的详情页面。
    2.  **When** 我点击“手动触发分析”或类似操作的按钮/链接。
    3.  **Then** 系统应创建一个新的后台分析任务，该任务针对此项目的指定代码版本（MVP默认为最新commit on default branch）。
    4.  **And** 该任务的状态应初始化为“排队中”。
    5.  **And** 我应在界面上看到一个操作成功的即时反馈（例如，toast消息“分析任务已成功创建并加入队列”）。
    6.  **And** 项目详情页面的“最近分析任务”列表应更新以显示这个新创建的排队中任务。
    7.  **Given** 该项目当前已有一个正在运行或排队中的分析任务。
    8.  **When** 我尝试再次点击“手动触发分析”按钮。
    9.  **Then** 系统可以允许创建新任务并将其加入队列（遵循FIFO），或者（可选，但更好）提示“已有分析任务正在处理中，请稍后再试”并阻止创建重复的立即执行任务，以避免资源浪费（具体策略待定，MVP阶段允许排队即可）。

---

### Epic 2: 文档访问、理解与版本控制

**US-005: 访问和浏览生成的文档网站**
* 作为一个 **新成员 (NJ)**，我想要访问一个结构清晰、导航便捷的项目文档网站，以便我能快速理解其架构、数据模型以及如何开始使用或开发该项目。
* **验收标准:**
    1.  **Given** 一个项目已成功完成代码分析和文档生成。
    2.  **When** 我从项目详情页面点击“访问文档网站”的链接。
    3.  **Then** 我应能在一个新的浏览器标签页或当前窗口中看到该项目专属的文档网站首页 (`project_doc_home.html`原型所示)。
    4.  **And** 文档网站应包含一个清晰的左侧导航树，列出所有主要的文档章节（如：产品简介、架构设计、数据模型、组件图、E-R图、错误码指南等）。
    5.  **And** 点击导航树中的任一链接，右侧内容区域应正确显示对应的Markdown文档内容。
    6.  **And** 文档中的Java代码片段应有正确的语法高亮。
    7.  **And** 文档中嵌入的Mermaid格式图表代码（如组件图、E-R图）应被前端正确渲染为可视化图表。
    8.  **And** 文档网站应采用响应式设计，在桌面和主流移动设备上均能良好展示。

**US-006: 在文档网站中切换查看不同代码版本的文档**
* 作为一个 **开发人员 (DEV)**，我想要在项目文档网站中切换查看不同代码版本（Commit ID）对应的文档，以便我能了解特定历史版本的系统状态或架构设计。
* **验收标准:**
    1.  **Given** 一个项目已针对多个不同的Commit ID生成了文档版本。
    2.  **When** 我在项目文档网站的任意页面。
    3.  **Then** 我应能看到一个版本选择器（例如，下拉菜单），列出所有可用的文档版本（以Commit ID或关联的标签/日期标识）。
    4.  **And** 默认应显示最新版本的文档。
    5.  **When** 我从版本选择器中选择一个历史版本。
    6.  **Then** 文档网站的内容（包括导航树和当前页面内容）应刷新以展示所选历史版本对应的文档。
    7.  **And** 版本选择器应正确反映当前正在查看的版本。
    8.  **And** 切换版本后，所有图表和代码片段也应是该历史版本的内容。

**US-007: 对比不同文档版本的内容差异**
* 作为一个 **架构师 (ARCH)**，我想要在项目文档网站中选择两个不同版本的同一篇文档进行内容对比，以便清晰地看到架构、数据模型或其他文档内容在不同版本间的具体变化。
* **验收标准:**
    1.  **Given** 一个项目已针对多个不同的Commit ID生成了文档版本，且至少有一篇文档在不同版本间存在内容差异。
    2.  **When** 我在项目文档网站上，并发起一个内容对比操作（例如，通过专门的“版本对比”按钮或选项，如 `project_doc_compare.html` 原型所示）。
    3.  **And** 我选择了要对比的文档页面以及两个不同的文档版本（版本A和版本B）。
    4.  **Then** 系统应以并排或合并的视图展示这两个版本的文档内容。
    5.  **And** 从版本A到版本B的内容差异（新增、删除、修改的行）应通过不同的背景色或标记清晰高亮显示。
    6.  **And** 对于Mermaid图表或代码片段的差异，也应能以文本差异的形式展示。
    7.  **And** 界面应清晰标注哪个是旧版本（版本A），哪个是新版本（版本B）。

**US-008: 查阅特定于分析项目的错误码指南**
* 作为一个 **开发人员 (DEV)**，当我在一个由ArchScope分析的项目中遇到问题时，我希望能够查阅到一个指南页面，该页面列出了ArchScope在该项目中可能识别出的特定错误模式，并提供了相应的解释或解决建议，以便我更快地定位和修复问题。
* **验收标准:**
    1.  **Given** ArchScope系统已配置了一份由ArchScope团队维护的错误码和解决方案指南（静态或半静态内容）。
    2.  **And** 一个项目已完成分析，并且在分析过程中，系统（可能通过LLM）识别出该项目中存在的某些错误模式，这些模式与指南中的条目相关联。
    3.  **When** 我访问该项目的文档网站，并导航到“错误码指南”或类似页面 (`error_codes_guide.html`原型所示)。
    4.  **Then** 我应能看到一个结构化的页面，列出适用于该项目的错误码/错误模式、描述和建议的解决方案。
    5.  **And** （MVP可选，但更佳）如果系统在代码分析时识别出具体的错误实例，文档的其他部分（如代码浏览器或分析报告）可能会提供直接链接到错误码指南中相关条目的功能。
    6.  **And** 指南页面的内容应清晰易懂，帮助我理解问题并找到解决方案。

---

### Epic 3: 后台任务监控 (MVP简化版)

**US-009: 查看手动触发的分析任务状态**
* 作为一个 **开发人员 (DEV)**，在我手动触发一次项目分析后，我想要在项目详情页面查看到该任务的当前状态（例如：排队中、运行中、成功、失败），以便我知道分析进度以及文档是否已更新。
* **验收标准:**
    1.  **Given** 我已在一个项目的详情页面手动触发了一次分析任务。
    2.  **When** 我停留在或刷新该项目详情页面。
    3.  **Then** 页面上的“最近分析任务”列表（或类似区域）应显示我刚刚触发的任务。
    4.  **And** 该任务应清晰地显示其当前状态（如：排队中）。
    5.  **And** 如果任务正在运行，状态应更新为“运行中”。
    6.  **And** 如果任务成功完成，状态应更新为“成功”，并且项目的“最后分析时间”应更新，文档网站链接应指向最新内容。
    7.  **And** 如果任务执行失败，状态应更新为“失败”，并可能简要显示失败原因或提供查看详细日志的指引（详细日志查看功能可能是MVP后）。
    8.  **And** 任务状态的更新可以通过前端定时轮询后端API的方式实现。

---
**文档结束**