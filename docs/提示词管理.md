## 提示词管理

本项目采用基于文件的提示词管理系统，所有提示词都存储在`prompts/`目录下的YAML文件中，便于随时修改和管理。

### 提示词目录结构

```
prompts/
├── repository/        # 仓库级别的提示词
├── code/              # 代码分析提示词
├── documentation/     # 文档生成提示词
├── integration/       # 集成分析提示词
└── version/           # 版本比较提示词
```

### 提示词文件格式

每个YAML文件包含元数据和多个提示词模板：

```yaml
metadata:
  name: "提示词名称"
  description: "提示词描述"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  prompt_key:
    description: "提示词描述"
    model: "gpt-4"
    parameters:
      temperature: 0.1
      max_tokens: 2000
    template: |
      提示词模板内容，可以包含变量 {{variable_name}}
```

### 使用提示词

通过`PromptManager`加载和管理提示词：

```java
// 获取提示词模板
PromptTemplate template = promptManager.getPromptTemplate("code.class_structure");

// 填充提示词模板
Map<String, String> variables = new HashMap<>();
variables.put("file_path", "path/to/file.java");
variables.put("code", "public class Example { ... }");
String prompt = promptManager.fillPromptTemplate("code.class_structure", variables);

// 使用LLM服务生成响应
String response = llmService.generateResponse(prompt, template.getParameters());
```