# 产品需求文档 (PRD): ArchScope - 架构鹰眼

## 1. 引言

### 1.1 项目概述 (Overview)

架构鹰眼 (ArchScope) 是一个面向开发团队和架构师的自动化架构观测与守护系统。它通过深度分析项目的代码仓库（未来可能扩展至需求与设计文档），为开发者提供项目的实时、全局架构视图。ArchScope 旨在帮助团队快速理解项目结构、有效治理技术复杂度、保障架构一致性，并为项目的相关方（如新成员、外部协作者）提供便捷的对接和理解途径。开发者只需提供项目的仓库URL，系统即可自动分析并生成专属的文档网站 [user clarification]。

### 1.2 项目目标与价值 (Goals and Value Proposition)

* **提升理解效率：** 自动化生成和维护最新的架构文档及可视化视图（如图表），缩短新成员熟悉项目的时间，降低复杂项目的认知负荷。
* **强化架构治理：** （MVP阶段后）通过健康度评估、依赖分析和变更感知，帮助团队识别潜在风险、管理技术债务，确保架构演进的可持续性。
* **促进团队协作：** 提供统一、标准的项目信息源，改善开发、测试、运维团队之间的沟通与协作效率。
* **简化项目对接：** 为外部用户或依赖方提供清晰的API文档、系统概览、错误码指南，加速集成过程。
* **智能化辅助：** 利用抽象语法树（AST）提取代码结构，并结合大语言模型 (LLM) 进行更深层次的代码洞察和文档内容生成 [user clarification, prd3.md]。

### 1.3 用户画像 (User Personas)

* **技术团队管理者 (Tech Lead / Engineering Manager - TLM):**
  * **关注点：** 团队效率、项目整体架构健康度（MVP后）、技术风险、项目进展、资源分配。
  * **需求：** 宏观的项目视图、可量化的健康度报告（MVP后）、技术债务识别、团队成员对项目的理解程度。
* **开发人员 (Developer - DEV):**
  * **关注点：** 具体模块的实现细节、代码依赖关系、API接口定义、快速定位问题、错误排查。
  * **需求：** 清晰的模块结构图、准确的依赖关系、易于查阅的API文档、代码变更对其他部分的影响、明确的错误码和解决方案指南。
* **架构师 (Architect - ARCH):**
  * **关注点：** 架构一致性、技术选型合理性、系统可扩展性、可维护性、潜在的架构风险点、设计模式应用。
  * **需求：** 精确的架构图（如C4模型）、跨模块依赖分析、设计模式识别（MVP后）、自定义架构规则校验（MVP后）。
* **新成员 (New Joiner - NJ):**
  * **关注点：** 快速上手项目、理解项目核心业务流程、找到关键代码和文档。
  * **需求：** 结构化的项目概览、核心模块导览、清晰的入门文档。
* **外部协作者/使用方 (External Collaborator / Consumer - EXT):**
  * **关注点：** 如何与项目集成、API接口定义、系统功能边界。
  * **需求：** 准确、易懂的API文档、系统上下文图。

### 1.4 项目范围 (Scope)

#### 1.4.1 MVP阶段范围内 (In Scope for MVP)

* **用户访问**：用户直接使用所有功能，无须认证。系统将依赖企业统一单点登录(SSO)框架，不设计独立的用户管理系统。
* **代码仓库接入**：支持主流Git仓库 (GitHub, GitLab) 的接入。
* **编程语言支持**：**MVP阶段重点支持Java语言项目**。
* **代码分析**：
  * 采用混合解析策略：AST（如JavaParser）为主，精确提取代码基础结构元素，为LLM提供抽象知识 [user clarification, prd3.md]。
  * LLM（集成OpenRouter API）为辅，进行语义理解增强，并主导生成核心文档内容，如架构设计（可采用C4模型，含组件图）、数据模型（含E-R图、表概要设计）等 [user clarification, prd3.md]。所有图表采用Mermaid格式输出 [user clarification]。
* **文档生成**：自动化生成Markdown格式的架构文档。
* **文档网站**：
  * **项目文档网站的生成与基础托管** (MVP核心特性)。文档网站由后端API将Markdown（含Mermaid图表代码）直接输出为HTML内容到指定目录，或生成静态HTML文件集由Nginx等托管。
  * 提供清晰的导航结构、代码高亮、Mermaid图表前端渲染。
* **文档版本管理**：
  * 生成的文档与代码版本（Commit ID）关联。
  * **支持文档版本切换与内容对比** (MVP核心特性)。内容对比采用diff机制，版本化文档采用全量存储Markdown内容的方式。
* **错误码指南**：提供错误码和解决方案查看页面，针对**被分析的项目中识别出的错误**提供指南。指南内容由ArchScope团队维护，系统负责将识别的错误模式链接到指南条目。MVP阶段可以是一个结构化的静态或半静态帮助文档页面。
* **后台任务管理**：
  * 管理代码解析、文档生成等异步任务。
  * 使用基于RocketMQ的消息队列进行调度，MVP阶段采用FIFO（先进先出）策略。
  * 提供任务状态跟踪API供前端轮询。
* **API接口**：提供RESTful API接口。
* **LLM提示词管理**：MVP阶段LLM的提示词通过配置文件管理 [user clarification, prd3.md]。

#### 1.4.2 MVP阶段范围外 (Out of Scope for MVP)

* **用户账户系统**：任何形式的ArchScope独立用户账户注册、密码管理或用户信息存储系统。
* **项目健康度评估**：项目健康度评估及报告功能（完全排除在MVP之外）。
* **权限控制**：ArchScope系统内部完整的RBAC功能开发（依赖SSO）。
* **代码仓库修改**：直接修改用户代码仓库。
* **IDE集成**：完整的、商业级的IDE集成插件。
* **外部文档解析**：需求文档、设计文档的自动解析与关联（指外部非ArchScope生成的文档）。
* **代码风格检查**：过于细致的实时代码风格检查与修正建议。
* **项目管理工具同步**：项目管理工具的深度双向同步。
* **需求/设计/代码一致性检查**：不在MVP范围内 [user clarification]。
* **自动化变更感知 (FR-CHANGE)**：通过Webhook或定时扫描自动感知代码变更、自动触发解析和通知等功能，均在MVP后续阶段实现。
* **统计与分析功能 (FR-STATS)**：文档访问日志、访问量统计、热门排行、统计报表等，均在MVP后续阶段实现。
* **高级任务管理**：任务优先级调度（除FIFO外）、完善的DLQ处理、管理员干预界面等，均在MVP后续阶段实现。
* **高级搜索功能**：文档网站的全文搜索功能在MVP后续阶段实现。

## 2. 核心功能需求 (Functional Requirements - MVP Focus)

### 2.1 项目管理功能 (FR-PM)

* **FR-PM-001:** 用户可以注册新的代码仓库项目。用户仅需提供仓库URL。系统将尝试从仓库URL自动初始化项目名称、项目描述、分支列表等信息（具体提取字段参考界面原型 `register_project.html`）。如自动提取失败或信息不完整，应提示用户进行手动维护 [user clarification, prd3.md]。 (对应 `register_project.html`)
* **FR-PM-002:** 系统应提供项目列表页，展示项目及其摘要信息（如名称、最后分析时间）。 (对应 `project_list.html`)
* **FR-PM-003:** 用户可以查看特定项目的详细信息页，包括项目元数据、文档入口、最近分析任务等。 (对应 `project_detail.html`)
* **FR-PM-004:** 用户可以修改项目的基本信息和配置（例如，手动维护自动初始化失败的信息）。
* **FR-PM-005:** 用户可以删除项目（需二次确认）。
* **FR-PM-007:** 用户可以手动触发对已注册项目的代码进行分析（MVP阶段为全量分析，或非常简化的增量）。
* **FR-PM-008:** 系统应支持对编程语言的识别和标记。**MVP阶段将重点支持Java语言项目的分析**。

### 2.2 代码仓库解析功能 (FR-CODE)

* **FR-CODE-001:** 系统应能克隆或拉取指定Git仓库的代码（使用系统配置的凭证，凭证需通过AES等强加密方式安全存储在配置文件中）。
* **FR-CODE-002:** 系统应采用混合解析引擎策略：
  * **AST为主：** 利用抽象语法树 (AST) 解析精确提取代码的基础结构元素（MVP阶段针对Java语言，如使用JavaParser），为LLM提供抽象知识 [user clarification, prd3.md]。
  * **LLM为辅：** 利用大语言模型 (LLM)，通过OpenRouter API接入，进行语义理解并主导生成文档内容 [user clarification, prd3.md]。
* **FR-CODE-003:** 解析分析的粒度应支持多个层次。**MVP阶段主要关注基础结构和直接依赖，并提取足够信息供LLM生成指定文档** [user clarification, prd3.md]。
* **FR-CODE-004:** 系统应支持增量解析。**MVP阶段可能只实现全量解析或非常简化的增量**。
* **FR-CODE-005:** 解析结果（代码知识图谱）应存储在图数据库中（如Neo4j）。
* **FR-CODE-006:** 系统应支持多种编程语言的解析。**MVP阶段将重点支持Java语言**。
* **FR-CODE-007 (LLM相关):** LLM的Prompt应可配置管理。**MVP阶段通过配置文件管理系统预设的Prompt** [user clarification, prd3.md]。

### 2.3 文档生成功能 (FR-DOCGEN)

* **FR-DOCGEN-001:** 系统应能根据代码知识图谱的解析结果（**MVP阶段针对Java项目**），利用LLM自动生成Markdown格式的项目架构文档 [user clarification, prd3.md]。
* **FR-DOCGEN-002:** 文档内容应包括：
  * 项目概览、模块列表、核心类说明等（原PRD提及，需确认LLM覆盖范围）。
  * LLM主导生成的架构设计文档（可采用C4模型，包含组件图） [user clarification]。
  * LLM主导生成的数据模型文档（包含E-R图、每个表的概要设计） [user clarification]。
  * (用户已确认移除FR-DOCGEN-002中“较基础”的描述) [user clarification]。
* **FR-DOCGEN-003:** 系统应支持文档模板定制。**MVP阶段提供1-2个针对Java项目的固定Thymeleaf模板**，用于将Markdown内容渲染为HTML的骨架结构。
* **FR-DOCGEN-004:** 系统应支持生成架构图。**MVP阶段由LLM生成Mermaid格式的组件图和E-R图** [user clarification]。前端负责渲染这些Mermaid图表 [user clarification, prd3.md]。
* **FR-DOCGEN-005:** 生成的文档应与代码版本（Commit ID）关联，实现文档的版本管理。**MVP阶段包含基础的文档版本管理功能，采用全量存储Markdown文档的方式**。

### 2.4 文档网站生成和托管功能 (FR-DOCSITE)

* **FR-DOCSITE-001:** 系统应能将生成的Markdown文档集合自动渲染成一个独立的、可在线访问的静态HTML文档网站。**(MVP核心特性)** 可以是后端API直接输出HTML到指定目录，或者生成静态HTML文件集后由Nginx等Web服务器托管。
* **FR-DOCSITE-002:** 文档网站应提供清晰的导航结构（如左侧导航树，对应`DocTreeNodeDTO`），方便用户浏览不同章节。**(MVP核心特性)** (参照 `project_doc_home.html` 等文档页面的布局)。
* **FR-DOCSITE-003:** 文档网站应支持版本切换功能，允许用户选择查看不同代码版本（Commit ID）对应的文档。**(MVP核心特性)**。
* **FR-DOCSITE-004:** 文档网站应支持文档内容对比功能，允许用户选择两个文档版本进行差异比较（基于diff机制），高亮显示增删改的内容。**(MVP核心特性)** (对应 `project_doc_compare.html`)。
* **FR-DOCSITE-005:** 文档网站应提供基本的全文搜索功能。**(MVP后实现)**。
* **FR-DOCSITE-006:** 文档网站应支持代码高亮、Mermaid图表渲染。**(MVP支持代码高亮和前端Mermaid图表渲染)**。
* **FR-DOCSITE-007 (UI/UX):** 界面应简洁直观，符合开发者习惯，响应式设计支持桌面和移动端访问。
* **FR-DOCSITE-008:** 系统应提供一个专门的页面或区域，用于展示常见的系统错误码、错误信息描述及其推荐的解决方案或排查步骤。此页面针对**被分析的项目中识别出的错误**提供指南链接或展示。指南内容由ArchScope团队维护和更新（MVP阶段可以是一个结构化的静态或半静态帮助文档页面）。(概念上对应 `docs/prototype/error_codes_guide.html`)。

### 2.5 后台任务管理与调度功能 (FR-TASK)

* **FR-TASK-001:** 系统应提供后台任务管理功能，用于管理所有异步执行的任务，如代码解析、文档生成等。(对应 `task_queue.html`, `task_detail.html`，但MVP阶段UI可能简化)。
* **FR-TASK-002:** 后台任务应使用基于RocketMQ的消息队列进行调度。
* **FR-TASK-003:** 任务调度。**MVP阶段采用FIFO（先进先出）策略**。基于应用层定义的优先级（如项目评分）的调度在MVP后实现。
* **FR-TASK-004:** 系统应提供任务状态跟踪功能，用户可以查看任务的当前状态（如排队中、运行中、成功、失败）、创建时间、开始/结束时间、执行日志摘要。**(MVP核心特性，主要通过API轮询，`project_detail.html`中展示，`task_queue.html`可能简化)**。
* **FR-TASK-005:** 系统应为失败的任务提供自动重试机制。**MVP阶段简化重试逻辑**（可利用RocketMQ的消费者重试机制）。
* **FR-TASK-006:** 对于多次重试后仍失败的任务，应将其移至死信队列 (DLQ)。**MVP阶段简化DLQ处理**。
* **FR-TASK-007:** 管理员应能通过管理界面查看任务队列，并对特定任务进行操作。**(MVP后实现)**。

### 2.6 其他功能模块 (MVP范围外)
* **版本变更感知功能 (FR-CHANGE):** 全部功能 (Webhook, 定时扫描, 自动触发增量解析和文档更新, 通知) 均在MVP后实现。
* **项目健康度评估功能 (FR-HEALTH):** 全部功能均在MVP范围之外。
* **统计与分析功能 (FR-STATS):** 全部功能均在MVP后实现。

## 3. 非功能性需求 (Non-Functional Requirements - MVP Focus)

### 3.1 安全性 (NFR-SEC)

* **NFR-SEC-001 (数据保护):** 敏感数据（如Git仓库访问凭证、外部API Keys如OpenRouter密钥、数据库密码）必须加密存储（如使用AES-256），密钥通过安全机制管理。Git凭证保存在配置文件中，采用AES加密。**(MVP核心)**。

### 3.2 可集成性 (NFR-INTEGRATE)

* **NFR-INTEGRATE-001 (Git仓库):** 与GitHub/GitLab API稳定集成，支持Token认证、代码拉取。
* **NFR-INTEGRATE-002 (LLM服务):** 支持与LLM服务API（MVP阶段为OpenRouter）集成，并设计可插拔的LLM适配器层。

### 3.3 测试策略 (NFR-TEST)

* **NFR-TEST-001 (多层次测试):** 实施单元测试、组件测试、集成测试、端到端(E2E)测试。
* **NFR-TEST-002 (自动化):** 核心功能和回归场景的测试应高度自动化，并集成到CI/CD流水线。
* **NFR-TEST-003 (专项测试):** （MVP后）定期进行性能测试、安全测试。
* **NFR-TEST-004 (UAT):** （MVP后）在版本发布前，邀请目标用户进行用户验收测试。
  * *MVP关注点:* 核心功能的单元测试和关键流程的集成测试。

### 3.4 性能与并发 (NFR-PERF, NFR-SCALE)
* MVP阶段暂不设定具体的代码解析性能指标或高并发用户处理能力指标，但系统应保证核心功能的稳定可用 [user clarification, prd3.md]。

### 3.5 部署 (NFR-DEPLOY)
* **NFR-DEPLOY-003 (本地开发与测试):** 提供Docker Compose配置，方便本地开发和测试环境一键部署所有后端服务（Java应用, MySQL, Redis, Neo4j, RocketMQ）。

## 4. 界面原型参考

详细的界面原型请参考项目仓库中的 `docs/prototype/` 目录。该目录包含以下HTML原型文件：

* **核心页面:** `project_list.html`, `project_detail.html`, `register_project.html` (注意：注册逻辑已根据讨论简化为仅URL输入，后续自动初始化), `task_queue.html` (MVP阶段此页面可能简化或主要通过项目详情查看任务状态)。
* **文档页面:** `project_doc_home.html`, `project_doc_architecture.html`, `project_doc_api.html`, `project_doc_compare.html` (MVP核心)。 ( `project_doc_extension.html`, `project_doc_user_manual.html`, `project_doc_llms_txt.html` 也是原型的一部分，其内容生成和具体程度需结合LLM能力确定)。
* **管理页面:** `task_detail.html` (MVP阶段任务详情可能主要通过API轮询体现在项目详情页，完整UI页面MVP后)。
* **新增概念原型:** `docs/prototype/error_codes_guide.html` (错误码与解决方案查看页面)。

相关分析文档参见: `architecture/ui-prototype-analysis.md`。

## 5. API 规范参考

详细的API接口规范请参考项目仓库中的 `api-spec.yaml` (OpenAPI v3 YAML格式)。
该文件定义了所有外部API的端点、请求/响应格式、数据传输对象 (DTOs)。
包括用于获取错误码信息的 `/system/error-codes` (GET) 端点 (如果错误码页面是动态获取数据)。

## 6. 技术架构初步设想 (Initial Technical Architecture)

* **前端 (Frontend):** Vue 3 + TypeScript + Tailwind CSS (响应式设计)。
* **后端 (Backend):** Java (JDK 17+) + Spring Boot 3.x + MyBatisPlus (遵从DDD四层架构)。
* **数据库 (Database):**
  * 关系型数据: MySQL 8.0+ (存储项目元数据、任务、配置等；不存在独立用户表)。
  * 缓存/计数: Redis 7.x。
  * 图数据: Neo4j 5.x (或兼容的图数据库，存储代码知识图谱)。
* **解析引擎 (Parsing Engine):**
  * 混合解析引擎:
    * AST解析器: **MVP阶段重点支持Java语言 (如使用JavaParser)，提取抽象知识**。
    * LLM集成: **MVP阶段对接OpenRouter API，用于主导文档内容（含Mermaid图表）生成**。
* **任务队列 (Task Queue):** Apache RocketMQ 5.x。
* **文档网站生成器 (Documentation Site Generator):** 后端基于Thymeleaf模板引擎将LLM生成的Markdown（含Mermaid代码）渲染为HTML字符串由后端API提供，或生成静态HTML文件集由Nginx等托管。前端负责Mermaid图表的实际渲染。
* **构建工具:** Maven (后端), Vite/Webpack (前端)。
* **部署:** Docker。MVP阶段提供Docker Compose配置支持本地一键部署。Kubernetes (MVP后)。

## 7. 开发路线图 (Development Roadmap - MVP Phase)

### 7.1 Phase 1: MVP - 核心基础与文档生成、查看及版本管理

* **目标:** 验证核心流程：用户打开网站 -> 注册Java项目 (仅提供URL，系统自动初始化) -> 手动触发全量解析（AST为主提取抽象知识，针对Java；LLM主导生成架构、数据模型等Markdown文档及Mermaid图表） -> 用户可查看生成的、包含基础导航和Mermaid图表的最新版Java项目文档网站，能够进行版本切换与内容对比，并能查阅针对被分析项目错误的指南页面。
* **主要功能 (MVP):**
  * FR-PM-001 (注册Java项目，URL输入，自动初始化，手动维护接口) [user clarification, prd3.md], FR-PM-002 (项目列表核心字段), FR-PM-007 (手动全量触发解析，**仅针对Java项目**)。
  * FR-CODE-001 (拉取Java代码), FR-CODE-002 (**AST为主，针对Java语言，如使用JavaParser，提取抽象知识；LLM通过OpenRouter API主导文档生成**) [user clarification, prd3.md], FR-CODE-005 (基础图谱存储，如类和方法节点及简单关系), FR-CODE-007 (Prompt通过配置文件管理) [user clarification, prd3.md]。
  * FR-DOCGEN-001 (LLM基于AST结果生成Markdown) [user clarification, prd3.md], FR-DOCGEN-002 (文档内容：项目概览、模块列表、核心类说明，LLM生成C4架构含组件图、数据模型含ER图和表概要) [user clarification, prd3.md], FR-DOCGEN-003 (1-2个固定Thymeleaf模板，**针对Java项目结构**), FR-DOCGEN-004 (LLM生成Mermaid图表文本) [user clarification], FR-DOCGEN-005 (文档与Commit ID关联，全量存储Markdown进行版本管理) [user clarification, prd3.md]。
  * FR-DOCSITE-001 (Markdown转HTML并生成可在线访问的静态文档网站 - **核心交付物**), FR-DOCSITE-002 (基于解析结果生成基础的文档导航树), FR-DOCSITE-003 (文档版本切换功能 - **MVP核心特性**), FR-DOCSITE-004 (文档内容对比功能，diff机制 - **MVP核心特性**), FR-DOCSITE-006 (Java代码片段在文档中正确高亮，前端渲染Mermaid图表), **FR-DOCSITE-008 (针对被分析项目错误的指南查看页面 - 可以是系统内嵌的静态帮助文档页面，由ArchScope团队维护内容，系统负责链接)**。
  * FR-TASK-001 (后台任务抽象), FR-TASK-002 (RocketMQ基础集成), FR-TASK-003 (FIFO调度) [user clarification, prd3.md], FR-TASK-004 (提供API供前端轮询任务状态), FR-TASK-005 (极简化的任务失败记录和重试)。
  * NFR-SEC-001 (敏感信息加密), NFR-DEPLOY-003 (Docker Compose本地部署)。
  * 核心技术栈搭建 (Java/Spring Boot, Vue/TS, MySQL, Redis, RocketMQ基础集成, Neo4j基础集成, OpenRouter API集成)。
  * API规范 (`api-spec.yaml`) MVP部分的实现。
* **明确排除在MVP之外的（部分调整）：**
  * 独立的ArchScope用户注册、登录、密码管理、RBAC功能。
  * 除Java以外的其他编程语言支持。
  * 复杂的LLM微调或本地LLM部署选项。
  * 完整的增量解析与上下文维护（MVP可能只做全量或非常简化的增量）。
  * 文档网站高级搜索功能。
  * **完整的项目健康度评估体系和自定义规则（完全排除在MVP之外）**。
  * Webhook自动触发和定时扫描功能 (FR-CHANGE)。
  * 完善的统计分析功能 (FR-STATS)。
  * 严格的NFR指标达成（如高可用性），MVP关注核心功能可用和基本稳定。
  * 任务调度的优先级处理（FIFO之外）、完善的DLQ和管理员干预界面 (FR-TASK高级功能)。

## 8. 附录 (Appendix)

* **API设计草案:** 见 `api_design.md` (初始草案)
* **API详细规范:** 见 `api-spec.yaml` (OpenAPI v3 YAML)
* **LLM Prompts示例与指南:** (MVP阶段存于配置文件) `prompts/llm_prompts.md` (待创建，包含针对不同分析任务的Prompt设计原则和示例)
* **界面原型:** 见 `docs/prototype/` 目录 (包含新增的 `error_codes_guide.html` 概念)
* **领域模型图:** (待创建，基于 `domain-model.md` 的可视化) `docs/architecture/domain-model.png`
* **用户故事:** (待创建) `user-story.md`