<context>
# Overview  
ArchScope (架构鹰眼) 是一个面向开发团队和架构师的自动化架构观测与守护系统。它通过深度分析项目的代码仓库，为开发者提供项目的实时、全局架构视图。ArchScope 旨在帮助团队快速理解项目结构、有效治理技术复杂度、保障架构一致性，并为项目的相关方（如新成员、外部协作者）提供便捷的对接和理解途径。

系统完全依赖企业统一单点登录(SSO)框架，不设计独立的用户管理系统。MVP阶段重点支持Java语言项目，包含文档版本管理功能，但完全排除健康度评估功能。

# Core Features
## 1. 项目管理与代码解析
- **功能**: 注册Git仓库项目，自动化代码结构和依赖分析
- **重要性**: 为架构文档生成提供数据基础
- **实现**: AST解析为主(MVP阶段针对Java)，LLM辅助增强，存储到图数据库

## 2. 文档自动生成与网站托管
- **功能**: 基于代码分析结果自动生成Markdown架构文档并托管为网站
- **重要性**: 提供统一、标准的项目信息源，改善团队协作效率
- **实现**: Thymeleaf模板引擎生成，支持导航结构和代码高亮

## 3. 文档版本管理与对比 (MVP核心特性)
- **功能**: 支持文档版本切换和内容对比
- **重要性**: 跟踪项目演进历史，便于理解架构变化
- **实现**: 与代码版本(Commit ID)关联，提供差异比较界面

## 4. 后台任务管理
- **功能**: 管理代码解析、文档生成等异步任务
- **重要性**: 确保系统稳定性和用户体验
- **实现**: 基于RocketMQ的消息队列调度

# User Experience  
## 用户画像
- **技术团队管理者**: 关注团队效率、项目整体架构、技术风险
- **开发人员**: 关注模块实现细节、代码依赖关系、API接口定义
- **架构师**: 关注架构一致性、技术选型合理性、系统可扩展性
- **新成员**: 关注快速上手项目、理解核心业务流程
- **外部协作者**: 关注如何与项目集成、API接口定义

## 关键用户流程
1. **打开网站** → 访问项目列表 → 选择项目 → 查看文档
2. **项目注册** → 配置仓库信息 → 触发解析 → 生成文档网站
3. **文档浏览** → 版本切换 → 内容对比 → 导出分享

## UI/UX考虑
- 简洁直观的界面设计，符合开发者习惯
- 响应式设计支持桌面和移动端访问
- 清晰的导航结构和搜索功能
</context>
<PRD>
# Technical Architecture  
## 系统组件
- **前端**: Vue 3 + TypeScript + Tailwind CSS
- **后端**: Java (JDK 17+) + Spring Boot 3.x + MyBatisPlus (DDD四层架构)
- **数据库**: 
  - MySQL 8.0+ (项目元数据、任务、配置等；无独立用户表)
  - Redis 7.x (缓存/计数)
  - Neo4j 5.x (代码知识图谱)
- **解析引擎**: JavaParser (MVP阶段)，LLM集成简化
- **任务队列**: Apache RocketMQ 5.x
- **文档生成**: Thymeleaf模板引擎

## 数据模型
- **项目实体**: 项目元数据、仓库信息、配置
- **代码图谱**: 类、方法、依赖关系节点
- **文档版本**: 文档内容与代码版本关联
- **任务实体**: 异步任务状态和日志

## APIs和集成
- **Git仓库集成**: GitHub/GitLab API，Token认证
- **RESTful API**: 项目管理、文档访问、任务状态查询

## 基础设施要求
- **容器化**: Docker部署
- **本地开发**: Docker Compose一键启动
- **生产环境**: 预留Kubernetes支持(MVP后)

# Development Roadmap  
## Phase 1: 核心基础与手动MVP
**目标**: 验证核心流程 → 注册Java项目 → 手动解析 → 生成文档网站 → 版本管理

**核心功能**:
- Java项目注册和管理
- AST为主的Java代码解析，基础图谱存储
- Markdown文档生成，固定模板
- 文档网站生成与托管，支持导航和代码高亮
- 文档版本切换和内容对比功能
- 基础错误码查看页面
- 后台任务管理，RocketMQ集成，API轮询状态
- Docker Compose本地开发环境

**排除功能**:
- 独立用户管理系统
- 非Java语言支持
- 复杂LLM集成
- 项目健康度评估
- 高级搜索功能
- 复杂图表生成

## Phase 2: 自动化流程与文档站增强
**目标**: 实现自动化触发和文档站功能增强

**主要功能**:
- Webhook接收，自动触发增量解析
- 基础C4图PlantUML生成
- 文档搜索功能
- 完善任务重试和DLQ处理
- Kubernetes部署支持

## Phase 3: 功能增强与健康度评估
**目标**: 引入健康度评估，丰富解析能力

**主要功能**:
- LLM深度集成(设计模式、代码异味)
- 多语言支持扩展
- 基础健康度指标计算与报告
- 任务管理UI界面
- 安全性增强

## Phase 4: 成熟化与规模化
**目标**: 系统功能完善，性能优化

**主要功能**:
- 自定义健康度规则
- 统计分析功能
- 本地LLM部署选项
- 性能和安全性优化
- 外部API集成

# Logical Dependency Chain
## 基础设施层 (优先级1)
1. **数据库设计** - MySQL/Redis/Neo4j基础架构

## 核心业务层 (优先级2)
2. **项目管理模块** - 项目注册、列表、详情
3. **Git仓库集成** - 代码拉取和版本管理
4. **Java AST解析引擎** - 代码分析核心

## 文档生成层 (优先级3)
5. **文档生成模块** - Markdown生成和模板系统
6. **文档网站托管** - 静态网站生成和访问
7. **版本管理功能** - 文档版本切换和对比

## 任务调度层 (优先级4)
8. **RocketMQ集成** - 异步任务基础
9. **任务管理模块** - 任务状态跟踪和API

## 前端界面层 (优先级5)
10. **Vue前端框架** - 用户界面基础
11. **项目管理界面** - 注册、列表、详情页面
12. **文档浏览界面** - 文档展示、导航、版本切换

# Risks and Mitigations  
## 技术挑战
**风险**: Java AST解析复杂度和性能问题
**缓解**: 从简单项目开始，逐步优化解析算法，设置合理的超时机制

**风险**: 文档版本管理的存储和性能开销
**缓解**: 采用增量存储策略，只保存差异部分，设置版本保留策略

## MVP范围控制
**风险**: 功能范围蔓延，影响MVP交付
**缓解**: 严格按照PRD排除清单，健康度评估等功能坚决不在MVP实现

## 资源约束
**风险**: 开发资源不足，无法按计划交付
**缓解**: 采用敏捷开发，优先实现核心用户流程，非核心功能可延后

**风险**: 基础设施复杂度
**缓解**: 使用Docker简化部署，云服务降低运维复杂度

# Appendix  
## 技术规范
- **API规范**: 见 `api-spec.yaml` (OpenAPI v3)
- **界面原型**: 见 `docs/prototype/` 目录
- **架构文档**: 见 `architecture.md`
- API设计规范见 `/docs/api_design.md`
- LLM Prompts见 `/prompts/llm_prompt.md`

## 研究发现
- Java项目AST解析工具调研结果
- 文档版本管理技术方案对比

## 质量标准
- **代码覆盖率**: 核心模块 > 80%
</PRD>
