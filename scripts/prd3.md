# 产品需求文档 (PRD): ArchScope - 架构鹰眼

## 1. 引言

### 1.1 项目概述 (Overview)

架构鹰眼 (ArchScope) 是一个面向开发团队和架构师的自动化架构观测与守护系统。它通过深度分析项目的设计文档（未来）和代码仓库，为开发者提供项目的实时、全局架构视图。ArchScope 旨在帮助团队快速理解项目结构、有效治理技术复杂度、保障架构一致性，并为项目的相关方（如新成员、外部协作者）提供便捷的对接和理解途径。

### 1.2 项目目标与价值 (Goals and Value Proposition)

* **提升理解效率：** 自动化生成和维护最新的架构文档及可视化视图，缩短新成员熟悉项目的时间，降低复杂项目的认知负荷。
* **强化架构治理：** 通过健康度评估、依赖分析和变更感知，帮助团队识别潜在风险、管理技术债务，确保架构演进的可持续性。
* **促进团队协作：** 提供统一、标准的项目信息源，改善开发、测试、运维团队之间的沟通与协作效率。
* **简化项目对接：** 为外部用户或依赖方提供清晰的API文档、系统概览、错误码指南，加速集成过程。
* **智能化辅助：** 利用大语言模型 (LLM) 和静态分析技术，提供更深层次的代码洞察和初步的智能建议。

### 1.3 用户画像 (User Personas)

* **技术团队管理者 (Tech Lead / Engineering Manager - TLM):**
    * **关注点：** 团队效率、项目整体架构健康度、技术风险、项目进展、资源分配。
    * **需求：** 宏观的项目视图、可量化的健康度报告、技术债务识别、团队成员对项目的理解程度。
* **开发人员 (Developer - DEV):**
    * **关注点：** 具体模块的实现细节、代码依赖关系、API接口定义、快速定位问题、错误排查。
    * **需求：** 清晰的模块结构图、准确的依赖关系、易于查阅的API文档、代码变更对其他部分的影响、明确的错误码和解决方案指南。
* **架构师 (Architect - ARCH):**
    * **关注点：** 架构一致性、技术选型合理性、系统可扩展性、可维护性、潜在的架构风险点、设计模式应用。
    * **需求：** 精确的架构图（如C4模型）、跨模块依赖分析、设计模式识别、自定义架构规则校验。
* **新成员 (New Joiner - NJ):**
    * **关注点：** 快速上手项目、理解项目核心业务流程、找到关键代码和文档。
    * **需求：** 结构化的项目概览、核心模块导览、清晰的入门文档。
* **外部协作者/使用方 (External Collaborator / Consumer - EXT):**
    * **关注点：** 如何与项目集成、API接口定义、系统功能边界。
    * **需求：** 准确、易懂的API文档、系统上下文图。

### 1.4 项目范围 (Scope)

ArchScope 系统将提供从项目注册、代码分析、文档生成与网站托管到任务管理的全套功能。

* **范围内 (In Scope):**
    * 用户直接使用所有功能，无须认证。
    * 支持主流Git仓库 (GitHub, GitLab) 的接入。
    * **MVP阶段重点支持Java语言项目**，后续扩展其他语言。
    * 自动化代码结构和依赖分析 (AST + LLM 混合)。
    * 自动化生成Markdown格式的架构文档及部分图表 (如基于PlantUML的C4模型图)。
    * **项目文档网站的生成与基础托管 (MVP核心特性)。**
    * **文档版本管理与内容对比 (MVP核心特性)。**
    * 提供错误码和解决方案查看页面。
    * 后台任务管理与调度。
    * RESTful API 接口。
* **范围外 (Out of Scope for initial phases, but potential future enhancements):**
    * **任何形式的独立用户账户注册、密码管理或用户信息存储系统。**
    * **项目健康度评估及报告功能（完全排除在MVP之外）。**
    * ArchScope系统内部完整的RBAC功能开发。
    * 直接修改用户代码仓库。
    * 完整的、商业级的IDE集成插件。
    * 需求文档、设计文档的自动解析与关联。
    * 过于细致的实时代码风格检查与修正建议。
    * 项目管理工具的深度双向同步。

## 2. 核心功能需求 (Functional Requirements)

### 2.2 项目管理功能 (FR-PM)
* **FR-PM-001:** 用户可以注册新的代码仓库项目，需提供项目名称、仓库URL、仓库类型 (GitHub/GitLab/Bitbucket/Other)、默认分支等信息。 (对应 `register_project.html`)
* **FR-PM-002:** 系统应提供项目列表页，展示项目及其摘要信息（如名称、健康度星级（MVP后）、最后分析时间）。(对应 `project_list.html`)
* **FR-PM-003:** 用户可以查看其有权访问的特定项目的详细信息页，包括项目元数据、文档入口、最新健康度摘要（MVP后）、最近分析任务等。(对应 `project_detail.html`)
* **FR-PM-004:** 用户可以修改项目的基本信息和配置。
* **FR-PM-005:** 用户可以删除项目（需二次确认）。
* **FR-PM-007:** 用户可以手动触发对已注册项目的代码进行分析（全量或增量）。
* **FR-PM-008:** 系统应支持对编程语言的识别和标记。**MVP阶段将重点支持Java语言项目的分析。** 后续版本将扩展支持Python, JavaScript/TypeScript等其他主流语言。

### 2.3 代码仓库解析功能 (FR-CODE)
* **FR-CODE-001:** 系统应能克隆或拉取指定Git仓库的代码（使用系统配置的凭证，需安全存储）。
* **FR-CODE-002:** 系统应采用混合解析引擎策略：
    * **AST为主：** 利用抽象语法树 (AST) 解析精确提取代码的基础结构元素。**MVP阶段针对Java语言。**
    * **LLM为辅：** 利用大语言模型 (LLM) 进行语义理解增强。**MVP阶段LLM应用将非常简化或暂不启用深度分析。**
* **FR-CODE-003:** 解析分析的粒度应支持多个层次（结构、依赖、语义）。**MVP阶段主要关注基础结构和直接依赖。**
* **FR-CODE-004:** 系统应支持增量解析。**MVP阶段可能只实现全量解析或非常简化的增量。**
* **FR-CODE-005:** 解析结果应存储在图数据库中，形成代码知识图谱。
* **FR-CODE-006:** 系统应支持多种编程语言的解析。**MVP阶段将重点支持Java语言。** 后续可扩展支持更多语言。
* **FR-CODE-007 (LLM相关):** LLM的Prompt应可配置管理。**MVP阶段由系统预设。** (参照 `project_doc_llms_txt.html` 的理念，但非用户直接编辑的llms.txt文件，而是系统内部的Prompt管理)

### 2.4 文档生成功能 (FR-DOCGEN)
* **FR-DOCGEN-001:** 系统应能根据代码知识图谱的解析结果（**MVP阶段针对Java项目**），自动生成Markdown格式的项目架构文档。
* **FR-DOCGEN-002:** 文档内容应包括项目概览、模块列表、核心类说明等。**MVP阶段内容较基础。**
* **FR-DOCGEN-003:** 系统应支持文档模板定制。**MVP阶段提供1-2个针对Java项目的固定Thymeleaf模板。**
* **FR-DOCGEN-004:** 系统应支持生成架构图。**MVP阶段暂不生成复杂架构图 (如C4 PlantUML)，或只生成非常基础的模块依赖文字描述。**
* **FR-DOCGEN-005:** 生成的文档应与代码版本（Commit ID）关联，实现文档的版本管理（**MVP阶段包含基础的文档版本管理功能**）。

### 2.5 版本变更感知功能 (FR-CHANGE)
* **FR-CHANGE-001:** 系统应支持通过Webhook（GitHub, GitLab等）实时感知代码仓库的变更（如push, merge）。**(MVP后实现)**
* **FR-CHANGE-002:** 系统应支持通过定时扫描代码仓库（如每日一次）作为Webhook的补充或备用机制，来发现代码变更。**(MVP后实现)**
* **FR-CHANGE-003:** 当检测到代码变更时，系统应自动触发增量代码解析任务和受影响文档的更新任务。**(MVP后实现)**
* **FR-CHANGE-004:** 系统应能提供代码变更和文档更新的通知（如邮件通知、系统内通知给项目关注者）。**(MVP后实现)**

### 2.6 项目健康度评估功能 (FR-HEALTH)
* **(FR-HEALTH系列功能完全排除在MVP之外，将在后续阶段逐步实现)**
* **FR-HEALTH-001:** 定义一套可量化的项目健康度评估指标，包括但不限于：
    * **代码质量与复杂度：** 模块/组件平均圈复杂度、传入/传出依赖数量、平均类/函数代码行数、继承深度、LCOM、循环依赖。
    * **代码异味（LLM辅助）：** 长方法、大类、上帝类、特征依恋等。
    * **文档与注释：** 公开API/方法注释覆盖率、注释质量初步评估（LLM辅助）。
    * **测试（通过外部集成）：** 测试覆盖率（行、分支）。
    * **依赖管理（通过外部集成或解析）：** 依赖库更新频率、过期依赖数量、存在已知漏洞的依赖数量。
    * **变更活跃度：** 代码变动频率（Churn）。
* **FR-HEALTH-002:** 系统应实现一个星级评定算法（如1-5星），综合多个指标及其权重，给出项目的总体健康度评分。
* **FR-HEALTH-003:** 系统应能生成项目健康度报告，展示总体评分、各指标的当前值、趋势（可选）和相关的改进建议。
* **FR-HEALTH-004:** 用户应能（在一定程度上）自定义健康度评估规则，例如：
    * 选择关心的指标。
    * 调整指标的权重。
    * 设置指标的健康/警告/危险阈值。
* **FR-HEALTH-005:** 系统应支持集成静态安全分析工具 (SAST) 的结果，或利用LLM辅助进行代码安全漏洞的初步扫描和识别（如SQL注入、XSS等常见漏洞模式）。LLM主要作为SAST的补充和报警解释。

### 2.7 文档网站生成和托管功能 (FR-DOCSITE)
* **FR-DOCSITE-001:** 系统应能将生成的Markdown文档集合自动渲染成一个独立的、可在线访问的静态HTML文档网站。**(MVP核心特性)**
* **FR-DOCSITE-002:** 文档网站应提供清晰的导航结构（如左侧导航树，对应`DocTreeNodeDTO`），方便用户浏览不同章节。**(MVP核心特性)** (参照 `project_doc_home.html` 等文档页面的布局)
* **FR-DOCSITE-003:** 文档网站应支持版本切换功能，允许用户选择查看不同代码版本（Commit ID）对应的文档。**(MVP核心特性)**
* **FR-DOCSITE-004:** 文档网站应支持文档内容对比功能，允许用户选择两个文档版本进行差异比较，高亮显示增删改的内容。**(MVP核心特性)** (对应 `project_doc_compare.html`)
* **FR-DOCSITE-005:** 文档网站应提供基本的全文搜索功能，帮助用户快速找到包含特定关键词的文档页面。**(MVP后实现)**
* **FR-DOCSITE-006:** 文档网站应支持代码高亮、Mermaid/PlantUML图表渲染（若图表生成）。**(MVP支持代码高亮，图表渲染视FR-DOCGEN-004在MVP的实现程度)**
* **FR-DOCSITE-007 (UI/UX):** 界面应简洁直观，符合开发者习惯，响应式设计支持桌面和移动端访问。
* **FR-DOCSITE-008:** 系统应提供一个专门的页面或区域，用于展示常见的系统错误码、错误信息描述及其推荐的解决方案或排查步骤。此页面内容由ArchScope团队维护和更新（**MVP阶段可以是一个结构化的静态或半静态帮助文档页面**）。(概念上对应 `docs/prototype/error_codes_guide.html`)

### 2.8 任务管理与调度功能 (FR-TASK)
* **FR-TASK-001:** 系统应提供后台任务管理功能，用于管理所有异步执行的任务，如代码解析、文档生成、健康度评估等。 (对应 `task_queue.html`, `task_detail.html`)
* **FR-TASK-002:** 后台任务应使用基于RocketMQ的消息队列进行调度。
* **FR-TASK-003:** 任务调度应支持基于应用层定义的优先级。**(MVP阶段简化，先进先出为主)**
* **FR-TASK-004:** 系统应提供任务状态跟踪功能，用户可以查看任务的当前状态（如排队中、运行中、成功、失败）、创建时间、开始/结束时间、执行日志摘要。**(MVP核心特性，通过API轮询)**
* **FR-TASK-005:** 系统应为失败的任务提供自动重试机制（针对可重试错误），并支持配置最大重试次数和重试间隔（可利用RocketMQ的延迟消息和消费者重试）。**(MVP阶段简化重试)**
* **FR-TASK-006:** 对于多次重试后仍失败的任务，应将其移至死信队列 (DLQ)，并通知管理员进行人工干预。**(MVP阶段简化)**
* **FR-TASK-007:** 管理员应能通过管理界面查看任务队列，并对特定任务进行操作（如手动重试、取消正在排队或运行中的任务）。**(MVP后实现)**

### 2.9 统计与分析功能 (FR-STATS)
* **(FR-STATS系列功能均在MVP阶段后逐步实现)**
* **FR-STATS-001:** 系统应记录文档网站的访问日志。
* **FR-STATS-002:** 系统应提供项目文档的访问量统计功能。
* **FR-STATS-003:** 系统应能分析并展示热门文档和热门项目。
* **FR-STATS-004:** 系统应能生成基本的统计报表（如项目活跃度、文档使用趋势）。 (对应 `project_detail.html` 中的统计信息部分)

## 3. 非功能性需求 (Non-Functional Requirements - NFRs)

### 3.1 安全性 (NFR-SEC)
* **NFR-SEC-001 (数据保护):**
    * 敏感数据（如Git仓库访问凭证、外部API Keys、数据库密码）必须加密存储（如使用AES-256），密钥通过安全机制管理。 (MVP核心)

### 3.2 可集成性 (NFR-INTEGRATE)
* **NFR-INTEGRATE-001 (Git仓库):** 与GitHub/GitLab API稳定集成，支持Token认证、代码拉取。
* **NFR-INTEGRATE-002 (LLM服务):** 支持与主流LLM服务API集成，并设计可插拔的LLM适配器层。**MVP阶段LLM集成简化。**

### 3.3 测试策略 (NFR-TEST)
* **NFR-TEST-001 (多层次测试):** 实施单元测试、组件测试、集成测试、端到端(E2E)测试。
* **NFR-TEST-002 (自动化):** 核心功能和回归场景的测试应高度自动化，并集成到CI/CD流水线。
* **NFR-TEST-003 (专项测试):** 定期进行性能测试、安全测试。
* **NFR-TEST-004 (UAT):** 在版本发布前，邀请目标用户进行用户验收测试。
    * *MVP关注点:* 核心功能的单元测试和关键流程的集成测试。

## 4. 界面原型参考

详细的界面原型请参考项目仓库中的 `docs/prototype/` 目录。该目录包含以下HTML原型文件：

* **核心页面:** `project_list.html`, `project_detail.html`, `register_project.html`, `task_queue.html` (MVP阶段`task_queue.html`可能简化或主要通过项目详情查看任务)
* **文档页面:** `project_doc_home.html`, `project_doc_architecture.html`, `project_doc_extension.html` (MVP后), `project_doc_user_manual.html`, `project_doc_api.html`, `project_doc_llms_txt.html` (概念页，非用户编辑文件), `project_doc_compare.html` (MVP后)
* **管理页面:** `task_detail.html` (MVP阶段通过API轮询，完整UI页面MVP后)
* **新增概念原型:** `docs/prototype/error_codes_guide.html` (错误码与解决方案查看页面)

相关分析文档参见: `architecture/ui-prototype-analysis.md`

## 5. API 规范参考

详细的API接口规范请参考项目仓库中的 `api-spec.yaml` (OpenAPI v3 YAML格式)。
该文件定义了所有外部API的端点、请求/响应格式、数据传输对象 (DTOs)。
新增 `/system/error-codes` (GET) 端点用于获取错误码信息（如果错误码页面是动态获取数据，否则此API在MVP阶段可能不是必需的，页面内容可硬编码或从静态文件加载）。

## 6. 技术架构初步设想 (Initial Technical Architecture)

* **前端 (Frontend):** Vue 3 + TypeScript + Tailwind CSS (响应式设计)
* **后端 (Backend):** Java (JDK 17+) + Spring Boot 3.x + MyBatisPlus (遵从DDD四层架构)
* **数据库 (Database):**
    * 关系型数据: MySQL 8.0+ (存储项目元数据、任务、配置等；不存在用户表)
    * 缓存/计数: Redis 7.x
    * 图数据: Neo4j 5.x (或兼容的图数据库，存储代码知识图谱)
* **解析引擎 (Parsing Engine):**
    * 混合解析引擎:
        * AST解析器: **MVP阶段重点支持Java语言 (如使用JavaParser)。**
        * LLM集成: **MVP阶段极简化或暂不包含深度LLM分析功能。**
* **任务队列 (Task Queue):** Apache RocketMQ 5.x
* **文档网站生成器 (Documentation Site Generator):** 后端基于Thymeleaf模板引擎生成Markdown，**MVP阶段可以直接将Markdown渲染为HTML字符串由后端API提供，或生成静态HTML文件集由Nginx等托管。**
* **构建工具:** Maven (后端), Vite/Webpack (前端)
* **部署:** Docker, Kubernetes (MVP后)

## 7. 开发路线图 (Development Roadmap)

### 7.1 Phase 1: 核心基础与手动MVP (MVP - 最小可行产品)

* **目标:** 验证核心流程：用户打开网站 -> 注册Java项目 -> 手动触发全量解析（AST为主，针对Java，LLM极简或无） -> 手动生成基础文档 -> 用户可查看生成的、包含基础导航的最新版Java项目文档网站，并能查阅基础的错误码指南。
* **主要功能:**
    * FR-PM-001 (注册Java项目), FR-PM-002 (项目列表核心字段), FR-PM-007 (手动全量触发解析，**仅针对Java项目**)。
    * FR-CODE-001 (拉取Java代码), FR-CODE-002 (**AST为主，针对Java语言，如使用JavaParser**，LLM极简或无), FR-CODE-005 (基础图谱存储，如类和方法节点及简单关系)。
    * FR-DOCGEN-001 (基于AST结果生成Markdown), FR-DOCGEN-002 (基础文档内容：如项目结构概览、包/类列表), FR-DOCGEN-003 (1-2个固定Thymeleaf模板，**针对Java项目结构**), FR-DOCGEN-005 (主要关注最新版本的文档生成与展示)。
    * FR-DOCSITE-001 (Markdown转HTML并生成可在线访问的静态文档网站 - **核心交付物**), FR-DOCSITE-002 (基于解析结果生成基础的文档导航树), FR-DOCSITE-003 (文档版本切换功能 - **MVP核心特性**), FR-DOCSITE-004 (文档内容对比功能 - **MVP核心特性**), FR-DOCSITE-006 (Java代码片段在文档中正确高亮), **FR-DOCSITE-008 (基础的错误码查看页面 - 可以是系统内嵌的静态帮助文档页面)**。 (明确排除：高级搜索功能，复杂图表自动生成)。
    * FR-TASK-001 (后台任务抽象), FR-TASK-002 (RocketMQ基础集成), FR-TASK-004 (提供API供前端轮询任务状态), FR-TASK-005 (极简化的任务失败记录和重试，如RocketMQ自身消费者重试机制)。
    * NFR-DEPLOY-003 (提供Docker Compose配置，方便本地开发和测试环境一键部署)。
    * 核心技术栈搭建 (Java/Spring Boot, Vue/TS, MySQL, Redis, RocketMQ基础集成, 图数据库基础集成)。
    * API规范 (`api-spec.yaml`) MVP部分的实现。
* **明确排除在MVP之外的（部分调整）：**
    * 独立的ArchScope用户注册、登录、密码管理、RBAC功能。
    * 除Java以外的其他编程语言支持。
    * 复杂的LLM集成和深度分析（如设计模式识别、代码异味、LLM安全扫描）。
    * 本地LLM部署选项。
    * 完整的增量解析与上下文维护（MVP可能只做全量或非常简化的增量）。
    * 复杂的C4模型图和其他高级图表自动生成。
    * 文档网站高级搜索功能。
    * **完整的项目健康度评估体系和自定义规则（完全排除在MVP之外）。**
    * Webhook自动触发和定时扫描功能。
    * 完善的统计分析功能。
    * 严格的NFR指标达成（如99.9%可用性），MVP关注核心功能可用和基本稳定。
    * 任务调度的优先级处理、完善的DLQ和管理员干预界面。

### 7.2 Phase 2: 自动化流程与文档站增强

* **目标:** 实现自动化触发和文档站功能增强。
* **主要功能:**
    * FR-CHANGE-001 (Webhook接收), 003 (自动触发增量解析 - 初步)
    * FR-CODE-004 (增量解析逻辑 - 初步，关注上下文关联性)
    * FR-DOCGEN-004 (基础C4图PlantUML文本生成，前端渲染)
    * FR-DOCSITE-005 (基础前端文档搜索 - 如Lunr.js)
    * FR-PM-003 (更丰富的项目详情页)
    * FR-TASK-005, 006 (完善重试和DLQ处理)
    * NFR-DEPLOY-001, 002 (初步K8s部署支持)

### 7.3 Phase 3: 功能增强与用户体验提升

* **目标:** 丰富解析能力，完善文档功能，引入健康度评估。
* **主要功能:**
    * FR-CODE-002 (LLM深度集成：设计模式、代码异味), 006 (扩展支持更多语言)
    * FR-DOCGEN-003 (更多模板，用户自定义模板管理界面 - 初步)
    * FR-HEALTH-001, 002, 003 (基础健康度指标计算与报告展示)
    * FR-TASK-007 (任务管理UI，查看任务状态)
    * NFR-MAINTAIN-002 (提升测试覆盖率)

### 7.4 Phase 4: 成熟化与规模化

* **目标:** 系统功能完善，性能优化，安全性加固，支持更大规模应用。
* **主要功能:**
    * FR-HEALTH-004 (自定义健康度规则), 005 (SAST集成/LLM安全扫描)
    * FR-STATS-001, 002, 003, 004 (统计与分析功能)
    * FR-CODE-007 (LLM Prompt用户管理)
    * 支持本地部署LLM选项。
    * NFR-PERF, NFR-AVAIL, NFR-SCALE 各项指标的持续优化和达标。
    * NFR-SEC (深度安全加固：完善LLM交互安全审计，优化代码脱敏和PII过滤，强化权限控制)。
    * 提供API供外部集成。
    * `settings.html` (项目级详细配置), 系统级`admin_settings.html` 等管理功能完善。

## 8. 附录 (Appendix)

* **API设计草案:** 见 `api_design.md` (初始草案)
* **API详细规范:** 见 `api-spec.yaml` (OpenAPI v3 YAML)
* **LLM Prompts示例与指南:** 见 `prompts/llm_prompts.md` (待创建，包含针对不同分析任务的Prompt设计原则和示例)
* **界面原型:** 见 `docs/prototype/` 目录 (包含新增的 `error_codes_guide.html` 概念)
* **领域模型图:** 见 `docs/architecture/domain-model.png` (待创建，基于 `domain-model.md` 的可视化)
* **用户故事:** 见 `user-story.md`