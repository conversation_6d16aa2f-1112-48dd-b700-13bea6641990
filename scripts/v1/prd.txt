<context>

## Overview

架构鹰眼 (ArchScope) 是一个面向开发团队和架构师的自动化架构观测与守护系统。
它旨在通过深度分析项目的设计文档（未来规划）和代码仓库，为开发者提供项目的实时、全局架构视图，从而帮助开发者快速理解项目结构、有效治理技术复杂度、保障架构一致性。
ArchScope 的核心价值在于提升开发效率、强化架构治理、促进团队协作，并为项目的相关方（如新成员、外部协作者）提供便捷的对接和理解途径，最终降低软件开发的沟通成本和维护风险。
目标用户包括技术团队管理者、开发人员、架构师和新成员。

## Core Features

1. **项目管理功能**:

* **作用**: 注册、管理项目，控制访问。
* **重要性**: 项目接入和管理的基础。
* **实现**: 提供项目列表/详情页，支持多种代码仓库（GitHub/GitLab），实现成员和角色管理（同GitLab权限）。支持多种编程语言（Java, Python, JS等）。

2. **代码仓库解析功能**:

* **作用**: 分析代码，提取架构和依赖。
* **重要性**: 理解项目结构的核心。
* **实现**: 基于混合方法（LLM+提示词解析，结合传统静态分析技术如AST解析），支持多种语言，实现增量解析，并关注增量解析过程中的上下文维护。分析粒度分为多个层次，从提取类/函数/模块结构，到类、函数、模块间的依赖关系，再到设计模式的应用等。

3. **文档生成功能**:

* **作用**: 自动生成项目架构文档。
* **重要性**: 提供项目理解的标准化输出。
* **实现**: 根据解析结果生成Markdown文档，支持版本管理和模板定制。支持生成架构图（如PlantUML C4模型图）。

4. **版本变更感知功能**:

* **作用**: 监控代码变更，触发文档更新。
* **重要性**: 保证文档与代码同步。
* **实现**: 支持Webhook和定时扫描，触发更新任务，提供变更通知。

5. **项目健康度评估功能**:

* **作用**: 评估项目质量。
* **重要性**: 提供项目风险和改进方向。
* **实现**: 定义评估指标（覆盖率、依赖更新频率等），实现星级评定算法，提供报告，支持自定义规则。支持代码安全漏洞扫描（基于LLM或集成静态安全分析工具）。

6. **文档网站生成和托管功能**:

* **作用**: 将文档渲染为可访问的网站。
* **重要性**: 方便用户查阅和分享文档。
* **实现**: Markdown转HTML，自动生成网站，支持版本切换/对比和搜索。

7. **任务管理与调度功能**:

* **作用**: 管理后台任务（解析、生成等）。
* **重要性**: 保证系统后台稳定运行。
* **实现**: 管理文档生成/更新任务，基于优先级调度，提供状态跟踪、重试和错误处理。

8. **统计与分析功能**:

* **作用**: 分析文档使用情况。
* **重要性**: 了解用户关注点，优化文档。
* **实现**: 记录访问日志，提供访问量统计，分析热门文档/项目，生成报表。

## User Experience

* **User Personas**:
  * 技术团队管理者：关注整体架构、健康度、项目进展。
  * 开发人员：关注具体模块实现、依赖关系、API文档。
  * 架构师：关注架构一致性、技术选型、风险点。
  * 新成员：关注快速上手、项目概览、核心流程。
* **Key User Flows**:
  * 注册新项目 -> 配置代码仓库 -> 查看首次生成的文档 -> 查看健康度报告。
  * 浏览项目列表 -> 进入项目详情 -> 浏览/搜索文档 -> 对比不同版本文档。
  * 代码提交 -> Webhook触发 -> 查看更新后的文档和变更通知。
  * 管理员 -> 管理用户和项目权限。
* **UI/UX Considerations**:
  * 界面简洁直观，符合开发者习惯。
  * 文档阅读体验友好，支持代码高亮、搜索、版本切换。
  * 提供清晰的任务状态反馈。
  * 响应式设计，支持移动端访问。
  * 文档页面支持主题定制。

</context>
<PRD>

## Technical Architecture

* **System Components**:
  * **Frontend**: Vue 3 + TypeScript + Tailwind CSS (响应式设计)
  * **Backend**: Java + Spring Boot + MyBatisPlus (DDD架构)
  * **Database**: MySQL 8.0+ (结构化数据), Redis (缓存, 计数)
  * **Parsing Engine**: 混合解析引擎: LLM 集成 (具体模型待定，支持本地部署选项) + 语言特定解析器 (基于Tree-sitter的AST解析器)
  * **Task Queue**: 采用基于 RocketMQ 的消息队列
  * **Documentation Site Generator**: 基于Markdown渲染

* **Data Models**:
  * Project (ID, Name, Repo URL, Type, Members, Config, etc.)
  * DocumentVersion (ID, ProjectID, CommitID, ContentPath, Timestamp)
  * Task (ID, ProjectID, Type, Status, Priority, CreatedAt, UpdatedAt)
  * User (ID, Name, Role, Permissions)
  * HealthMetric (ID, ProjectID, Type, Value, Timestamp)
  * AccessLog (ID, UserID, DocumentID, Timestamp)

* **APIs and Integrations**:
  * RESTful API for Frontend-Backend communication.
  * Git Repository API (GitHub/GitLab) for cloning and fetching changes.
  * LLM Service API for code/doc analysis.
  * Webhook endpoints for receiving Git events.

* **Infrastructure Requirements**:
  * Security: API Protection (Rate limiting, Input validation), Data Encryption (at rest, in transit), SQL Injection/XSS prevention, Security Logging, **LLM交互安全, 敏感数据处理, 支持本地模型选项**.
  * Performance: Page load < 2s, API response < 1s, handle large repos, concurrent access.
  * Availability: 99.9% uptime, fault tolerance, disaster recovery.

## Development Roadmap

* **Phase 1: Core Foundation (MVP)**
  * User Authentication & Basic Project Registration (Manual Git URL input).
  * Backend API framework setup (Java/Spring Boot).
  * Basic Frontend framework setup (Vue/TS).
  * Manual Trigger for Code Parsing (Simplified LLM call for basic structure).
  * Manual Trigger for Basic Markdown Documentation Generation (Structure overview).
  * Basic Document Display Page (No versioning, no site generation).
  * MySQL + Redis setup.
  * Containerized deployment setup (Docker Compose for local dev).
* **Phase 2: Automated Workflow & Basic Docs Site**
  * Git Integration (Clone, Fetch changes).
  * Webhook receiver for Git events.
  * Task Queue implementation for parsing and doc generation.
  * Incremental Parsing logic(需关注上下文关联性).
  * Basic Document Versioning (Store different versions).
  * Simple Static Site Generation for documentation (Render Markdown to HTML).
  * Basic Document Search.
  * Frontend: Project List, Basic Doc View with version selection.
* **Phase 3: Enhanced Features & UX**
  * Project Member Management & Permissions.
  * Advanced Parsing (Deeper dependency analysis, 集成传统AST解析器, LLM fine-tuning/prompt engineering, 初步代码安全分析集成).
  * Documentation Template Customization.
  * Document Comparison Feature.
  * Basic Health Metrics Calculation & Display.
  * Improved Frontend UX (Better navigation, theme customization).
  * Task Management UI (View task status).
  * 架构图生成与展示.
* **Phase 4: Maturity & Scale**
  * Advanced Health Metrics & Reporting.
  * Customizable Health Rules.
  * Access Statistics & Analysis.
  * Kubernetes deployment support.
  * Performance Optimizations for large scale.
  * 深度安全加固: 完善LLM交互安全审计, 优化代码脱敏和PII过滤规则, 强化权限控制.
  * API for external integrations.

## Logical Dependency Chain

1. **Backend/Frontend Setup**: Basic frameworks, API structure.
2. **Project Registration**: Ability to add a project.
3. **Manual Parsing/Doc Gen**: Core logic proof-of-concept.
4. **Basic Doc Display**: See the output. (Initial visible frontend)
5. **Git Integration & Webhooks**: Automate the trigger.
6. **Task Queue**: Handle background processing.
7. **Versioning & Site Gen**: Make docs usable.
8. **Incremental Parsing**: Efficiency improvement.
9. **Health Metrics**: Value-add feature.
10. **User Management**: Collaboration feature.
11. **Advanced Features**: Build upon the core (Templates, Comparison, Stats).
12. **Scalability/Ops**: Prepare for production.

## Risks and Mitigations

* **Technical Challenges**:
  * *Risk*: LLM parsing accuracy and consistency across languages/complex codebases.
  * *Mitigation*: Start with simpler analysis, use targeted prompts, potentially combine with traditional AST parsing for specific languages, allow manual overrides/corrections. Extensive testing with diverse projects. 探索使用RAG (检索增强生成) 提升特定领域代码理解能力, 加强对增量解析中上下文丢失问题的处理.
  * *Risk*: Handling very large code repositories efficiently (parsing time, memory usage).
  * *Mitigation*: Implement incremental parsing early. Optimize parsing algorithms. Use efficient data structures. Explore distributed task processing if needed. 优化代码分块策略以维持上下文连贯性.
* **Figuring out the MVP**:
  * *Risk*: MVP scope creep, trying to include too much initially.
  * *Mitigation*: Strictly follow the defined Phase 1 roadmap. Focus on the core workflow (Register -> Parse -> Generate -> View) even if manually triggered initially. Get user feedback early on the core value.
* **Resource Constraints**:
  * *Risk*: Development time/cost underestimated, especially LLM integration/tuning.
  * *Mitigation*: Phased approach allows for delivering value incrementally. Prioritize core features. Be flexible with LLM choices (consider cost/performance trade-offs). Clearly define "good enough" for each phase.
* **Dependency on External Services**:
  * *Risk*: LLM API changes, costs, or availability issues. Git provider API limits.
  * *Mitigation*: Abstract external service interactions. Monitor usage and costs. Have fallback strategies (e.g., simpler parsing if LLM fails). Implement rate limiting and caching for external API calls. 提供并推广本地部署LLM模型选项，作为隐私保护和外部服务中断的备选方案, 向用户清晰透明地展示数据处理流程和隐私政策.

## Appendix

* API设计规范见 [api_design.md](/docs/api_design.md)
* LLM Prompts见 [llm_prompt.md](/prompts/llm_prompt.md)

</PRD>