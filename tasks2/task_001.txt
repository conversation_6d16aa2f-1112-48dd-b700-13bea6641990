# Task ID: 1
# Title: Setup Backend Project and SSO Integration (MVP)
# Status: done
# Dependencies: None
# Priority: high
# Description: Initialize the Spring Boot 3.x backend project with Java 17+, Maven, and integrate with the enterprise SSO framework (OIDC/SAML) for user authentication and basic role mapping (FR-AUTH-001, FR-AUTH-002, FR-AUTH-003, FR-AUTH-005). Implement JWT/session management and logout (FR-AUTH-004).
# Details:
Create Spring Boot project structure (DDD layers). Add Spring Security, SAML/OIDC client dependencies. Configure SSO provider details. Implement UserDetailsService or equivalent to map SSO user attributes/roles (e.g., ARCHSCOPE_ADMINS to ADMIN). Setup MySQL for basic user/role info if needed to supplement SSO. Pseudo-code: SecurityConfig with .saml2Login() or .oidcLogin(), CustomUserDetailsService for role mapping.

# Test Strategy:
Unit test SSO attribute mapping and role conversion. Integration test SSO login flow with a mock/test SSO provider. Verify authentication, session creation, role-based access to a dummy secured endpoint, and logout.
