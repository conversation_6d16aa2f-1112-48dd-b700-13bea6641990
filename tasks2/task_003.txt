# Task ID: 3
# Title: Implement Project Registration API and UI (MVP for Java Projects)
# Status: pending
# Dependencies: 1, 2
# Priority: high
# Description: Develop the backend REST API endpoint (POST /api/projects) to register a new Java project (name, Git URL, default branch) and the corresponding frontend form (register_project.html equivalent). Store project metadata in MySQL (FR-PM-001).
# Details:
Backend: Define Project entity, ProjectRepository (MyBatisPlus), ProjectService, ProjectController. Validate input. Secure endpoint based on SSO roles (ARCHSCOPE_PROJECT_CREATORS). Frontend: Create RegisterProject.vue with form, API call logic, user feedback. MySQL: Define `projects` table. Pseudo-code (Controller): `@PostMapping public ResponseEntity<ProjectDTO> registerProject(@Valid @RequestBody ProjectCreateDTO createDTO) { /* ... */ }`

# Test Strategy:
Backend: Unit test service logic, controller validation. Integration test API endpoint (valid/invalid data, auth). Frontend: Unit test form validation. E2E test project registration. Verify data persistence in MySQL.

# Subtasks:
## 1. Develop Backend API for Project Registration [pending]
### Dependencies: None
### Description: Implement all backend components for project registration, including entity, repository, service, controller, DTOs, validation, and security for Java projects.
### Details:
1. Backend: Define Project entity (name, gitUrl, defaultBranch, etc.) and corresponding MySQL `projects` table schema. 2. Backend: Implement ProjectRepository (e.g., using Spring Data JPA/MyBatisPlus). 3. Backend: Implement ProjectService with business logic for project registration and input validation. 4. Backend: Implement ProjectController with a POST /api/projects endpoint, DTOs for request/response, and secure it based on SSO roles (ARCHSCOPE_PROJECT_CREATORS).

## 2. Develop Frontend UI for Project Registration [pending]
### Dependencies: 3.1
### Description: Create the Vue.js user interface for project registration, enabling users to input project details and submit them to the backend API.
### Details:
5. Frontend: Create RegisterProject.vue component with a form for project name, Git URL, default branch, client-side validation, and API call logic to the backend, including user feedback.

## 3. Implement and Execute Tests for Project Registration [pending]
### Dependencies: 3.1, 3.2
### Description: Write and run comprehensive tests (unit, integration, E2E) for both backend and frontend components of the project registration feature.
### Details:
6. Testing: Write backend unit/integration tests for service/controller and frontend/E2E tests for the registration flow.

