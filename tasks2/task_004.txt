# Task ID: 4
# Title: Implement Project Listing API and UI (MVP)
# Status: pending
# Dependencies: 1, 2, 3
# Priority: high
# Description: Develop the backend API (GET /api/projects) to list projects accessible to the user (based on SSO permissions) and the frontend page (project_list.html equivalent) to display them with basic info (name, ID, last analysis time - placeholder for <PERSON>) (FR-PM-002).
# Details:
Backend: ProjectController GET /api/projects endpoint. ProjectService method to fetch projects, filtering by user's access rights from SSO. Return List<ProjectSummaryDTO>. Frontend: Create ProjectList.vue, fetch project list, display in table/cards, link to project detail (future). Pseudo-code (Service): `public List<ProjectSummaryDTO> getUserProjects(UserDetails currentUser) { /* Logic to determine accessible projects */ }`

# Test Strategy:
Backend: Unit test service logic for fetching projects. Integration test API endpoint with different user roles. Frontend: Manually verify project list display. E2E test navigation to project list.
