# Task ID: 5
# Title: Implement Background Task Management with RocketMQ (MVP Setup)
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Integrate Apache RocketMQ for managing asynchronous tasks. Implement API for manually triggering a full project analysis (FR-PM-007) and an API for frontend to poll task status (FR-TASK-004). This task sets up the queue and basic task submission/status tracking.
# Details:
Backend: Add RocketMQ client dependencies, configure connection (FR-TASK-002). Define message DTOs (e.g., AnalysisTaskPayload). Implement RocketMQ producer. ProjectController endpoint POST /api/projects/{projectId}/analyze to send task. Store task status (pending, running, success, failed) in MySQL. TaskController endpoint GET /api/tasks/{taskId} for status. Basic retry via RocketMQ consumer (FR-TASK-005 simplified). This task focuses on setting up RocketMQ, task submission, and status DB/API. The consumer logic that calls parsing is part of Task 6.

# Test Strategy:
Unit test message production. Integration test task submission via API and status updates in DB. Verify task status polling API. Test basic RocketMQ connectivity.

# Subtasks:
## 1. Setup RocketMQ, Define Data Structures, and Implement Producer [pending]
### Dependencies: None
### Description: Add RocketMQ client dependencies, configure connection properties (nameserver address, producer group for FR-TASK-002). Define message DTOs (e.g., AnalysisTaskPayload for FR-PM-007) and a DB entity/table for storing task status (ID, project ID, status, timestamps). Implement a RocketMQ producer service to send analysis task messages.
### Details:
Combines initial RocketMQ setup (original point 1), data model definition for messages and task status (original point 2), and the core message publishing logic (original point 3). This forms the foundation for background task processing.

## 2. Implement API Endpoints for Task Submission and Status Polling [pending]
### Dependencies: 5.1
### Description: Implement a ProjectController endpoint (e.g., POST /api/projects/{projectId}/analyze) to create a task entry in the DB and send a message to RocketMQ using the producer service. Implement a TaskController endpoint (e.g., GET /api/tasks/{taskId}) for the frontend to poll task status from the DB (FR-TASK-004).
### Details:
Focuses on creating the backend API interfaces. The task submission API (original point 4) will utilize the producer and DB setup from subtask 1. The status polling API (original point 5) will allow clients to check task progress.

## 3. Develop Unit and Integration Tests for Background Task System [pending]
### Dependencies: 5.2
### Description: Create unit and integration tests for message production, the task submission API, task status updates in the DB, and the task status polling API.
### Details:
Ensures the reliability and correctness of the implemented background task management features (original point 6). Tests will cover the functionality developed in subtasks 1 and 2, including message flow, API behavior, and database interactions.

## 4. Implement RocketMQ Consumer for Task Processing [pending]
### Dependencies: None
### Description: Develop the consumer component that listens to the RocketMQ topic, retrieves tasks, executes the defined background logic, and updates task status.
### Details:
- Configure RocketMQ consumer group and topic subscription.
- Implement message deserialization logic based on defined data structures.
- Develop the core task execution logic (can be a simple placeholder for MVP).
- Implement status update mechanism (e.g., writing to a database or cache) to reflect task progress (pending, processing, completed, failed).

## 5. Implement Error Handling and Retry Mechanisms for Consumers [pending]
### Dependencies: 5.4
### Description: Enhance the RocketMQ consumer with robust error handling for task processing failures, including configurable retry strategies and a dead-letter queue approach.
### Details:
- Identify common failure scenarios during task processing (e.g., external service unavailability, data validation errors, unexpected exceptions).
- Implement try-catch blocks around task execution and specific exception handling.
- Configure RocketMQ's built-in retry capabilities or implement custom retry logic (e.g., exponential backoff).
- Design and implement a dead-letter queue (DLQ) strategy for tasks that fail repeatedly after retries.

## 6. Integrate Basic Logging and Monitoring for Task Lifecycle [pending]
### Dependencies: 5.4
### Description: Implement comprehensive logging for key events in the task lifecycle (submission, consumption, success, failure) and set up basic monitoring for queue health and consumer performance.
### Details:
- Integrate a logging framework (e.g., SLF4J with Logback/Log4j2).
- Add structured logs for: task submission (API), message production, message consumption, task state transitions (processing, success, failure), and errors encountered.
- Identify key metrics for monitoring: queue size/depth, number of messages in flight, consumer lag, task processing throughput, error rate.
- Set up basic dashboards or alerts using RocketMQ console or other simple monitoring tools for these metrics.

