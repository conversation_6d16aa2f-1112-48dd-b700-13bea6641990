# Task ID: 6
# Title: Implement Git Cloning and Basic Java AST Parsing (MVP)
# Status: pending
# Dependencies: 1, 5
# Priority: high
# Description: Implement backend logic to clone a specified Java project's Git repository (FR-CODE-001) using token-based auth. Perform basic AST parsing using JavaParser (FR-CODE-002 for Java) to extract classes/methods. Store basic graph data (nodes: Package, Class, Method; relationships: CONTAINS, DEFINES) in Neo4j (FR-CODE-005). This will be executed as a background task managed by RocketMQ.
# Details:
Backend: Service method using JGit for cloning. Securely handle Git tokens. Integrate JavaParser for AST analysis. Extract package, class, method info. Integrate Neo4j client and define basic schema. Service to persist parsed data to Neo4j. Implement RocketMQ consumer for 'analysis-tasks' topic that invokes this parsing logic. Pseudo-code (AST Parsing): `CompilationUnit cu = StaticJavaParser.parse(file); cu.findAll(ClassOrInterfaceDeclaration.class).forEach(c -> { /* store class */ c.getMethods().forEach(m -> { /* store method */ }); });`

# Test Strategy:
Unit test Git cloning, JavaParser logic with sample files, Neo4j persistence. Integration test RocketMQ consumer triggering clone -> parse -> store in Neo4j flow.

# Subtasks:
## 1. Implement Git Cloning Service (JGit) [pending]
### Dependencies: None
### Description: Backend: Implement a Git cloning service using JGit to clone a specified Java project's Git repository (FR-CODE-001), including handling of Git URLs, branches, and secure token-based authentication.
### Details:
Utilize JGit for cloning. Handle Git URLs, branches, and token-based authentication. Corresponds to FR-CODE-001.

## 2. Implement Java AST Parsing (JavaParser) [pending]
### Dependencies: 6.1
### Description: Backend: Integrate JavaParser (FR-CODE-002) and implement logic to traverse Java source files, performing AST parsing to extract class and method declarations.
### Details:
Integrate JavaParser for AST parsing. Extract class and method declarations from Java source files. Corresponds to FR-CODE-002.

## 3. Define Basic Neo4j Graph Schema [pending]
### Dependencies: None
### Description: Backend: Define a basic Neo4j graph schema (Nodes: Package, Class, Method; Relationships: CONTAINS, DEFINES for FR-CODE-005).
### Details:
Define Neo4j schema: Nodes (Package, Class, Method) and Relationships (CONTAINS, DEFINES). Corresponds to FR-CODE-005.

## 4. Implement Neo4j Persistence Service [pending]
### Dependencies: 6.2, 6.3
### Description: Backend: Implement a Neo4j persistence service to connect to Neo4j and store the extracted package, class, and method information as nodes and relationships.
### Details:
Develop service to connect to Neo4j and persist extracted AST data (packages, classes, methods) based on the defined schema.

## 5. Implement RocketMQ Consumer for 'analysis-tasks' [pending]
### Dependencies: None
### Description: Backend: Implement a RocketMQ consumer for the 'analysis-tasks' topic.
### Details:
Set up a RocketMQ consumer to listen to the 'analysis-tasks' topic for incoming analysis requests.

## 6. Orchestrate Analysis Flow in RocketMQ Consumer [pending]
### Dependencies: 6.1, 6.2, 6.4, 6.5
### Description: Backend: Orchestrate the Git cloning, AST parsing, and Neo4j data persistence logic within the RocketMQ consumer.
### Details:
Integrate and manage the sequence of Git cloning, AST parsing, and Neo4j persistence within the RocketMQ consumer's message handling logic.

## 7. Develop Unit and Integration Tests [pending]
### Dependencies: 6.6
### Description: Testing: Unit tests for Git cloning, JavaParser logic with sample files, Neo4j persistence, and integration test for the RocketMQ consumer end-to-end flow.
### Details:
Create unit tests for Git cloning, JavaParser, Neo4j persistence. Implement an integration test for the complete RocketMQ consumer processing pipeline.

## 8. Refine AST Parser for Detailed Method Information [pending]
### Dependencies: None
### Description: Enhance the Java AST parser (building on existing subtask 2) to extract comprehensive details about method declarations (name, parameters, return type, modifiers, annotations) and method calls (target, arguments, type of call).
### Details:
Focus on accurately resolving method signatures, handling overloaded methods, and capturing call sites. Consider common Java language features and libraries. Ensure parsing of Javadoc comments for methods if feasible.

## 9. Extend Neo4j Schema for Method-Level Code Representation [pending]
### Dependencies: 6.8
### Description: Expand the basic Neo4j graph schema (from existing subtask 3) to model detailed method-level constructs and their relationships, including method signatures, calls, parameters, return types, and annotations.
### Details:
Define specific node labels (e.g., `Method`, `MethodParameter`, `MethodCallSite`, `Annotation`) and relationship types (e.g., `DECLARES_METHOD`, `CALLS_METHOD`, `HAS_PARAMETER`, `RETURNS_TYPE`, `IS_ANNOTATED_BY`, `OVERRIDES`). Document schema changes and rationale.

## 10. Implement Neo4j Persistence for Granular Method Data [pending]
### Dependencies: 6.9
### Description: Update the Neo4j persistence service (from existing subtask 4) to map and store the detailed method information (declarations, calls, parameters, annotations) extracted by the refined AST parser into the extended graph schema.
### Details:
Ensure robust transaction management for creating/updating method-related nodes and their relationships. Optimize Cypher queries for performance, especially for batch insertions. Handle potential data conflicts or updates for re-analyzed code.

## 11. Develop Integration Tests for Method Analysis Flow [pending]
### Dependencies: 6.10
### Description: Create comprehensive integration tests (extending existing subtask 7) to validate the end-to-end flow: Java code parsing for methods, AST extraction of detailed method information, transformation, and accurate persistence of method details and relationships in Neo4j.
### Details:
Test cases should cover various scenarios: simple methods, methods with multiple parameters, overloaded methods, generic methods, method calls between different classes, lambda expressions, and recursive calls. Verify graph structure and properties post-persistence against expected outcomes.

## 12. Implement Centralized Configuration for Analysis Services [pending]
### Dependencies: 6.10
### Description: Establish a centralized configuration mechanism (e.g., using externalized properties/YAML files, environment variables, or a config server for more advanced setups) for all components of the analysis pipeline (Git, AST, Neo4j, MQ, Orchestration).
### Details:
Externalize settings like API keys, database credentials, queue names, processing batch sizes, AST parser options (e.g., Java language level), and logging levels. Ensure configurations can be easily managed per environment (dev, staging, prod).

## 13. Setup Foundational Logging and Monitoring for Pipeline Health [pending]
### Dependencies: 6.11, 6.12
### Description: Implement structured, application-level logging across all services and set up basic monitoring dashboards or alerts for key operational metrics of the analysis pipeline, focusing on the newly implemented method analysis capabilities.
### Details:
Integrate a standard logging framework (e.g., SLF4J with Logback/Log4j2) with consistent log formats. Define and track metrics such as processing time per method, number of methods parsed, graph update latency for method data, MQ message throughput for analysis tasks, and error rates during method parsing/persistence. Consider tools like Prometheus/Grafana for MVP monitoring or simple health check endpoints.

