# Task ID: 7
# Title: Implement Basic Markdown Document Generation for Java Projects (MVP)
# Status: pending
# Dependencies: 1, 6
# Priority: medium
# Description: Generate basic Markdown documents (project overview, module/class list) from AST parsing results in Neo4j (FR-DOCGEN-001, FR-DOCGEN-002). Use 1-2 fixed Thymeleaf templates for Java projects (FR-DOCGEN-003). Focus on latest analysis version (FR-DOCGEN-005).
# Details:
Backend: Service to query Neo4j for project structure. Integrate Thymeleaf for Markdown generation. Create Thymeleaf templates (e.g., project-overview.md.html, package-detail.md.html). Service method to process Neo4j data with Thymeleaf, producing Markdown. Store generated Markdown (DB/filesystem) associated with project analysis. This is part of the background analysis task. Pseudo-code (Thymeleaf): `# Project Overview: [[${projectName}]] ## Packages <th:block th:each="pkg : ${packages}">* [[${pkg.name}]]</th:block>`

# Test Strategy:
Unit test Neo4j querying, Thymeleaf template processing with mock data (verify Markdown output). Integration test: trigger analysis, verify Markdown generation based on parsed data.
