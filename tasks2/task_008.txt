# Task ID: 8
# Title: Implement Document Website Generation & Serving (MVP)
# Status: pending
# Dependencies: 1, 2, 7
# Priority: high
# Description: Convert generated Markdown into a static HTML website with basic navigation (left-side tree from DocTreeNodeDTO structure) and Java code highlighting (FR-DOCSITE-001, FR-DOCSITE-002, FR-DOCSITE-006). Serve HTML via API or static files.
# Details:
Backend: Service using commonmark-java to convert Markdown to HTML. Render HTML using Thymeleaf for site structure (header, nav, content). Generate navigation tree (DocTreeNodeDTO). API endpoint(s) like GET /api/projects/{projectId}/docs/{path} to serve HTML. Frontend (Vue): ProjectDocumentationView.vue to fetch nav tree and HTML, render using v-html. Ensure Prism.js/Highlight.js for syntax highlighting. Alternatively, generate static HTML files served by Nginx.

# Test Strategy:
Unit test Markdown-to-HTML conversion, navigation tree generation. Integration test API endpoints serving document HTML. Frontend: Manually verify document website rendering, navigation, code highlighting.

# Subtasks:
## 1. Backend Development for Document Processing and API [pending]
### Dependencies: None
### Description: Implement all backend functionalities including Markdown to HTML conversion, navigation tree generation, and API endpoints for serving document content and navigation.
### Details:
Implement a service using commonmark-java for Markdown to HTML conversion. Design and implement logic to generate a navigation tree structure (e.g., List<DocTreeNodeDTO>, FR-DOCSITE-002). Develop API endpoints (e.g., GET /api/projects/{projectId}/docs/nav for navigation, GET /api/projects/{projectId}/docs/{path} for HTML content). Consider Thymeleaf for the overall site shell if serving HTML directly.

## 2. Frontend Implementation for Document Website [pending]
### Dependencies: 8.1
### Description: Develop the Vue.js frontend to consume backend APIs, display the navigation tree, render document content, and integrate syntax highlighting.
### Details:
Create a ProjectDocumentationView.vue component to fetch the navigation tree and HTML content. Render the fetched navigation tree and display HTML content using `v-html`. Integrate a client-side syntax highlighting library like Prism.js or Highlight.js for Java code blocks (FR-DOCSITE-001, FR-DOCSITE-006).

## 3. Comprehensive Testing and MVP Validation [pending]
### Dependencies: 8.1, 8.2
### Description: Conduct thorough unit and E2E testing of the document website, ensuring all features like rendering, navigation, and code highlighting meet MVP requirements.
### Details:
Perform unit tests for Markdown-to-HTML conversion and navigation tree generation. Conduct manual/E2E tests for website rendering, navigation, and code highlighting, ensuring compliance with FR-DOCSITE-001, FR-DOCSITE-002, and FR-DOCSITE-006.

